/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 3.0.12
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package it.robur.test.physicalDCC.t32api;

public final class etFunctionalState {
  public final static etFunctionalState fsOff = new etFunctionalState("fsOff");
  public final static etFunctionalState fsOffAndDelay = new etFunctionalState("fsOffAndDelay");
  public final static etFunctionalState fsDelay = new etFunctionalState("fsDelay");
  public final static etFunctionalState fsOnlyCircOn = new etFunctionalState("fsOnlyCircOn");
  public final static etFunctionalState fsRunning = new etFunctionalState("fsRunning");
  public final static etFunctionalState fsRecovery = new etFunctionalState("fsRecovery");

  public final int swigValue() {
    return swigValue;
  }

  public String toString() {
    return swigName;
  }

  public static etFunctionalState swigToEnum(int swigValue) {
    if (swigValue < swigValues.length && swigValue >= 0 && swigValues[swigValue].swigValue == swigValue)
      return swigValues[swigValue];
    for (int i = 0; i < swigValues.length; i++)
      if (swigValues[i].swigValue == swigValue)
        return swigValues[i];
    throw new IllegalArgumentException("No enum " + etFunctionalState.class + " with value " + swigValue);
  }

  private etFunctionalState(String swigName) {
    this.swigName = swigName;
    this.swigValue = swigNext++;
  }

  private etFunctionalState(String swigName, int swigValue) {
    this.swigName = swigName;
    this.swigValue = swigValue;
    swigNext = swigValue+1;
  }

  private etFunctionalState(String swigName, etFunctionalState swigEnum) {
    this.swigName = swigName;
    this.swigValue = swigEnum.swigValue;
    swigNext = this.swigValue+1;
  }

  private static etFunctionalState[] swigValues = { fsOff, fsOffAndDelay, fsDelay, fsOnlyCircOn, fsRunning, fsRecovery };
  private static int swigNext = 0;
  private final int swigValue;
  private final String swigName;
}

