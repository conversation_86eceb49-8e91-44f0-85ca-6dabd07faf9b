When you execute a suite page, !-FitNesse-! tests all the subpages
unless the Prune attribute on the Properties page is set.
This example shows just one test subpage of two subpages being executed.

----

Create a Suite page

|script|Page Builder|
|line|${SUT_PATH}|
|page|!-SuitePage-!|

Create two sub pages: 1 is tested, 2 is pruned/ignored

|script|Page Builder|
|line|!-|!-fitnesse.testutil.PassFixture-!-!!-|-!|
|page|!-SuitePage.TestPageOne-!|

|script|Page Builder|
|line|!-|!-fitnesse.testutil.PassFixture-!-!!-|-!|
|attributes|Prune=true|
|page|!-SuitePage.TestPageTwo-!|

Now run the suite page.

|Response Requester.|
|uri   |valid?|
|!-SuitePage?responder=suite-!|true|

|Response Examiner.|
|contents?|
||

The suite should report only the one TestPage and should show no errors.

|Response Examiner.|
|type  |pattern|matches?|
|contents|!-TestPageOne-!|true|
|contents|!-TestPageTwo-!|false|
|contents|Test Pages:.*1 right|true|

The error log page should not have any errors

|Response Requester.|
|uri   |valid?|
|!-SuitePage?executionLog-!|true|

|Response Examiner.|
|contents?|
||

|Response Examiner.|
|type  |pattern|matches?|
|contents|Exit code.*0.*Time|true|
