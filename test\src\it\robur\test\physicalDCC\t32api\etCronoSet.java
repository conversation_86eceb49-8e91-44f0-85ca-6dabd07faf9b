/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 3.0.12
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package it.robur.test.physicalDCC.t32api;

public final class etCronoSet {
  public final static etCronoSet csOff = new etCronoSet("csOff");
  public final static etCronoSet csTemp1 = new etCronoSet("csTemp1");
  public final static etCronoSet csTemp2 = new etCronoSet("csTemp2");
  public final static etCronoSet csTemp3 = new etCronoSet("csTemp3");

  public final int swigValue() {
    return swigValue;
  }

  public String toString() {
    return swigName;
  }

  public static etCronoSet swigToEnum(int swigValue) {
    if (swigValue < swigValues.length && swigValue >= 0 && swigValues[swigValue].swigValue == swigValue)
      return swigValues[swigValue];
    for (int i = 0; i < swigValues.length; i++)
      if (swigValues[i].swigValue == swigValue)
        return swigValues[i];
    throw new IllegalArgumentException("No enum " + etCronoSet.class + " with value " + swigValue);
  }

  private etCronoSet(String swigName) {
    this.swigName = swigName;
    this.swigValue = swigNext++;
  }

  private etCronoSet(String swigName, int swigValue) {
    this.swigName = swigName;
    this.swigValue = swigValue;
    swigNext = swigValue+1;
  }

  private etCronoSet(String swigName, etCronoSet swigEnum) {
    this.swigName = swigName;
    this.swigValue = swigEnum.swigValue;
    swigNext = this.swigValue+1;
  }

  private static etCronoSet[] swigValues = { csOff, csTemp1, csTemp2, csTemp3 };
  private static int swigNext = 0;
  private final int swigValue;
  private final String swigName;
}

