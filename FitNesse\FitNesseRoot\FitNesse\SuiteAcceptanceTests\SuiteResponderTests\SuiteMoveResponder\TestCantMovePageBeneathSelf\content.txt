!3 We should ''not'' be able to move a page below one of it's children.

First build a page, a child page to move, and a grandchild page to act as the target of the move

|Page creator.|
|Page name.                               |Page contents.   |valid?|
|!-ParentPage-!                         |x              |true   |
|!-ParentPage.ChildPage-!               |child page     |true   |
|!-ParentPage.ChildPage.GrandchildPage-!|grandchild page| true  |

Then try to move the child page below the grandhild page.

|Response Requester.|
|uri   |status?|
|!-ParentPage.ChildPage?responder=movePage&newLocation=ParentPage.ChildPage.GrandchildPage-!|400|

Make sure we get an error message.

|Response Examiner.|
|type  |pattern|matches?|wrapped html?|
|contents|Cannot move|true||

Make sure the page didn't really move.

|Response Requester.|
|uri|valid?|contents?|
|!-ParentPage.ChildPage-!|true||

|Response Examiner.|
|type  |pattern|matches?|wrapped html?|
|contents|child page|true||

Make sure that the grandchild page still exists

|Response Requester.|
|uri|valid?|contents?|
|!-ParentPage.ChildPage.GrandchildPage-!|true||

|Response Examiner.|
|type  |pattern|matches?|wrapped html?|
|contents|grandchild page|true||

