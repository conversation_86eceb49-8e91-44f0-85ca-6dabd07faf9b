package it.robur.test.physicalDCCAndRoburberry;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.CoreMatchers.is;

import java.io.IOException;

import org.json.JSONArray;
import org.json.JSONObject;
import org.json.JSONTokener;
import org.junit.After;
import org.junit.AfterClass;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Ignore;
import org.junit.Test;

import static org.junit.Assert.assertThat;
import static uk.co.datumedge.hamcrest.json.SameJSONAs.sameJSONAs;
import static uk.co.datumedge.hamcrest.json.SameJSONAs.sameJSONObjectAs;

import it.robur.interfaces.Op;
import it.robur.modbus.ModbusDDC;
import it.robur.test.physicalDCC.modbus.cT32Api;
import it.robur.test.physicalDCC.t32api.cDDCConf;
import it.robur.test.physicalDCC.t32api.cDDCModbus;
import it.robur.test.physicalDCC.t32api.etGroupType;
import it.robur.test.physicalDCC.t32api.etPlant;
import it.robur.test.physicalDCCAndRoburberry.Settings;
import it.robur.test.physicalDCCAndRoburberry.mocks.MockSocketRberryComm;
import it.robur.test.multiplatform.util.JSONTestUtil;

@Ignore
public class GetSetConfigTest 
{

	private static MockSocketRberryComm comm;
	private static JSONObject identification;
	
	private static JSONObject responseOK = new JSONObject( new JSONTokener("{\"action\":\"response\",\"payload\":{\"_id\":\"0\",\"res\":0}}"));
	
	// Register addresses
	public static final int DI_COMMAND_START_STOP_COOLING_IS_WRITABLE = 0;	// used also for 2 pipes cool/heat	 
	public static final int DI_COMMAND_START_STOP_HEATING_IS_WRITABLE = 1;		 
	public static final int DI_COMMAND_START_STOP_DHW_BASE_IS_WRITABLE = 18;		 
	public static final int DI_COMMAND_START_STOP_DHW_SEPARABLE_IS_WRITABLE = 20;
	
	@BeforeClass
	public static void setupTest() throws IOException
	{	
		System.loadLibrary("libt32api"); // ignored the second time the same lib is loaded 
		
    	cT32Api.Init();
    	cT32Api.ResetGoDecisionThread();
    	
		// Modbus
    	cDDCConf.ZeroConf();
    	cDDCConf.EnableModbusProtocol2_0(Settings.rs232FromRoburberry);
    	
    	cDDCConf.ZeroConf();
    	cDDCConf.EnableModbusProtocol2_0(Settings.rs232FromRoburberry);
    	
    	cDDCConf.LoadLocalConf("BothHeatGroupsAndChil.lcn");
    	cDDCConf.SetHeatGroupType(false, etGroupType.gtBothContemporary);
    	cDDCConf.SetHeatGroupType(true, etGroupType.gtBothNotContemporary);
    	
    	cDDCConf.EnableModbusRAControl(etPlant.chil, true);
    	cDDCConf.EnableModbusRAControl(etPlant.heat, true);
    	
    	cT32Api.Go();
    	
    	comm = new MockSocketRberryComm(Settings.roburberryIP);
		comm.waitForConnection();
		comm.sendStringFile("{\"action\":\"doreboot\"}");
		comm.disconnect();

	}
	
	@AfterClass
	public static void teardownTest() throws IOException
	{	
		if( comm != null )
			comm.disconnect();
		
		cT32Api.BreakOnDecisionThread();
		cT32Api.Exit();
	}
	
	@Before
	public void setup() throws IOException
	{	
		comm = new MockSocketRberryComm(Settings.roburberryIP);
		
		comm.waitForConnection();
		
		identification = comm.waitForIdentification();
		
	}	
	
	@After
	public void teardown() throws IOException
	{		
		comm.disconnect();
	}
		
    @Test
    public void testGetWholeConfig() throws IOException
    {
    	comm.sendJSONFile(JSONTestUtil.loadJSONWithNullDontCaresFromConfserver("controlGetTargetConfig"));
    	
    	JSONObject response = comm.waitForResponse();
    	
        assertThat(
        		JSONTestUtil.nullifyDontCares(response),
        		sameJSONObjectAs(JSONTestUtil.loadJSONWithNullDontCaresFromConfserver("BothHeatGroupsAndChil_ddc_config_as_get_response")));    	
    	
    }
    
    @Test
    public void testSetWholeConfig() throws IOException
    {
    	final short[] out = new short[1];

    	cT32Api.doWhileStopped(new Op()
    	{
			@Override
			public void op() throws IOException 
			{				
		    	cDDCModbus.GetModbusCoil( ModbusDDC.SuperService.COIL_RA_COMMAND_COOL_ON , out);
		    	assertThat(out[0], equalTo((short)0));
		    	
		    	cDDCModbus.GetModbusCoil( ModbusDDC.SuperService.COIL_RA_COMMAND_HEAT_ON , out);
		    	assertThat(out[0], equalTo((short)0));
								    	
			}					
		});
    	
    	
    	// turn heat on

    	final JSONObject bothHeatGroupsAndChil_ddc_config_as_set_control = JSONTestUtil.loadJSONWithNullDontCaresFromConfserver("BothHeatGroupsAndChil_ddc_config_as_set_control");
    	
    	JSONArray superServices = bothHeatGroupsAndChil_ddc_config_as_set_control
    			.getJSONObject("payload")
    			.getJSONObject("val")
    			.getJSONArray("configuration_samples").getJSONObject(0)
    			.getJSONArray("super_services");
		superServices.getJSONObject(ModbusDDC.SuperService.Type.SPACE_COOLING.ordinal()).put("on_off_switch", "false");
		superServices.getJSONObject(ModbusDDC.SuperService.Type.SPACE_HEATING.ordinal()).put("on_off_switch", "true");
    	
    	comm.sendJSONFile(bothHeatGroupsAndChil_ddc_config_as_set_control);
    	
    	JSONObject response = comm.waitForResponse();
    	
        assertThat(response, sameJSONObjectAs(responseOK));
        
    	cT32Api.doWhileStopped(new Op()
    	{
			@Override
			public void op() throws IOException 
			{				
		    	cDDCModbus.GetModbusCoil( ModbusDDC.SuperService.COIL_RA_COMMAND_COOL_ON , out);
		    	assertThat(out[0], equalTo((short)0));
		    	
		    	cDDCModbus.GetModbusCoil( ModbusDDC.SuperService.COIL_RA_COMMAND_HEAT_ON , out);
		    	assertThat(out[0], equalTo((short)1));
								    	
			}					
		});
    	
    	
    	// turn off heat, turn on cool
    	   	
    	superServices.getJSONObject(ModbusDDC.SuperService.Type.SPACE_COOLING.ordinal()).put("on_off_switch", "true");
    	superServices.getJSONObject(ModbusDDC.SuperService.Type.SPACE_HEATING.ordinal()).put("on_off_switch", "false");
    	
    	comm.sendJSONFile(bothHeatGroupsAndChil_ddc_config_as_set_control);
    	
    	response = comm.waitForResponse();
    	
        assertThat(response, sameJSONObjectAs(responseOK));
        
    	cT32Api.doWhileStopped(new Op()
    	{
			@Override
			public void op() throws IOException 
			{				
		    	cDDCModbus.GetModbusCoil( ModbusDDC.SuperService.COIL_RA_COMMAND_COOL_ON , out);
		    	assertThat(out[0], equalTo((short)1));
		    	
		    	cDDCModbus.GetModbusCoil( ModbusDDC.SuperService.COIL_RA_COMMAND_HEAT_ON , out);
		    	assertThat(out[0], equalTo((short)0));
								    	
			}					
		});
    	
    	
    	// reset conf
    	
    	comm.sendJSONFile(JSONTestUtil.loadJSONWithNullDontCaresFromConfserver("BothHeatGroupsAndChil_ddc_config_as_set_control"));
    	response = comm.waitForResponse();
    	assertThat(response, sameJSONObjectAs(responseOK));
    	
    }

    @Test
    public void testGetWholeIdentification() throws IOException
    {
    	comm.sendJSONFile(JSONTestUtil.loadJSONWithNullDontCaresFromConfserver("controlGetTargetIdentification"));
    	
    	JSONObject response = comm.waitForResponse();
    	
        assertThat(
        		JSONTestUtil.nullifyDontCares(response),
        		sameJSONObjectAs(JSONTestUtil.loadJSONWithNullDontCaresFromConfserver("BothHeatGroupsAndChil_ddc_identif_as_get_response")));    	
    	
    }



}
