!3 When a page is renamed, any  alias links to that page from sibling pages are changed.

First build pages where one refers to the other.

|Page creator.|
|Page name.|Page contents.|valid?|
|!-SourcePage-!|!-refer to [[link][TargetPage]]-!|true|
|!-TargetPage-!|whatever|true|

Then rename the target page.

|Response Requester.|
|uri   |status?|
|!-TargetPage?responder=renamePage&newName=NewTarget&refactorReferences=on-!||

Next fetch the Source page.

|Response Requester.|
|uri|valid?|contents?|
|!-SourcePage-!|true||

Make sure that the new target name is present and that the old name is not.

|Response Examiner.|
|type  |pattern|matches?|
|contents|!-href="NewTarget"-!|true|
|contents|!-TargetPage-!|false|

