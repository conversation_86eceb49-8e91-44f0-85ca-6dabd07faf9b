/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 3.0.12
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package it.robur.test.physicalDCC.t32api;

public final class etGMError {
  public final static etGMError gmErrOk = new etGMError("gmErrOk");
  public final static etGMError gmErrUnknown = new etGMError("gmErrUnknown");
  public final static etGMError gmErrCRId = new etGMError("gmErrCRId");
  public final static etGMError gmErrBoardId = new etGMError("gmErrBoardId");
  public final static etGMError gmErrModuleOwn = new etGMError("gmErrModuleOwn");
  public final static etGMError gmErrMaster = new etGMError("gmErrMaster");
  public final static etGMError gmErrBoardModified = new etGMError("gmErrBoardModified");
  public final static etGMError gmErrModModified = new etGMError("gmErrModModified");
  public final static etGMError gmErrCRNotCompatible = new etGMError("gmErrCRNotCompatible");
  public final static etGMError gmErrModNotCompatible = new etGMError("gmErrModNotCompatible");
  public final static etGMError gmErrGlobTwoTubed = new etGMError("gmErrGlobTwoTubed");
  public final static etGMError gmErrGlobWW = new etGMError("gmErrGlobWW");
  public final static etGMError gmErrInconsistentCR = new etGMError("gmErrInconsistentCR");
  public final static etGMError gmErrBothGroups = new etGMError("gmErrBothGroups");
  public final static etGMError gmErrGroupPromotions = new etGMError("gmErrGroupPromotions");
  public final static etGMError gmErrBoardVsIOModId = new etGMError("gmErrBoardVsIOModId");
  public final static etGMError gmErrIOModId = new etGMError("gmErrIOModId");
  public final static etGMError gmErrIOModModified = new etGMError("gmErrIOModModified");
  public final static etGMError gmErrIOModOnSlave = new etGMError("gmErrIOModOnSlave");

  public final int swigValue() {
    return swigValue;
  }

  public String toString() {
    return swigName;
  }

  public static etGMError swigToEnum(int swigValue) {
    if (swigValue < swigValues.length && swigValue >= 0 && swigValues[swigValue].swigValue == swigValue)
      return swigValues[swigValue];
    for (int i = 0; i < swigValues.length; i++)
      if (swigValues[i].swigValue == swigValue)
        return swigValues[i];
    throw new IllegalArgumentException("No enum " + etGMError.class + " with value " + swigValue);
  }

  private etGMError(String swigName) {
    this.swigName = swigName;
    this.swigValue = swigNext++;
  }

  private etGMError(String swigName, int swigValue) {
    this.swigName = swigName;
    this.swigValue = swigValue;
    swigNext = swigValue+1;
  }

  private etGMError(String swigName, etGMError swigEnum) {
    this.swigName = swigName;
    this.swigValue = swigEnum.swigValue;
    swigNext = this.swigValue+1;
  }

  private static etGMError[] swigValues = { gmErrOk, gmErrUnknown, gmErrCRId, gmErrBoardId, gmErrModuleOwn, gmErrMaster, gmErrBoardModified, gmErrModModified, gmErrCRNotCompatible, gmErrModNotCompatible, gmErrGlobTwoTubed, gmErrGlobWW, gmErrInconsistentCR, gmErrBothGroups, gmErrGroupPromotions, gmErrBoardVsIOModId, gmErrIOModId, gmErrIOModModified, gmErrIOModOnSlave };
  private static int swigNext = 0;
  private final int swigValue;
  private final String swigName;
}

