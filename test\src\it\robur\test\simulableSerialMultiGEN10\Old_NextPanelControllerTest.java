package it.robur.test.simulableSerialMultiGEN10;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertThat;
import static org.junit.Assert.assertTrue;
import static uk.co.datumedge.hamcrest.json.SameJSONAs.sameJSONObjectAs;

import java.io.IOException;
import java.text.ParseException;
import java.util.Date;
import java.util.Locale;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.junit.After;
import org.junit.AfterClass;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Ignore;
import org.junit.Test;

import it.robur.gen10.GEN10.AnalogIndex;

import it.robur.gen10.GEN10.SummerVentilationSpeed;
import it.robur.gen10.GEN10.HeatingDiscretePowerLevel;
import it.robur.jserialtool.SerialPurejavacomm;
import it.robur.loggers.AllByTagJSONLogPublisher;
import it.robur.loggers.JSONPublisherDisableableDecorator;
import it.robur.loggers.JSONPublisherMulti;
import it.robur.loggers.JSONUtil;
import it.robur.loggers.LastByTagJSONLogPublisher;
import it.robur.loggers.LogConsts;
import it.robur.loggers.NextPanelJSONLogCons;
import it.robur.loggers.SimpleJSONLogPublisher;
import it.robur.loggers.Timer;
import it.robur.modbus.ModbusInstrument;
import it.robur.nextpanel.JSONNextPanel;
import it.robur.nextpanel.NextPanel;
import it.robur.nextpanel.NextPanelController;
import it.robur.nextpanel.NextPanelProducerConsumer;
import it.robur.nextpanel.StatefulNextZone;
import it.robur.nextpanel.StatefulNextZone.ZoneConfig.HeatingTimer.Level;
import it.robur.nextpanel.StatefulNextZone.ZoneConfig.HeatingModeEnum;
import it.robur.nextpanel.StatefulNextZone.ZoneConfig.ServiceModeEnum;
import it.robur.test.multiplatform.util.JSONTestUtil;
import it.robur.test.multiplatform.util.TestUtil;
import it.robur.util.DebugLog;

public class Old_NextPanelControllerTest
{
	
	// BEWARE!! TODO: remove each dontcare when adding it, and the remove the method altogether
	public static JSONObject nullifyNextPanelDontCares(JSONObject json) throws JSONException
	{
		if( json.has("device_id") )
			json.remove("device_id");
		
		if( json.has("summer_ventilation") )
			json.remove("summer_ventilation");
		
		if( json.has("heating_request_time") )
			json.remove("heating_request_time");
		
		if( json.has("vent_request_time") )
			json.remove("vent_request_time");
		
		if( json.has("board_serial") )
			json.put("board_serial", JSONTestUtil.zeroSerial);
		
		if( json.has("measure_unit") )
			json.put("measure_unit", "°C");

		if( json.has("heat_module_type_major") )
			json.put("heat_module_type_major", "0");
		
		if( json.has("daily_ids") )
			json.put("daily_ids", new JSONArray()); // i.e. zero-length
		
		if( json.has("param_ids") )
			json.put("param_ids", new JSONArray()); // i.e. zero-length
		
		if( json.has("params") )
			json.put("params", new JSONArray()); // i.e. zero-length
				
		if( json.has("digital_sample") )
			JSONTestUtil.zeroAllStringFields(json.getJSONObject("digital_sample"));
		
		if( json.has("analog_sample") )
			JSONTestUtil.zeroAllStringFields(json.getJSONObject("analog_sample"));		

		if( json.has("enum_sample") )
			JSONTestUtil.zeroAllStringFields(json.getJSONObject("enum_sample"));
		
		if( json.has("counters") )
			JSONTestUtil.zeroAllStringFields(json.getJSONObject("counters"));
		
		JSONArray keys = json.names();		
		String key;
		if( keys != null )
			for (int i = 0; i < keys.length(); ++i) 
			{
			   key = keys.getString(i);
			   
			   if ( json.get(key) instanceof JSONObject )
				   nullifyNextPanelDontCares(json.getJSONObject(key));
			   else if ( json.get(key) instanceof JSONArray )
				   nullifyNextPanelDontCares(json.getJSONArray(key));		   
			}
		
		return json;
	}
	public static JSONArray nullifyNextPanelDontCares(JSONArray json) throws JSONException
	{
		for (int i = 0; i < json.length(); ++i) 
		{		   
		   if ( json.get(i) instanceof JSONObject )
			   nullifyNextPanelDontCares(json.getJSONObject(i));
		   else if ( json.get(i) instanceof JSONArray )
			   nullifyNextPanelDontCares(json.getJSONArray(i));		   
		}
		
		return json;		
	}
	

	public static final int MODBUS_COMMAND_DESTRATIFICATION_ADDR  = 798;
	public static final int MODBUS_COMMAND_DIFFERENTIAL_ADDR      = 799;
	public static final int MODBUS_COMMAND_OPERATION_REQUEST_ADDR = 800;
	public static final int MODBUS_COMMAND_AMBIENT_SETPOINT_ADDR  = 802;
	public static final int MODBUS_COMMAND_DISCRETE_POWER_LEVEL_ADDR = 803;
	public static final int MODBUS_COMMAND_AMBIENT_TEMP_ADDR = 804;
	
	public final static String path = "test/res/json/NextPanel";
	public final static String JSONIdentificationFilename = "old_identification";
	public final static String JSONConfigurationFilename = "configuration"; // 1 zone
	public final static String JSONAllZonesConfigurationFilename = "configuration_all_zones";
	public final static String JSONSplitAtSplitTimeFilename = "split";
	
	private static DebugLog debugLog;
	
	private static NextPanel nextPanel;
	private static NextPanelController nextPanelController;
	
	private static LastByTagJSONLogPublisher jPublisher;
	
	@BeforeClass
	public static void setupTest() throws IOException
	{
		
	}
	
	@AfterClass
	public static void teardownTest() throws IOException
	{
		
	}
	
	@Before
	public void setup() throws IOException
	{
		debugLog = null;		
 
		nextPanel = null;
		nextPanelController = null;
		
	}
	
	@After
	public void teardown() throws IOException
	{
		if( nextPanel != null )
		{
			nextPanel.close();
			nextPanel = null;
		}
		
	}
	
	public void setupNextPanel() throws IOException
	{
		debugLog = new DebugLog(TestUtil.tmpPath, "npctest", true);
		
		SerialPurejavacomm serial = new SerialPurejavacomm(Settings.getPort());				
		jPublisher = new LastByTagJSONLogPublisher();
		
		String tProbeFilename = path + "/ntc_temperature_value";
		nextPanel = new NextPanel("1.0", serial, jPublisher, new JSONPublisherDisableableDecorator(null), tProbeFilename, debugLog);		
		    	
	}
	
	public void setupNextPanelController() throws IOException
	{
		setupNextPanel();
		
		nextPanelController = new NextPanelController(nextPanel, debugLog);
		    	
	}
	
	
    // *** Before test: set mod_RSsim OEM registers not compatible ***
    @Ignore
    @Test
    public void TestTickControllerOnlineNotCompatible() throws IOException
    {
    	int count;
    	
    	setupNextPanelController();
    	
    	nextPanelController.init(path, JSONIdentificationFilename, JSONConfigurationFilename);
    	
    	// preset OEM register to force all modules not compatible
    	for(int i=0; i<nextPanel.heaters.length; ++i)
    	{
    		nextPanel.heaters[i].modbus.getModbusInstrument().writeRegister(1, 3);
    	}
    	
    	for(count=0; count < 4; count++)
    	{
	    	nextPanelController.tick.op();
	    	// check online & compatibility
	    	for(int j=0; j<nextPanel.heaters.length; ++j)
	    	{
		    	assertFalse(nextPanel.heaters[j].stateful.offline);
		    	assertFalse(nextPanel.heaters[j].stateful.compatible);
	    	}
    	}
    	// check analogs: should be not read
    	for(int i=0; i<nextPanel.heaters.length; ++i)
    	{
    		assertThat(nextPanel.heaters[i].stateful.analogs[AnalogIndex.SETPOINT_TEMP.ordinal()],
    				equalTo(null));
    	}
    	
    	// now set compatible and check if ok
    	for(int i=0; i<nextPanel.heaters.length; ++i)
    	{
    		nextPanel.heaters[i].modbus.getModbusInstrument().writeRegister(1, 1);
    		
        	// simulate offline - needed to re-check compatibility - NB simulation needed because setting modSim offline presently is not supported.
        	nextPanel.heaters[i].stateful.offline = true;
    	}
    	// preset setpoint value
    	for(int i=0; i<nextPanel.heaters.length; ++i)
    	{
    		nextPanel.heaters[i].modbus.getModbusInstrument().writeRegister(500, 9);
    		nextPanel.heaters[i].modbus.getModbusInstrument().writeRegister(504, ModbusInstrument.NO_VALUE_SIGNED);	
    	}
    	nextPanelController.tick.op();
    	// check online & compatibility
    	for(int i=0; i<nextPanel.heaters.length; ++i)
    	{
	    	assertFalse(nextPanel.heaters[i].stateful.offline);
	    	assertTrue(nextPanel.heaters[i].stateful.compatible);

	    	assertThat(nextPanel.heaters[i].stateful.analogs[AnalogIndex.SETPOINT_TEMP.ordinal()],
					equalTo(LogConsts.NO_VALUE_STRING));
    	}
    	
    	// now set not compatible appliance type (and force offline to update) ========== 
    	for(int i=0; i<nextPanel.heaters.length; ++i)
    	{
			nextPanel.heaters[i].modbus.getModbusInstrument().writeRegister(110, 45);
	    	nextPanel.heaters[i].stateful.offline = true;
    	}
    	nextPanelController.tick.op();
    	// check online & compatibility
    	for(int i=0; i<nextPanel.heaters.length; ++i)
    	{
	    	assertFalse(nextPanel.heaters[i].stateful.offline);
	    	assertFalse(nextPanel.heaters[i].stateful.compatible);

	    	// TODO check if analogs should keep previous value or not
	    	//assertThat(nextPanel.heaters[i].stateful.analogs[AnalogIndex.SETPOINT_TEMP.ordinal()],
	    	//		equalTo(LogConsts.NO_VALUE_STRING));
    	}
    	// restore compatible appliance type  
    	for(int i=0; i<nextPanel.heaters.length; ++i)
    	{
			nextPanel.heaters[i].modbus.getModbusInstrument().writeRegister(110, 0);
	    	nextPanel.heaters[i].stateful.offline = true;
    	}
    	// ===========================
    	
    	
    	
    	// TODO test real offline using 40809 register when supported by GEN10 simulator
    }    	
    	
    
    
    @Test
    public void TestTickController() throws IOException
    {
    	setupNextPanelController();
    	
    	nextPanelController.init(path, JSONIdentificationFilename, JSONConfigurationFilename);
    	
    	// preset setpoint value
    	for(int i=0; i<nextPanel.heaters.length; ++i)
    	{
    		nextPanel.heaters[i].modbus.getModbusInstrument().writeRegister(500, 9);
    		nextPanel.heaters[i].modbus.getModbusInstrument().writeRegister(504, ModbusInstrument.NO_VALUE_SIGNED);	
    	}
    	nextPanel.heaters[2].stateful.config.userExcluded = false;	// set different from loaded configuration.json
    	nextPanelController.tick.op();
    	
    	// Debug
//    	JSONTestUtil.saveJSON("tmpidentification", jPublisher.getLast("np_identification"));
    	
    	int numberOfHeaters = nextPanel.heaters.length;
    	assertThat(numberOfHeaters, equalTo(3));	// according to reference identification

    	JSONArray statistics = jPublisher.getLast("np_split").getJSONArray("statistics_samples").getJSONObject(0).getJSONArray("heater_modules");
    	for(int i=0; i<numberOfHeaters; ++i)
    	{
	    	// check online & compatibility
	    	assertFalse(nextPanel.heaters[i].stateful.offline);
	    	assertTrue(nextPanel.heaters[i].stateful.compatible);
	    	
	    	// check analogs
	    	// current setpoint shall be ABSENT_VALUE (i.e. not provided) when module is turned off. 
	    	// here we check NO_VALUE because of limitation of preset modbus slave simulator ****
	    	assertThat(nextPanel.heaters[i].stateful.analogs[AnalogIndex.SETPOINT_TEMP.ordinal()],
	    					equalTo(LogConsts.NO_VALUE_STRING));

	    	// check statistics (json format)
	    	assertThat(statistics.getJSONObject(i).getJSONObject("counters").getString("power_on_time"), equalTo("7200"));
	    	assertThat(statistics.getJSONObject(i).getJSONObject("counters").getString("flame_on_time"), equalTo("3600"));
	    	assertThat(statistics.getJSONObject(i).getJSONObject("counters").getString("ignitions"), equalTo("10"));
	    	assertThat(statistics.getJSONObject(i).getJSONObject("counters").getString("missed_ignitions"), equalTo("2"));
    	}    	
    	
    	// check mean ambient temperature (first zone)
    	assertThat(nextPanel.stateful.getZone(1).meanAmbientTemp, equalTo(ModbusInstrument.NO_VALUE_SIGNED));
    	
    	
    	
    	// check commands
    	// NB this test should read from modbus to get command registers
    	//assertThat(nextPanel.stateful.getZone(1).config.differential, equalTo(20));	// default value
    	
    	// heaters[2] do not belongs to any zone, so module's default commands should apply.
    	assertThat(nextPanel.heaters[2].modbus.getModbusInstrument().readRegisters(MODBUS_COMMAND_DIFFERENTIAL_ADDR, 1)[0], equalTo(20));
	

    	// NEW TICK
    	// preset differential in zone 0 - 
    	// NB: only first 2 modules (of 3) belong to zone 0.
    	nextPanel.stateful.getZone(1).config.onSwitch = false;
    	nextPanel.stateful.getZone(1).config.destratification = true;
    	int myDifferential = 40;
    	nextPanel.stateful.getZone(1).config.differential = myDifferential;

    	// NB : due to ModSim limitation, all heater modules share same registers values, so only last written value counts.
    	// So I need to exclude last module. This test is compatible with GEN10 simulator, soon to be introduced. 
    	nextPanel.heaters[2].stateful.config.userExcluded = true;

    	// preset ambient temperature to check mean temperature computation
    	for(int i=0; i<nextPanel.heaters.length; ++i)
    	{
    		nextPanel.heaters[i].modbus.getModbusInstrument().writeRegister(500, 2);
    		nextPanel.heaters[i].modbus.getModbusInstrument().writeRegister(504, 195);	// i.e. 19,5°C
    	}
    	// TODO add test with different values when GEN10 simulator will be introduced.
    	
    	nextPanelController.tick.op();

    	assertThat(nextPanel.stateful.getZone(1).config.differential, equalTo(myDifferential));
    	// check mean ambient temperature (first zone)
    	assertThat(nextPanel.stateful.getZone(1).meanAmbientTemp, equalTo(195));
    	
    	
    	// NB due to ModSim limitation the loop will read always the same register value
    	for(int i=0; i<numberOfHeaters; ++i)
    	{
    		assertThat(nextPanel.heaters[i].modbus.getModbusInstrument().readRegisters(MODBUS_COMMAND_DESTRATIFICATION_ADDR, 1)[0], equalTo(1));	// i.e. ON
    		assertThat(nextPanel.heaters[i].modbus.getModbusInstrument().readRegisters(MODBUS_COMMAND_DIFFERENTIAL_ADDR, 1)[0], equalTo(myDifferential));
    		assertThat(nextPanel.heaters[i].modbus.getModbusInstrument().readRegisters(MODBUS_COMMAND_OPERATION_REQUEST_ADDR, 1)[0], equalTo(0));	// i.e. OFF
    		// other commands don't care when off
    	}
    	
    	
    	// Debug
//    	JSONTestUtil.saveJSON("tmpidentification", jPublisher.getLast("np_identification"));
    	    	
        assertThat(
        		nullifyNextPanelDontCares(JSONTestUtil.nullifyDontCares(jPublisher.getLast("np_identification"))),
                sameJSONObjectAs(nullifyNextPanelDontCares(JSONTestUtil.loadJSONWithNullDontCaresSubDir("NextPanel", JSONIdentificationFilename))));
        
        
    	// Debug
//    	JSONTestUtil.saveJSON("tmpsample", jPublisher.getLast("np_sample"));
//    	JSONTestUtil.saveJSON("nullifiedsample", nullifyNextPanelDontCares(JSONTestUtil.loadJSONWithNullDontCaresSubDir("NextPanel", "sample")));
        
        assertThat(
        		nullifyNextPanelDontCares(JSONTestUtil.nullifyDontCares(jPublisher.getLast("np_sample"))),
                sameJSONObjectAs(nullifyNextPanelDontCares(JSONTestUtil.loadJSONWithNullDontCaresSubDir("NextPanel", "sample_all_zones"))));   	


    }
    
    @Test
    public void TestTickControllerCommands() throws IOException, ParseException
    {
    	setupNextPanelController();
    	nextPanelController.init(path, JSONIdentificationFilename, JSONConfigurationFilename);

    	// NB : due to ModSim limitation, all heater modules share same registers values, so only last written value counts.
    	// So I need to exclude last module. This test is compatible with GEN10 simulator, soon to be introduced. 
    	nextPanel.heaters[2].stateful.config.userExcluded = true;
    	
    	// preset zone commands for first zone, set by GUI
    	nextPanel.stateful.getZone(1).config.onSwitch = true;
    	nextPanel.stateful.getZone(1).config.heatingMode = HeatingModeEnum.ON_OFF;
    	nextPanel.stateful.getZone(1).config.heatingDiscretePowerLevel = HeatingDiscretePowerLevel.LEVEL_2;
    	nextPanel.stateful.getZone(1).config.serviceMode = ServiceModeEnum.HEATING;
    	nextPanel.stateful.getZone(1).config.defaultSetpoint = 230; 	// i.e. 23,0°C
    	nextPanel.stateful.getZone(1).config.heatingTimerEnable = false;
    	nextPanel.stateful.getZone(1).config.destratification = false;
    	nextPanel.stateful.getZone(1).config.differential = 30;
    	
    	// preset ambient temperature to check mean temperature computation
    	for(int i=0; i<nextPanel.heaters.length; ++i)
    	{
    		nextPanel.heaters[i].modbus.getModbusInstrument().writeRegister(500, 2);
    		nextPanel.heaters[i].modbus.getModbusInstrument().writeRegister(504, 180);	// i.e. 18°C
    	}
    	
    	nextPanelController.tick.op();

    	// NB due to ModSim limitation the loop will read always the same register value
    	// loop on first zone heaters
    	for(int i=0; i<2; ++i)
    	{
			assertThat(nextPanel.heaters[i].modbus.getModbusInstrument().readRegisters(MODBUS_COMMAND_OPERATION_REQUEST_ADDR, 1)[0], equalTo(2));	// i.e. ON discrete power level
			assertThat(nextPanel.heaters[i].modbus.getModbusInstrument().readRegisters(MODBUS_COMMAND_AMBIENT_SETPOINT_ADDR, 1)[0], equalTo(230));
			assertThat(nextPanel.heaters[i].modbus.getModbusInstrument().readRegisters(MODBUS_COMMAND_DISCRETE_POWER_LEVEL_ADDR, 1)[0], equalTo(2));
			assertThat(nextPanel.heaters[i].modbus.getModbusInstrument().readRegisters(MODBUS_COMMAND_AMBIENT_TEMP_ADDR, 1)[0], equalTo(180));
			
			assertThat(nextPanel.heaters[i].modbus.getModbusInstrument().readRegisters(MODBUS_COMMAND_DESTRATIFICATION_ADDR, 1)[0], equalTo(0));
			assertThat(nextPanel.heaters[i].modbus.getModbusInstrument().readRegisters(MODBUS_COMMAND_DIFFERENTIAL_ADDR, 1)[0], equalTo(30));
    	}
    	
    	// now change heating mode and setpoint
    	nextPanel.stateful.getZone(1).config.heatingMode = HeatingModeEnum.MODULATION;
    	nextPanel.stateful.getZone(1).config.defaultSetpoint = 250; 	// i.e. 25,0°C

    	nextPanelController.tick.op();

    	// NB due to ModSim limitation the loop will read always the same register value
    	// loop on first zone heaters
    	for(int i=0; i<2; ++i)
    	{
			assertThat(nextPanel.heaters[i].modbus.getModbusInstrument().readRegisters(MODBUS_COMMAND_OPERATION_REQUEST_ADDR, 1)[0], equalTo(3));	// i.e. ON modulation
			assertThat(nextPanel.heaters[i].modbus.getModbusInstrument().readRegisters(MODBUS_COMMAND_AMBIENT_SETPOINT_ADDR, 1)[0], equalTo(250));
			//assertThat(nextPanel.heaters[i].modbus.getModbusInstrument().readRegisters(MODBUS_COMMAND_DISCRETE_POWER_LEVEL_ADDR, 1)[0], equalTo(2)); don't care
			assertThat(nextPanel.heaters[i].modbus.getModbusInstrument().readRegisters(MODBUS_COMMAND_AMBIENT_TEMP_ADDR, 1)[0], equalTo(180));
			
			assertThat(nextPanel.heaters[i].modbus.getModbusInstrument().readRegisters(MODBUS_COMMAND_DESTRATIFICATION_ADDR, 1)[0], equalTo(0));
			assertThat(nextPanel.heaters[i].modbus.getModbusInstrument().readRegisters(MODBUS_COMMAND_DIFFERENTIAL_ADDR, 1)[0], equalTo(30));
    	}
    	
    	
    	
    }
    
	
    @Test
    public void TestTickControllerLoop() throws IOException, ParseException
    {
    	setupNextPanelController();
    	
    	final int nTicks = 24;
    	final int period = 5; // NOTE: standard period is 40 sec.
    	
    	nextPanelController.init(path, JSONIdentificationFilename, JSONConfigurationFilename);
    	
    	// preset setpoint value
    	for(int i=0; i<nextPanel.heaters.length; ++i)
    	{
    		nextPanel.heaters[i].modbus.getModbusInstrument().writeRegister(500, 9);
    		nextPanel.heaters[i].modbus.getModbusInstrument().writeRegister(504, ModbusInstrument.NO_VALUE_SIGNED);	
    	}
    	
    	Thread stopThread = new Thread(new Runnable() 
		{    		
            public void run() 
            {
            	try 
            	{
    				Thread.sleep(nTicks * period * 1000);
    				
    				nextPanelController.stop();
    				
    			} 
            	catch (InterruptedException e) 
            	{
    				e.printStackTrace();
    			}
            }
		});
    	stopThread.start();
    	
    	
    	nextPanelController.startControlLoop(period, Timer.timeStringToDate("00:30"));
            	
    	    	
    	// check analogs
    	// current setpoint shall be NO_VALUE when module is turned off. 
    	assertThat(nextPanel.heaters[0].stateful.analogs[AnalogIndex.SETPOINT_TEMP.ordinal()],
    					equalTo(LogConsts.NO_VALUE_STRING));
    	   	
    	
    	// initially used to generate reference JSON:
//    	JSONTestUtil.saveJSON("identification_loop", jPublisher.getLast("np_identification"));
    	
        assertThat(
        		nullifyNextPanelDontCares(JSONTestUtil.nullifyDontCares(jPublisher.getLast("np_identification"))),
                sameJSONObjectAs(nullifyNextPanelDontCares(JSONTestUtil.loadJSONWithNullDontCaresSubDir("NextPanel", JSONIdentificationFilename))));
        
        assertThat(
        		nullifyNextPanelDontCares(JSONTestUtil.nullifyDontCares(jPublisher.getLast("np_sample"))),
                sameJSONObjectAs(nullifyNextPanelDontCares(JSONTestUtil.loadJSONWithNullDontCaresSubDir("NextPanel", "sample_all_zones"))));   	
    	    	
    }
    
    @Test
    public void TestSplit() throws IOException, ParseException
    {
    	
    	debugLog = new DebugLog(TestUtil.tmpPath, "npctest", true);
		
		SerialPurejavacomm serial = new SerialPurejavacomm(Settings.getPort());		
		
		AllByTagJSONLogPublisher jPublisherAlways    = new AllByTagJSONLogPublisher();
		AllByTagJSONLogPublisher jPublisherOnlySplit = new AllByTagJSONLogPublisher(); 
		JSONPublisherDisableableDecorator disableableJPublisherOnlySplit = new JSONPublisherDisableableDecorator(jPublisherOnlySplit);
		
		JSONPublisherMulti.Builder jsonSamplePublisherBuilder  = new JSONPublisherMulti.Builder();
		jsonSamplePublisherBuilder.addPublisher(jPublisherAlways);
		jsonSamplePublisherBuilder.addPublisher(disableableJPublisherOnlySplit);
		JSONPublisherMulti jsonSamplePublishers = jsonSamplePublisherBuilder.buildIfNeeded();
		
		String tProbeFilename = path + "/ntc_temperature_value";
		nextPanel = new NextPanel("1.0", serial, jsonSamplePublishers, disableableJPublisherOnlySplit, tProbeFilename, debugLog);				    				
		nextPanelController = new NextPanelController(nextPanel, debugLog);
		
		nextPanelController.init(path, JSONIdentificationFilename, JSONConfigurationFilename);
    	
    	
    	final int period = 40; // NOTE: standard period is 40 sec.
    	final int waitMinutes = 3;
    	    	
    	Thread stopThread = new Thread(new Runnable() 
		{    		
            public void run() 
            {
            	try 
            	{
    				Thread.sleep( ((waitMinutes * 60) + (period)) * 1000);
    				
    				nextPanelController.stop();
    				
    			} 
            	catch (InterruptedException e) 
            	{
    				e.printStackTrace();
    			}
            }
		});
    	stopThread.start();
    	
    	
    	nextPanelController.startControlLoop(period, new Date(System.currentTimeMillis() + (waitMinutes * 60 * 1000)));
    	
    	assertTrue(jPublisherAlways.getCount("np_split") > 1 );
    	assertEquals(jPublisherAlways.getCount("np_split"), jPublisherAlways.getCount("np_sample"));
    	
    	assertEquals(jPublisherOnlySplit.getCount("np_split"), 1);
    	assertEquals(jPublisherOnlySplit.getCount("np_sample"), jPublisherAlways.getCount("np_sample") );
    	   	
    	
    	// initially used to generate reference JSON:
    	JSONTestUtil.saveJSON("tmpAlwaysSplit", jPublisherAlways.get("np_split", 0));
    	JSONTestUtil.saveJSON("tmpOnlySplit",   jPublisherOnlySplit.get("np_split", 0));
    	
    	JSONObject fullSplit = JSONTestUtil.loadJSONWithNullDontCaresSubDir("NextPanel", JSONSplitAtSplitTimeFilename);
    	JSONObject noParamsSplit = JSONTestUtil.loadJSONWithNullDontCaresSubDir("NextPanel", JSONSplitAtSplitTimeFilename);
    	noParamsSplit.remove("params_samples");
    	
        assertThat(
        		nullifyNextPanelDontCares(JSONTestUtil.nullifyDontCares(jPublisherOnlySplit.get("np_split", 0))),
                sameJSONObjectAs(nullifyNextPanelDontCares(fullSplit)));
        
        assertThat(
        		nullifyNextPanelDontCares(JSONTestUtil.nullifyDontCares(jPublisherAlways.get("np_split", 0))),
        		sameJSONObjectAs(nullifyNextPanelDontCares(noParamsSplit)));
        
        assertThat(
        		nullifyNextPanelDontCares(JSONTestUtil.nullifyDontCares(jPublisherAlways.get("np_split", jPublisherAlways.getCount("np_split") - 1))),
        		sameJSONObjectAs(nullifyNextPanelDontCares(fullSplit)));

    	    	
    }


    @SuppressWarnings("deprecation")
	@Test
    public void TestHeatingTimer()
    {
    	int i;
    	
    	// NB controller timer needs NextPanelConfig.Zone.HeatingTimer
    	StatefulNextZone.ZoneConfig.HeatingTimer configTimer = new StatefulNextZone.ZoneConfig.HeatingTimer();
    	StatefulNextZone.HeatingTimer timer = new StatefulNextZone.HeatingTimer(configTimer);
    	
    	// set time slots 
    	for(i=10; i<20; ++i)
    		configTimer.setTimeSlot(StatefulNextZone.ZoneConfig.HeatingTimer.DayOfTheWeek.sunday, i, StatefulNextZone.ZoneConfig.HeatingTimer.Level.ANTIFREEZE);
    	
    	for(i=30; i<35; ++i)
    		configTimer.setTimeSlot(StatefulNextZone.ZoneConfig.HeatingTimer.DayOfTheWeek.sunday, i, StatefulNextZone.ZoneConfig.HeatingTimer.Level.REDUCED);
    	
    	// now test
		Date date1 = new Date(2023-1900, 1-1, 29, 2, 0, 0);	// yy-1900 mm-1 dd hh mm ss - i.e. hhIndex 4
		assertThat(timer.getPresentLevel(date1), equalTo(StatefulNextZone.ZoneConfig.HeatingTimer.Level.OFF));
		Date date2 = new Date(2023-1900, 1-1, 29, 6, 0, 0);	// i.e. hhIndex 12
		assertThat(timer.getPresentLevel(date2), equalTo(StatefulNextZone.ZoneConfig.HeatingTimer.Level.ANTIFREEZE));
		Date date3 = new Date(2023-1900, 1-1, 29, 17, 0, 0);// i.e. hhIndex 34	
		assertThat(timer.getPresentLevel(date3), equalTo(StatefulNextZone.ZoneConfig.HeatingTimer.Level.REDUCED));
		Date date4 = new Date(2023-1900, 1-1, 29, 20, 0, 0);// i.e. hhIndex 40	
		assertThat(timer.getPresentLevel(date4), equalTo(StatefulNextZone.ZoneConfig.HeatingTimer.Level.OFF));
		// set extended time
		
		timer.setExtendTimeSlot(5, date1);
		assertThat(timer.getPresentLevel(date2), equalTo(StatefulNextZone.ZoneConfig.HeatingTimer.Level.OFF));
		Date date2_5 = new Date(2023-1900, 1-1, 29, 8, 0, 0);	
		assertThat(timer.getPresentLevel(date2_5), equalTo(StatefulNextZone.ZoneConfig.HeatingTimer.Level.ANTIFREEZE));
		// implicit test that extension expired
		assertThat(timer.getPresentLevel(date3), equalTo(StatefulNextZone.ZoneConfig.HeatingTimer.Level.REDUCED));
		assertThat(timer.getPresentLevel(date4), equalTo(StatefulNextZone.ZoneConfig.HeatingTimer.Level.OFF));
		
		
    }
    
    
    //--------------------------------------------------------------------------------------------------------
    // TODO: move the following test in a package/file with no pshyical/simulable requirement
    @Test
    public void testCopyWholeConfigFromJSONToJSON() throws JSONException, IOException
    {
    	Locale.setDefault(new Locale("it", "IT"));
    	
    	JSONNextPanel jProducer = new JSONNextPanel();
    		    		    	
    	jProducer.storeIdentification(JSONUtil.loadJSON(path, JSONIdentificationFilename));
    	
    	SimpleJSONLogPublisher jPublisher = new SimpleJSONLogPublisher();
    	NextPanelJSONLogCons jConsumer = new NextPanelJSONLogCons( jPublisher, true, null);			
		jConsumer.setAndConnect(jProducer.getSerial());	
    	
		//TODO:? jProducer.copyWholeIdentification(jProducer.identificationProducer, jConsumer.identificationCons);
    	
    	final JSONObject np_config = JSONUtil.loadJSON(path, JSONConfigurationFilename);	    	
    	jProducer.storeConfigSamples(np_config);
    		    	    					    	
    	// invoke on the object that needs / can need lock
    	NextPanelProducerConsumer.copyWholeSampleConfig(jProducer.sampleConfigProducer, jConsumer.sampleConfigConsumer);
    	
    	// decomment to debug
//    	JSONTestUtil.saveJSON("tmp" + JSONConfigurationFilename, jPublisher.getJSON());

    	assertThat(
    			jPublisher.getJSON(),
                sameJSONObjectAs(np_config));
		    	

    }
    
    @Test
    public void TestNextPanelLoadConfiguration() throws IOException
    {
    	setupNextPanel();
    	
    	nextPanel.buildFromConfiguration(path, JSONIdentificationFilename, JSONConfigurationFilename);
    	// presently config files establish 1 zone with 2 modules and a total of 3 modules (modbus address 1, 2, 4)
    	
    	
    	// check next panel
    	assertThat(nextPanel.stateful.config.language, equalTo("it"));
    	assertThat(nextPanel.stateful.config.enableScreenLock, equalTo(false));
    	assertThat(nextPanel.stateful.config.modbusServerAddress, equalTo(1));
    	assertThat(nextPanel.stateful.config.temperatureProbeOffset, equalTo(0));
    	
    	
    	// check zone
    	assertThat(nextPanel.stateful.getZone(1).getId(), equalTo(1));
    	// TODO: check module binding in zones
    	assertThat(nextPanel.getNextHeater(nextPanel.stateful.getZone(1).config.heatersIds.get(0)).getModbusAddress(), equalTo(1));
    	assertThat(nextPanel.getNextHeater(nextPanel.stateful.getZone(1).config.heatersIds.get(1)).getModbusAddress(), equalTo(2));
    	
    	assertThat(nextPanel.stateful.getZone(1).config.name, equalTo("magazzino"));
    	assertThat(nextPanel.stateful.getZone(1).config.predictiveStartup, equalTo(true));
    	assertThat(nextPanel.stateful.getZone(1).config.defaultVentilationSpeed, equalTo(SummerVentilationSpeed.SPEED_2));
    	assertThat(nextPanel.stateful.getZone(1).config.heatingTimer.getSetpoint(Level.REDUCED), equalTo(100));
    	assertThat(nextPanel.stateful.getZone(1).config.heatingTimer.getSetpoint(Level.REDUCED), equalTo(100));
    	

    	// check modules
    	assertThat(nextPanel.heaters.length, equalTo(3));
    	assertThat(nextPanel.heaters[0].getModbusAddress(), equalTo(1));
    	assertThat(nextPanel.heaters[1].getModbusAddress(), equalTo(2));
    	assertThat(nextPanel.heaters[2].getModbusAddress(), equalTo(4));
    	
    	assertThat(nextPanel.getNextHeater(1).stateful.config.userExcluded, equalTo(false));
    	assertThat(nextPanel.getNextHeater(2).stateful.config.userExcluded, equalTo(false));
    	assertThat(nextPanel.getNextHeater(4).stateful.config.userExcluded, equalTo(true));


    }
    
    @Test
    public void TestNextPanelLoadOneZoneConfigurationAndSaveAllZones() throws IOException
    {
    	setupNextPanel();
    	
    	nextPanel.buildFromConfiguration(path, JSONIdentificationFilename, JSONConfigurationFilename);
    	
    	
    	Locale.setDefault(new Locale("it", "IT"));
    	    	
    	SimpleJSONLogPublisher jPublisher = new SimpleJSONLogPublisher();
    	NextPanelJSONLogCons jConsumer = new NextPanelJSONLogCons( jPublisher, true, null);			
		jConsumer.setAndConnect(nextPanel.stateful.getSerial());	
    	
		//TODO:? jProducer.copyWholeIdentification(jProducer.identificationProducer, jConsumer.identificationCons);
    	    		    	    					    	
    	// invoke on the object that needs / can need lock
		NextPanelProducerConsumer.copyWholeSampleConfig(nextPanel.stateful.sampleConfigProducer, jConsumer.sampleConfigConsumer);
    	
    	// decomment to debug
    	JSONTestUtil.saveJSON("tmp" + JSONConfigurationFilename, jPublisher.getJSON());

    	assertThat(
    			JSONTestUtil.nullifyDontCares(jPublisher.getJSON()),
                sameJSONObjectAs(JSONTestUtil.loadJSONWithNullDontCares(path, JSONAllZonesConfigurationFilename)));

    }
    
    @Test
    public void TestNextPanelLoadAndSaveAllZonesConfiguration() throws IOException
    {
    	setupNextPanel();
    	
    	nextPanel.buildFromConfiguration(path, JSONIdentificationFilename, JSONAllZonesConfigurationFilename);
    	
    	
    	Locale.setDefault(new Locale("it", "IT"));
    	    	
    	SimpleJSONLogPublisher jPublisher = new SimpleJSONLogPublisher();
    	NextPanelJSONLogCons jConsumer = new NextPanelJSONLogCons( jPublisher, true, null);			
		jConsumer.setAndConnect(nextPanel.stateful.getSerial());	
    	
		//TODO:? jProducer.copyWholeIdentification(jProducer.identificationProducer, jConsumer.identificationCons);
    	    		    	    					    	
    	// invoke on the object that needs / can need lock
		NextPanelProducerConsumer.copyWholeSampleConfig(nextPanel.stateful.sampleConfigProducer, jConsumer.sampleConfigConsumer);
    	
    	// decomment to debug
    	JSONTestUtil.saveJSON("tmp" + JSONConfigurationFilename, jPublisher.getJSON());

    	assertThat(
    			JSONTestUtil.nullifyDontCares(jPublisher.getJSON()),
                sameJSONObjectAs(JSONTestUtil.loadJSONWithNullDontCares(path, JSONAllZonesConfigurationFilename)));

    }


    

}
