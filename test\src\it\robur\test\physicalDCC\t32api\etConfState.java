/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 3.0.12
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package it.robur.test.physicalDCC.t32api;

public final class etConfState {
  public final static etConfState csUnset = new etConfState("csUnset", 0x2492);
  public final static etConfState csBeforeLocalConf = new etConfState("csBeforeLocalConf", 0x4891);
  public final static etConfState csLocalConf = new etConfState("csLocalConf", 0x4925);
  public final static etConfState csBeforeGlobalConf = new etConfState("csBeforeGlobalConf", 0x36DB);
  public final static etConfState csGlobalConf = new etConfState("csGlobalConf", 0x6DB6);
  public final static etConfState csReady = new etConfState("csReady", 0x9249);
  public final static etConfState csTotalResetting = new etConfState("csTotalResetting", 0xB6DA);

  public final int swigValue() {
    return swigValue;
  }

  public String toString() {
    return swigName;
  }

  public static etConfState swigToEnum(int swigValue) {
    if (swigValue < swigValues.length && swigValue >= 0 && swigValues[swigValue].swigValue == swigValue)
      return swigValues[swigValue];
    for (int i = 0; i < swigValues.length; i++)
      if (swigValues[i].swigValue == swigValue)
        return swigValues[i];
    throw new IllegalArgumentException("No enum " + etConfState.class + " with value " + swigValue);
  }

  private etConfState(String swigName) {
    this.swigName = swigName;
    this.swigValue = swigNext++;
  }

  private etConfState(String swigName, int swigValue) {
    this.swigName = swigName;
    this.swigValue = swigValue;
    swigNext = swigValue+1;
  }

  private etConfState(String swigName, etConfState swigEnum) {
    this.swigName = swigName;
    this.swigValue = swigEnum.swigValue;
    swigNext = this.swigValue+1;
  }

  private static etConfState[] swigValues = { csUnset, csBeforeLocalConf, csLocalConf, csBeforeGlobalConf, csGlobalConf, csReady, csTotalResetting };
  private static int swigNext = 0;
  private final int swigValue;
  private final String swigName;
}

