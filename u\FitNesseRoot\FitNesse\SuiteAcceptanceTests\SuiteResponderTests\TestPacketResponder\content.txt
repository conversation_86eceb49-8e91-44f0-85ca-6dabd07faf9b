!define TEST_SYSTEM {slim}

!path lib/*.jar
!define PACKET {|Bob|
||<PERSON>|
|||<PERSON><PERSON>|6|
|||<PERSON>|4|
|||<PERSON>|2|
||<PERSON>|
|||<PERSON><PERSON>|5|
||<PERSON>|
||<PERSON>|
}

!define JSON ({"tables": [{"<PERSON>": {
 "<PERSON>": {
  "<PERSON><PERSON>": "6",
  "<PERSON>": "2",
  "<PERSON>": "4"
 },
 "<PERSON>": {},
 "<PERSON>": {},
 "<PERSON>": {"Luka": "5"}
}}]})

!|script|page driver|
|given page|PacketPage|with content|${PACKET}|
|request page|$IT?packet|
|contains json packet|${JSON}|
|show|content|

