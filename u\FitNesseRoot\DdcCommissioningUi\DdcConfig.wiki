---
Test
---
!***< 
!define TEST_SYSTEM {slim}
*!
!headings

!1 DDC Configuration UI


The user must be able to review DDC configuration details


!3 Load

|it.robur.test.fitnesse.ddc.DDCConfigFixture                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               |
|'''Load Config'''        |''Cool Thermostatation Mode''?|''Heat Thermostatation Mode''?|''Cool Heat Water Pump Mode''?|''Cool Heat Working Mode''?|''Cool Space Water Set Point''?|''Cool Chrono T3''?|''Cool Weather Enable''?|''Cool Weather Offset''?|''Cool Weather Min In Setp''?|''Cool Weather Max In Setp''?|''Cool Weather Min Out Setp''?|''Cool Weather Max Out Setp''?|''Cool Weather TF1''?|''Cool Weather TF2''?|''Cool Init Power''?|''Cool Differential''?|''Cool Cat1 Stages''?|''Cool Cat1 Run Time''?|''Cool Cat1 Release Integral''?|''Cool Cat1 Reset Integral''?|''Cool Cat1 Locking Time''?|''Heat Space Water Set Point''?|''Heat Chrono T3''?|''Heat Weather Enable''?|''Heat Weather Offset''?|''Heat Weather Min In Setp''?|''Heat Weather Max In Setp''?|''Heat Weather Min Out Setp''?|''Heat Weather Max Out Setp''?|''Heat Weather Slope''?|''Heat Init Power''?|''Heat Differential''?|''Heat Cat1 Stages''?|''Heat Cat1 Run Time''?|''Heat Cat1 Release Integral''?|''Heat Cat1 Reset Integral''?|''Heat Cat1 Locking Time''?|''Heat Cat3 Stages''?|''Heat Cat3 Run Time''?|''Heat Cat3 Release Integral''?|''Heat Cat3 Reset Integral''?|''Heat Cat3 Locking Time''?|
|2pipes_ddc_config_change1|out                                |out                                |common_pump                        |Wcmp                            |8.5                                 |25.0                    |true                         |0.0                          |12.0                              |19.0                              |7.0                                |14.0                               |8.5                       |8.5                       |30                       |2.0                        |2                         |0                          |30                                 |40                               |7                              |57.0                             |22.0                    |true                         |0.0                          |30.0                              |70.0                              |40.0                               |80.0                               |201                         |30                       |2.0                        |10                        |7                          |0                                  |0                                |5                              |1                         |3                          |60                                 |80                               |2                              |

!3 Save

TODO

#|it.robur.test.fitnesse.ddc.DDCConfigFixture                                                                                           |
#|'''Load Config'''          |'''Cool Thermostatation Mode'''|'''Heat Thermostatation Mode'''|''Save Config''?                     |''Error''?|''Error String''?|
#|2pipes ddc config default 0|out                      |out                      |#json:2pipes ddc config default 0|false |<unset>      |


