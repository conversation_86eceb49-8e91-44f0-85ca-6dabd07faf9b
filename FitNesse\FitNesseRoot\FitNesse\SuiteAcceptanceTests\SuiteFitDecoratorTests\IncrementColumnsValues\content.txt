What if you want to increment the value of one of the columns of your fixture table at run time? You can use the !-IncrementColumnsValue-! decorator. This lets you increment column's values. This decorator supports the following data types:
 * int or integer
 * double
 * string
 * date

!|Increment Columns Value|numerator|of type|int|by|5|
|Division|
|numerator|denominator|quotient()|
|10|2|5|
|10|3|5|
|10|4|5|

!|Increment Columns Value|numerator|of type|integer|by|5|
|Division|
|numerator|denominator|quotient()|
|10|2|5|
|10|3|5|
|10|4|5|

!|Increment Columns Value|numerator|of type|double|by|10.2|
|Division|
|numerator|denominator|quotient()|
|10.2|2|5.1|
|10.2|4|5.1|

!|Increment Columns Value|numerator|of type|string|by|5|
|Division|
|numerator|denominator|quotient()|
|5|1|5|
|5|11|5|
|5|111|5|

Updates the dates by 5 days.
!|Increment Columns Value|inDate|of type|date|by|5|
|Get Dates|
|inDate|updatedDate()|
|12/02/2006|12/02/2006|
|12/02/2006|12/07/2006|
|12/22/2006|01/01/2007|

This decorator by itself is not as useful. But piped with other decorators this can be really handy.