/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 3.0.12
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package it.robur.test.physicalDCC.t32api;

public final class etAction {
  public final static etAction actNOP = new etAction("actNOP");
  public final static etAction actResIgnBox = new etAction("actResIgnBox");
  public final static etAction actResetErr = new etAction("actResetErr");
  public final static etAction actRun = new etAction("actRun");
  public final static etAction actStop = new etAction("actStop");
  public final static etAction actRunCircolatore = new etAction("actRunCircolatore");
  public final static etAction actStopCircolatore = new etAction("actStopCircolatore");
  public final static etAction actSwitchPlantState = new etAction("actSwitchPlantState");
  public final static etAction actExcludeModule = new etAction("actExcludeModule");
  public final static etAction actFreeModule = new etAction("actFreeModule");
  public final static etAction actTakeOwnership = new etAction("actTakeOwnership");
  public final static etAction actSetMonitoring = new etAction("actSetMonitoring");
  public final static etAction actManualDefrost = new etAction("actManualDefrost");
  public final static etAction actSetAnalogOut = new etAction("actSetAnalogOut");
  public final static etAction actSetDigitalOut = new etAction("actSetDigitalOut");
  public final static etAction actSetOut = new etAction("actSetOut");
  public final static etAction actSetDefrostingValve = new etAction("actSetDefrostingValve");
  public final static etAction actResetDefrostingValve = new etAction("actResetDefrostingValve");
  public final static etAction actRunWithPowerParam = new etAction("actRunWithPowerParam");
  public final static etAction actRunACSWithPowerParam = new etAction("actRunACSWithPowerParam");
  public final static etAction actRunWithSetpointAndPowerParam = new etAction("actRunWithSetpointAndPowerParam");
  public final static etAction actRunACSWithSetpointAndPowerParam = new etAction("actRunACSWithSetpointAndPowerParam");

  public final int swigValue() {
    return swigValue;
  }

  public String toString() {
    return swigName;
  }

  public static etAction swigToEnum(int swigValue) {
    if (swigValue < swigValues.length && swigValue >= 0 && swigValues[swigValue].swigValue == swigValue)
      return swigValues[swigValue];
    for (int i = 0; i < swigValues.length; i++)
      if (swigValues[i].swigValue == swigValue)
        return swigValues[i];
    throw new IllegalArgumentException("No enum " + etAction.class + " with value " + swigValue);
  }

  private etAction(String swigName) {
    this.swigName = swigName;
    this.swigValue = swigNext++;
  }

  private etAction(String swigName, int swigValue) {
    this.swigName = swigName;
    this.swigValue = swigValue;
    swigNext = swigValue+1;
  }

  private etAction(String swigName, etAction swigEnum) {
    this.swigName = swigName;
    this.swigValue = swigEnum.swigValue;
    swigNext = this.swigValue+1;
  }

  private static etAction[] swigValues = { actNOP, actResIgnBox, actResetErr, actRun, actStop, actRunCircolatore, actStopCircolatore, actSwitchPlantState, actExcludeModule, actFreeModule, actTakeOwnership, actSetMonitoring, actManualDefrost, actSetAnalogOut, actSetDigitalOut, actSetOut, actSetDefrostingValve, actResetDefrostingValve, actRunWithPowerParam, actRunACSWithPowerParam, actRunWithSetpointAndPowerParam, actRunACSWithSetpointAndPowerParam };
  private static int swigNext = 0;
  private final int swigValue;
  private final String swigName;
}

