/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 3.0.12
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package it.robur.test.physicalDCC.t32api;

public class cT32Api {
  private transient long swigCPtr;
  protected transient boolean swigCMemOwn;

  protected cT32Api(long cPtr, boolean cMemoryOwn) {
    swigCMemOwn = cMemoryOwn;
    swigCPtr = cPtr;
  }

  protected static long getCPtr(cT32Api obj) {
    return (obj == null) ? 0 : obj.swigCPtr;
  }

  protected void finalize() {
    delete();
  }

  public synchronized void delete() {
    if (swigCPtr != 0) {
      if (swigCMemOwn) {
        swigCMemOwn = false;
        t32apiJNI.delete_cT32Api(swigCPtr);
      }
      swigCPtr = 0;
    }
  }

  public static void SetNode(String node) {
    t32apiJNI.cT32Api_SetNode(node);
  }

  public static void setPort(String port) {
    t32apiJNI.cT32Api_setPort(port);
  }

  public static String GetNode() {
    return t32apiJNI.cT32Api_GetNode();
  }

  public static String GetPort() {
    return t32apiJNI.cT32Api_GetPort();
  }

  public static void Init() throws java.io.IOException {
    t32apiJNI.cT32Api_Init();
  }

  public static void Exit() {
    t32apiJNI.cT32Api_Exit();
  }

  public static void Cmd(String command) throws java.io.IOException {
    t32apiJNI.cT32Api_Cmd(command);
  }

  public static void CmdWaitEmulation(String command) throws java.io.IOException {
    t32apiJNI.cT32Api_CmdWaitEmulation(command);
  }

  public static String CmdCmmScript(String command) throws java.io.IOException {
    return t32apiJNI.cT32Api_CmdCmmScript(command);
  }

  public static void ExecExpr(String expression) throws java.io.IOException {
    t32apiJNI.cT32Api_ExecExpr(expression);
  }

  public static void ExecExprWait(String expression) throws java.io.IOException {
    t32apiJNI.cT32Api_ExecExprWait(expression);
  }

  public static void ExecFunc(String expression) throws java.io.IOException {
    t32apiJNI.cT32Api_ExecFunc(expression);
  }

  public static String getUserIntThreadFunction() {
    return t32apiJNI.cT32Api_UserIntThreadFunction_get();
  }

  public static String getDecisionThreadFunction() {
    return t32apiJNI.cT32Api_DecisionThreadFunction_get();
  }

  public static void ResetGoUIThread() throws java.io.IOException {
    t32apiJNI.cT32Api_ResetGoUIThread();
  }

  public static void ResetGoNonUIThread(String function) throws java.io.IOException {
    t32apiJNI.cT32Api_ResetGoNonUIThread(function);
  }

  public static void ResetGoDecisionThread() throws java.io.IOException {
    t32apiJNI.cT32Api_ResetGoDecisionThread();
  }

  public static void Go() throws java.io.IOException {
    t32apiJNI.cT32Api_Go();
  }

  public static void BreakOn(String function) throws java.io.IOException {
    t32apiJNI.cT32Api_BreakOn(function);
  }

  public static void BreakOnDecisionThread() throws java.io.IOException {
    t32apiJNI.cT32Api_BreakOnDecisionThread();
  }

  public static int GetFuncValueInt(String funcExpr) throws java.io.IOException {
    return t32apiJNI.cT32Api_GetFuncValueInt(funcExpr);
  }

  public static long GetFuncValueUInt(String funcExpr) throws java.io.IOException {
    return t32apiJNI.cT32Api_GetFuncValueUInt(funcExpr);
  }

  public static int GetExprValueInt(String expression) throws java.io.IOException {
    return t32apiJNI.cT32Api_GetExprValueInt(expression);
  }

  public static long GetExprValueUInt(String expression) throws java.io.IOException {
    return t32apiJNI.cT32Api_GetExprValueUInt(expression);
  }

  public static short GetExprValueShort(String expression) throws java.io.IOException {
    return t32apiJNI.cT32Api_GetExprValueShort(expression);
  }

  public static short GetExprValueTBool(String expression) throws java.io.IOException {
    return t32apiJNI.cT32Api_GetExprValueTBool(expression);
  }

  public static SWIGTYPE_p_void GetExprValuePointer(String expression) throws java.io.IOException {
    long cPtr = t32apiJNI.cT32Api_GetExprValuePointer(expression);
    return (cPtr == 0) ? null : new SWIGTYPE_p_void(cPtr, false);
  }

  public static short GetExprValueEnum(String expression) throws java.io.IOException {
    return t32apiJNI.cT32Api_GetExprValueEnum(expression);
  }

  public cT32Api() {
    this(t32apiJNI.new_cT32Api(), true);
  }

}
