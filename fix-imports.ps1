Write-Host "Correzione import sbagliati..." -ForegroundColor Green

# Trova tutti i file Java che hanno import sbagliati
$files = Get-ChildItem -Recurse "src\*.java"

$replacements = @{
    "import it.robur.jserialtool.Language;" = "import it.robur.strings.Language;"
    "import it.robur.jserialtool.ui.StringGetter;" = "import it.robur.strings.StringGetter;"
    "import it.robur.jserialtool.ConfServer;" = "import it.robur.net.ConfServer;"
}

$totalReplacements = 0

foreach ($file in $files) {
    $content = Get-Content $file.FullName -Raw
    $originalContent = $content
    
    foreach ($oldImport in $replacements.Keys) {
        $newImport = $replacements[$oldImport]
        if ($content -match [regex]::Escape($oldImport)) {
            $content = $content -replace [regex]::Escape($oldImport), $newImport
            Write-Host "Corretto import in: $($file.Name)" -ForegroundColor Yellow
            $totalReplacements++
        }
    }
    
    # Aggiungi import mancanti per JSONException se necessario
    if ($content -match "JSONException" -and $content -notmatch "import org.json.JSONException;") {
        if ($content -match "import org.json.JSONObject;") {
            $content = $content -replace "import org.json.JSONObject;", "import org.json.JSONObject;`nimport org.json.JSONException;"
            Write-Host "Aggiunto import JSONException in: $($file.Name)" -ForegroundColor Cyan
            $totalReplacements++
        }
    }
    
    if ($content -ne $originalContent) {
        Set-Content -Path $file.FullName -Value $content -NoNewline
    }
}

Write-Host "Completato! Effettuate $totalReplacements correzioni." -ForegroundColor Green
