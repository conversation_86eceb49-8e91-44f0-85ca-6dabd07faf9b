If a !-SuiteSetUp-! or !-SuiteTearDown-! page is eecuted without errors, it should be counted as a correctly executed page.

!define TEST_SYSTEM {slim}

!**< test content

!define PATHS {
${SUT_PATH}
!-!define TEST_SYSTEM (slim)-!
}

!define MOCK_TEST {
!-| script | fitnesse.fixtures.Pass |-!
!-| ensure | ok |-!
}
*!

!|Library|
|page driver|
|echo fixture|

!|given page with content |
|page |content |
|SuiteParent |${PATHS} |
|SuiteParent.SuiteSetUp | |
|SuiteParent.SuiteTearDown | |
|SuiteParent.TestOneOne | ${MOCK_TEST} |
|SuiteParent.TestOneTwo | ${MOCK_TEST} |

For normal (HTML) responses:

!|script |
| check | request page | SuiteParent?suite | 200 |
| show | content |
| ensure | content contains | <strong>Test Pages:</strong> 4 right, 0 wrong, 0 ignored, 0 exceptions |
| ensure | content matches | 0 right, 0 wrong, 0 ignored, 0 exceptions.*>SuiteSetUp</a> |
| ensure | content matches | 0 right, 0 wrong, 0 ignored, 0 exceptions.*>SuiteTearDown</a> |

Same for XML:

!|script |
| check | request page | SuiteParent?suite&format=xml | 200 |
| show | content |
| ensure | content matches | <finalCounts>.*<right>.*4.*</right>.*<wrong>.*0.*</wrong>.*<ignores>.*0.*</ignores>.*<exceptions>.*0.*</exceptions>.*</finalCounts>  |

