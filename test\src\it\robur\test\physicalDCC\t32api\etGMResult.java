/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 3.0.12
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package it.robur.test.physicalDCC.t32api;

public final class etGMResult {
  public final static etGMResult gmOk = new etGMResult("gmOk");
  public final static etGMResult gmOkButSecondaryArbitration = new etGMResult("gmOkButSecondaryArbitration");
  public final static etGMResult gmOkButAvailableWhileOppositeModeReset = new etGMResult("gmOkButAvailableWhileOppositeModeReset");
  public final static etGMResult gmUnknown = new etGMResult("gmUnknown");
  public final static etGMResult gmWrongPhase = new etGMResult("gmWrongPhase");
  public final static etGMResult gmRunningPlant = new etGMResult("gmRunningPlant");
  public final static etGMResult gmWrongPlant = new etGMResult("gmWrongPlant");
  public final static etGMResult gmLimitExceeded = new etGMResult("gmLimitExceeded");
  public final static etGMResult gmCannotCommit = new etGMResult("gmCannotCommit");
  public final static etGMResult gmNotMaster = new etGMResult("gmNotMaster");
  public final static etGMResult gmOutOfRange = new etGMResult("gmOutOfRange");
  public final static etGMResult gmInstNotEnabled = new etGMResult("gmInstNotEnabled");
  public final static etGMResult gmNotTwoPiped = new etGMResult("gmNotTwoPiped");
  public final static etGMResult gmNotCustomWMode = new etGMResult("gmNotCustomWMode");
  public final static etGMResult gmNotOwner = new etGMResult("gmNotOwner");
  public final static etGMResult gmNotPresent = new etGMResult("gmNotPresent");
  public final static etGMResult gmNotConnected = new etGMResult("gmNotConnected");
  public final static etGMResult gmCannotWrite = new etGMResult("gmCannotWrite");
  public final static etGMResult gmPendingRequest = new etGMResult("gmPendingRequest");
  public final static etGMResult gmNotAllowed = new etGMResult("gmNotAllowed");
  public final static etGMResult gmNotCompatible = new etGMResult("gmNotCompatible");
  public final static etGMResult gmThereAreNoWW = new etGMResult("gmThereAreNoWW");
  public final static etGMResult gmNotRunning = new etGMResult("gmNotRunning");
  public final static etGMResult gmHeatNotRunning = new etGMResult("gmHeatNotRunning");
  public final static etGMResult gmNoMasterFound = new etGMResult("gmNoMasterFound");
  public final static etGMResult gmIllegalRYWAuto = new etGMResult("gmIllegalRYWAuto");
  public final static etGMResult gmInTherm = new etGMResult("gmInTherm");
  public final static etGMResult gmDontCare = new etGMResult("gmDontCare");
  public final static etGMResult gmOnNotAllowedBecauseOfTwoPipedUnits = new etGMResult("gmOnNotAllowedBecauseOfTwoPipedUnits");
  public final static etGMResult gmTwoPipedUnitsOnSeparable = new etGMResult("gmTwoPipedUnitsOnSeparable");
  public final static etGMResult gmGAHPWUnitsOnSeparable = new etGMResult("gmGAHPWUnitsOnSeparable");
  public final static etGMResult gmTwoPipedButNoIncludentGroup = new etGMResult("gmTwoPipedButNoIncludentGroup");
  public final static etGMResult gmTakenIOModRole = new etGMResult("gmTakenIOModRole");
  public final static etGMResult gmNoUnitsOnPlant = new etGMResult("gmNoUnitsOnPlant");
  public final static etGMResult gmSepValveOnOnlySecondary = new etGMResult("gmSepValveOnOnlySecondary");
  public final static etGMResult gmNotAllowedOnNonRoburUnit = new etGMResult("gmNotAllowedOnNonRoburUnit");
  public final static etGMResult gmNoLowOrNoHighEffUnits = new etGMResult("gmNoLowOrNoHighEffUnits");
  public final static etGMResult gmNotForChilPlant = new etGMResult("gmNotForChilPlant");
  public final static etGMResult gmOnlyForTemperatureIntegrationScenario = new etGMResult("gmOnlyForTemperatureIntegrationScenario");
  public final static etGMResult gmSetSeparableGroupTypeFirst = new etGMResult("gmSetSeparableGroupTypeFirst");

  public final int swigValue() {
    return swigValue;
  }

  public String toString() {
    return swigName;
  }

  public static etGMResult swigToEnum(int swigValue) {
    if (swigValue < swigValues.length && swigValue >= 0 && swigValues[swigValue].swigValue == swigValue)
      return swigValues[swigValue];
    for (int i = 0; i < swigValues.length; i++)
      if (swigValues[i].swigValue == swigValue)
        return swigValues[i];
    throw new IllegalArgumentException("No enum " + etGMResult.class + " with value " + swigValue);
  }

  private etGMResult(String swigName) {
    this.swigName = swigName;
    this.swigValue = swigNext++;
  }

  private etGMResult(String swigName, int swigValue) {
    this.swigName = swigName;
    this.swigValue = swigValue;
    swigNext = swigValue+1;
  }

  private etGMResult(String swigName, etGMResult swigEnum) {
    this.swigName = swigName;
    this.swigValue = swigEnum.swigValue;
    swigNext = this.swigValue+1;
  }

  private static etGMResult[] swigValues = { gmOk, gmOkButSecondaryArbitration, gmOkButAvailableWhileOppositeModeReset, gmUnknown, gmWrongPhase, gmRunningPlant, gmWrongPlant, gmLimitExceeded, gmCannotCommit, gmNotMaster, gmOutOfRange, gmInstNotEnabled, gmNotTwoPiped, gmNotCustomWMode, gmNotOwner, gmNotPresent, gmNotConnected, gmCannotWrite, gmPendingRequest, gmNotAllowed, gmNotCompatible, gmThereAreNoWW, gmNotRunning, gmHeatNotRunning, gmNoMasterFound, gmIllegalRYWAuto, gmInTherm, gmDontCare, gmOnNotAllowedBecauseOfTwoPipedUnits, gmTwoPipedUnitsOnSeparable, gmGAHPWUnitsOnSeparable, gmTwoPipedButNoIncludentGroup, gmTakenIOModRole, gmNoUnitsOnPlant, gmSepValveOnOnlySecondary, gmNotAllowedOnNonRoburUnit, gmNoLowOrNoHighEffUnits, gmNotForChilPlant, gmOnlyForTemperatureIntegrationScenario, gmSetSeparableGroupTypeFirst };
  private static int swigNext = 0;
  private final int swigValue;
  private final String swigName;
}

