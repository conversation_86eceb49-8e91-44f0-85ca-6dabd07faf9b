fieldset {
	border: none;
	padding: 0;
    margin: 0 0 0.5em 0;
}
label {
    display: inline-block;
    text-align: right;
    width: 140px;
}
.validationerror {
    color: maroon;
    margin: -6px 0 8px 140px;
    font-size: 0.9em
}
.buttons {
    margin-left: 145px; /* 140 + input field padding/border/margin */
}

/** Wiki pages (wiki, test, suite, report) **/
.wikiPage td,
.testPage td,
.testExecutionReport td,
.compareHistory td {
  white-space: pre-line;
}

/** Edit page **/
.editPage {
    padding-bottom: 0;
}

.editPage article {
    margin: 0;
}

.editPage #editor label {
	width: auto;
}
.editPage .edit_options label {
	width: auto;
}

.editPage #pagename,
.editPage #helptext {
	width: 55em;
}
.editPage #pagetypes label {
	display: inline;
	float: none;
}
.editPage #pagetypes label:first-child {
    margin-left: 145px;
}
.editPage textarea {
	display: block;
	width: 100%;
	height: 32em;
    font-family: monospace;
}
.editPage textarea.no_wrap {
    overflow: scroll;
}
.editPage .edit_options {
	float: right;
}
.editPage #save_buttons {
    bottom: 0;
    margin-top: 9px;
    position: fixed;
}

.tagsinput {
    display: inline;
}
.tag {
    background-color: #F9F9F9;
    border: 1px dotted #BF8660;
    color: #BF8660;
    margin-right: 4px;
    padding: 2px;
    text-decoration: none;
}
.tag a {
    font-weight: bold;
    text-decoration: none;
    margin-left: 4px;
}


/** Properties page / Search form / Directory page **/
.properties {
    margin-left: 145px;
}
.properties fieldset {
    display: inline-block;
    vertical-align: top;
	border: none;
}
.properties label {
	display: block;
    text-align: left;
}
legend {
	font-weight: bold;
	margin-bottom: 0.5em;
	padding: 0;
}
label.checkbox {
	margin-left: 145px;
	width: auto;
    text-align: left;
}
.virtual-wiki-properties {
	margin-top: 1em;
}
#create-dir {
	width: 140px;
}

/** Merge page **/
.mergePage #merge-new,
.mergePage #merge-old {
	float: left;
	width: 47%;
	margin-right: 1em;
}
.mergePage textarea {
	width: 100%;
	height: 32em;
}
.mergePage #merge-old textarea{
	background-color: #efefef;
}

/** Delete page confirmation dialog **/
.warning {
	color: maroon;
}

/** Test history **/
.testHistory td {
    white-space: pre;
}
.testHistory td a {
	text-decoration: none;
}

/** Test result **/
tr.scenario > td:first-child,
tr.exception > td:first-child {
	padding-left: 16px;
	background-image: url("../images/collapsibleOpen.png");
	background-position: 2px;
	background-repeat: no-repeat;
}
tr.scenario.closed > td:first-child,
tr.exception.closed > td:first-child {
	background-image: url("../images/collapsibleClosed.png");
}
tr.closed-detail {
	display: none;
}
tr.exception-detail {
    background-color: #ffffaa;
}

#progressBar,
#test-summary.pass,
#test-summary.fail,
#test-summary.error {
    padding: 0.5em;
    white-space: nowrap;
}
#test-summary.pass,
#test-summary.fail,
#test-summary.error {
    padding-left: 1.5em;
}

#test-summaries ul {
	list-style: none;
	padding-left: 0;
}
#test_summaries h2, #test-summaries h3 {
	text-align: center;
}
#test-summaries .pass,
#test-summaries .fail,
#test-summaries .error,
span.error {
    padding: 0px 0px 0px 20px;
}

#test-summary.pass,
#test-summary.fail,
#test-summary.error,
#test-summaries .pass,
#test-summaries .fail,
#test-summaries .error,
span.error {
  background-repeat: no-repeat;
  background-position: 0 center;
}

#test-summary.pass,
#test-summaries .pass {
  background-image: url("../images/checkmark.png");
}
#test-summary.fail,
#test-summaries .fail {
  background-image: url("../images/wrong.png");
}
#test-summary.error,
#test-summaries .error,
span.error {
  background-image: url("../images/exception.png");
}
#test-action .stop {
	background-color: red;
	color: white;
	font-weight: bold;
}
#test-action .error {
	background-color: red;
}
/* Keep around for old pages: */
.test_output_name h3 {
	display: inline;
}

/** Suite overview **/
.overview_results table {
    border: none;
}
.overview_bar {
    border: 1px solid black;
    height: 1.4em;
    margin: 0 -0.5px;
}
.overview_part {
    float: left;
    height: 1.4em;
    margin: 0;
    padding: 0;
}
.unrun {
    background-color: #FFF3B4;
}

.passed_tests, .unrun_tests, .failed_tests {
    background-repeat: no-repeat;
    margin: 0 4px;
    padding-left: 22px;
}
.passed_tests {
    background-image: url("../images/checkmark.png");
}
.unrun_tests {
    background-image: url("../images/exception.png");
}
.failed_tests {
    background-image: url("../images/wrong.png");
}

/** Imported pages */
.imported header {
	background-image: url("../images/importedPage.jpg");
	background-position: center top;
	background-repeat: no-repeat;
}

/** Test Execution report, backwards compatibility **/
.collapse_rim {
  background: #F9F9F9;
  border: 1px dotted #909090;
}
.collapse_rim > a {
  padding-left: 16px;
  background: url(../images/collapsibleClosed.png) no-repeat scroll 0 2px transparent;
}
.collapse_rim.open > a {
  background: url(../images/collapsibleOpen.png) no-repeat scroll 0 2px transparent;
}

/** Table of Contents **/
.contents a.pruned {
  text-decoration: line-through;
}
.contents a.suite {
  font-weight: bold;
}
.contents a.static {
  font-style: italic;
}

.CodeMirror {
  border: 1px solid #d7d7d7;
}

/** Modal dialog **/
.modal-dialog:before{
    content: "";
    display: none;
    background: rgba(0,0,0,.2);
    position: fixed;
    top: 0; left: 0; right: 0; bottom: 0;
    z-index: 2000;
}

.modal-dialog:target:before {
    display: block;
}

.modal-dialog > div {
    display: none;
    position: fixed;
    background-color: white;
    border: 1px solid #a6a6a6;
    padding: 8px 16px;
    box-shadow: 0 2px 2px rgba(0, 0, 0, 0.2);
    border-radius: 4px;
    margin: auto;
    margin-left: 50%;
    z-index: 2001;
    top: 50%;
    -webkit-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
}
.modal-dialog:target > div {
    display: block;
}

.help-list {
    list-style: none;
    display: inline-block;
    padding: 0;
}