package it.robur.jsonui;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;

import it.robur.ddc.DDC;
import it.robur.ddc.config.DDCConfig;
import it.robur.strings.StringGetter;
import it.robur.ui.Strings;

public class DDCConfigJSONUIWindow
{
	// Generated by completeJSONUIWindow.py
	// Manual edits will be replaced.
	public final static String c_title_visible_id = "c_title_visible";
	public final static String h_title_visible_id = "h_title_visible";
	public final static String c_or_ch_plant_layout_visible_id = "c_or_ch_plant_layout_visible";
	public final static String h_plant_layout_visible_id = "h_plant_layout_visible";
	public final static String c_or_ch_title_id = "c_or_ch_title";
	public final static String water_pump_mode_id = "water_pump_mode";
	public final static String c_or_ch_superservice_layout_id = "c_or_ch_superservice_layout";
	public final static String h_superservice_layout_id = "h_superservice_layout";
	public final static String spinner_working_mode_id = "spinner_working_mode";
	public final static String c_subplant_layout_id = "c_subplant_layout";
	public final static String h_subplant_layout_id = "h_subplant_layout";
	public final static String spinner_thermostatation_mode_id = "spinner_thermostatation_mode";

	// completeJSONUIWindow.py will parse and modify the following fields to add xxx_id constant strings, ensure they are properly referenced in field construction, and setWidgets() method.
	// Manually modify or insert here, specifying just "protected final <type> <name>" 
	protected final JSONUIVisible	c_title_visible = new JSONUIVisible(c_title_visible_id);
	protected final JSONUIVisible	h_title_visible = new JSONUIVisible(h_title_visible_id);
	protected final JSONUIVisible	c_or_ch_plant_layout_visible = new JSONUIVisible(c_or_ch_plant_layout_visible_id);
	protected final JSONUIVisible	h_plant_layout_visible = new JSONUIVisible(h_plant_layout_visible_id);
	protected final JSONUISetter	c_or_ch_title = new JSONUISetter(c_or_ch_title_id);
	protected final JSONUIGetterSetterWithItems	water_pump_mode = new JSONUIGetterSetterWithItems(water_pump_mode_id);
	protected final JSONUIVisible	c_or_ch_superservice_layout = new JSONUIVisible(c_or_ch_superservice_layout_id);
	protected final JSONUIVisible	h_superservice_layout = new JSONUIVisible(h_superservice_layout_id);
	protected final JSONUIGetterSetterWithItems	spinner_working_mode = new JSONUIGetterSetterWithItems(spinner_working_mode_id);
	protected final JSONUIVisible	c_subplant_layout = new JSONUIVisible(c_subplant_layout_id);
	protected final JSONUIVisible	h_subplant_layout = new JSONUIVisible(h_subplant_layout_id);
	protected final JSONUIGetterSetterWithItems	spinner_thermostatation_mode = new JSONUIGetterSetterWithItems(spinner_thermostatation_mode_id);

	/**
	 * Generated by completeJSONUIWindow.py
	 * Manual edits will be replaced.
	 */
	protected void setWidgets() throws JSONException
	{
		widgets.put(c_title_visible_id, c_title_visible);
		widgets.put(h_title_visible_id, h_title_visible);
		widgets.put(c_or_ch_plant_layout_visible_id, c_or_ch_plant_layout_visible);
		widgets.put(h_plant_layout_visible_id, h_plant_layout_visible);
		widgets.put(c_or_ch_title_id, c_or_ch_title);
		widgets.put(water_pump_mode_id, water_pump_mode);
		widgets.put(c_or_ch_superservice_layout_id, c_or_ch_superservice_layout);
		widgets.put(h_superservice_layout_id, h_superservice_layout);
		widgets.put(spinner_working_mode_id, spinner_working_mode);
		widgets.put(c_subplant_layout_id, c_subplant_layout);
		widgets.put(h_subplant_layout_id, h_subplant_layout);
		widgets.put(spinner_thermostatation_mode_id, spinner_thermostatation_mode);
	}

    // End of completeJSONUIWindow.py sections


    protected DDCConfig ddcConfig = null;
    protected Map<String, JSONUIValidable> widgets = new HashMap<String, JSONUIValidable>();

    public DDCConfigJSONUIWindow(JSONObject identif, JSONObject recConfig) throws JSONException
    {
        StringGetter sg = new StringGetter();

        setWidgets();

        // Actual instantation

        final boolean readOnly = false;

        boolean coolTitleAlreadySet = false;
        c_title_visible.setVisibility(false);
        h_title_visible.setVisibility(false);

        DDCConfig.Builder builder = new DDCConfig.Builder();
        
        builder
                .setStringG( sg );


        // NOTE: Entities potentially chil-heat-two-pipes must be set before the other, to properly set coolTitleAlreadySet

        // Plants

        c_or_ch_plant_layout_visible.setVisibility(false);
        h_plant_layout_visible.setVisibility(false);
        JSONUIVisible plantViewVisible;
        for( DDC.Plant.Type type : DDC.Plant.Type.values() )
            if( DDCConfig.Builder.isPlantInConfig(recConfig, type) )
            {
                switch(type)
                {
                    case COOL_HEAT:
                        plantViewVisible = c_or_ch_plant_layout_visible;
                        c_or_ch_title.setValue( sg.getString(Strings.caldo_e_freddo_due_tubi) );
                        break;

                    case COOL:
                        plantViewVisible = c_or_ch_plant_layout_visible;
                        c_or_ch_title.setValue( sg.getString(Strings.c_cool) );
                        coolTitleAlreadySet = true;
                        break;

                    case HEAT:
                        plantViewVisible = h_plant_layout_visible;
                        h_title_visible.setVisibility(true);
                        break;

                    default:
                        continue;
                }

                plantViewVisible.setVisibility(true);

                builder
                        .addPlantGS(type)
                        .setWaterPumpModeGS(type, water_pump_mode);
            }


        // Superservices

        c_or_ch_superservice_layout.setVisibility(false);
        h_superservice_layout.setVisibility(false);
        JSONUIVisible superserviceViewVisible;
        for( DDC.SuperService.Type type : DDC.SuperService.Type.values() )
            if( DDCConfig.Builder.isSuperServiceInConfig(recConfig, type) )
            {
                switch(type)
                {
                    case SPACE_COOLING_HEATING:
                        superserviceViewVisible = c_or_ch_superservice_layout;
                        break;

                    case SPACE_COOLING:
                        superserviceViewVisible = c_or_ch_superservice_layout;
                        break;

                    case SPACE_HEATING:
                        superserviceViewVisible = h_superservice_layout;
                        break;

                    default:
                        continue;
                }

                superserviceViewVisible.setVisibility(true);


                builder
                        .addSuperServiceGS(type)
                        .setWorkingModeGS(type, spinner_working_mode);
            }


        c_subplant_layout.setVisibility(false);
        h_subplant_layout.setVisibility(false);
        JSONUIVisible subPlantViewVisible;
        for( DDC.SubPlant.Type type : DDC.SubPlant.Type.values() )
            if( DDCConfig.Builder.isSubPlantInConfig(recConfig, type) )
            {
                switch(type)
                {
                    case COOL:
                        subPlantViewVisible = c_subplant_layout;

                        if( ! coolTitleAlreadySet )
                            c_title_visible.setVisibility(true);

                        break;

                    case HEAT:
                        subPlantViewVisible = h_subplant_layout;

                        h_title_visible.setVisibility(true);
                        break;

                    default:
                        continue;
                }

                subPlantViewVisible.setVisibility(true);

                builder
                        .addSubPlantGS(type)
                        .setThermostatationModeGS(type, spinner_thermostatation_mode);
            }


        // TODO:
//        findViewById(R.id.c_service_layout).setVisibility(false);
//        findViewById(R.id.h_service_layout).setVisibility(false);
//        View serviceView;
//        for( DDC.Service.Type type : DDC.Service.Type.values() )
//            if( DDCConfig.Builder.isServiceInConfig(recConfig, type) )
//            {
//                switch(type)
//                {
//                    case COOLING:
//                        serviceView = findViewById(R.id.c_service_layout);
//                        break;
//
//                    case SPACE_HEATING:
//                        serviceView = findViewById(R.id.h_service_layout);
//                        break;
//
//                    default:
//                        continue;
//                }
//
//                serviceView.setVisibility(true);
//
//                builder
//                        .addServiceGS(type);
//
//                if( DDCConfig.hasDefaultWaterSetpointToBeSet(recConfig, type) )
//                    builder.setDefaultWaterSetpointGS(type,
//                            new TextWatcherValidatorSetter(this, serviceView,
//                                    R.id.edit_default_water_setpoint, R.id.edit_default_water_setpoint_layout, readOnly));
//                else
//                    serviceView.findViewById(R.id.edit_default_water_setpoint_layout).setVisibility(false);
//
//                if( DDCConfig.hasChronoThermostatToBeSet(recConfig, type) )
//                    builder
//                            .setChronoThermostatT3GS(type,
//                                    new TextWatcherValidatorSetter(this, serviceView,
//                                            R.id.edit_chrono_thermostat_t3, R.id.edit_chrono_thermostat_t3_layout, readOnly));
//                else
//                {
//                    serviceView.findViewById(R.id.edit_chrono_thermostat_t3_title).setVisibility(false);
//                    serviceView.findViewById(R.id.edit_chrono_thermostat_t3_layout).setVisibility(false);
//                }
//
//                if( type == DDC.Service.Type.COOLING || type == DDC.Service.Type.SPACE_HEATING )
//                {
//                    builder
//                            .setWeatherCompEnableGS(type,
//                                    new SwitchGetterSetter(this, serviceView,
//                                            R.id.toggle_weather_comp_enable, null, readOnly))
//                            .setWeatherCompMinInSetpGS(type,
//                                    new TextWatcherValidatorSetter(this, serviceView,
//                                            R.id.edit_weather_comp_min_in_setp, R.id.edit_weather_comp_min_in_setp_layout, readOnly))
//
//                            .setWeatherCompMaxInSetpGS(type,
//                                    new TextWatcherValidatorSetter(this, serviceView,
//                                            R.id.edit_weather_comp_max_in_setp, R.id.edit_weather_comp_max_in_setp_layout, readOnly))
//
//                            .setWeatherCompMinOutSetpGS(type,
//                                    new TextWatcherValidatorSetter(this, serviceView,
//                                            R.id.edit_weather_comp_min_out_setp, R.id.edit_weather_comp_min_out_setp_layout, readOnly))
//
//                            .setWeatherCompMaxOutSetpGS(type,
//                                    new TextWatcherValidatorSetter(this, serviceView,
//                                            R.id.edit_weather_comp_max_out_setp, R.id.edit_weather_comp_max_out_setp_layout, readOnly));
//
//                    builder
//                            .setWeatherCompOffsetGS(type,
//                                    new TextWatcherValidatorSetter(this, serviceView,
//                                            R.id.edit_weather_comp_offset, R.id.edit_weather_comp_offset_layout, readOnly));
//
//                    if( type == DDC.Service.Type.COOLING  )
//                    {
//                        builder
//                                .setWeatherCompTF1GS(new TextWatcherValidatorSetter(this, serviceView,
//                                        R.id.edit_weather_comp_TF1, R.id.edit_weather_comp_TF1_layout, readOnly))
//                                .setWeatherCompTF2GS(new TextWatcherValidatorSetter(this, serviceView,
//                                        R.id.edit_weather_comp_TF2, R.id.edit_weather_comp_TF2_layout, readOnly));
//                    }
//                    else
//                    {
//                        serviceView.findViewById(R.id.edit_weather_comp_TF1_layout).setVisibility(false);
//                        serviceView.findViewById(R.id.edit_weather_comp_TF2_layout).setVisibility(false);
//                    }
//
//                    if( type == DDC.Service.Type.SPACE_HEATING  )
//                    {
//                        builder
//                                .setWeatherCompSlopeGS(new TextWatcherValidatorSetter(this, serviceView,
//                                        R.id.edit_weather_comp_slope, R.id.edit_weather_comp_slope_layout, readOnly));
//                    }
//                    else
//                    {
//                        serviceView.findViewById(R.id.edit_weather_comp_slope_layout).setVisibility(false);
//                    }
//
//
//                    if( ! DDCConfig.hasWeatherCompensToBeSet(recConfig, type) )
//                    {
//                        serviceView.findViewById(R.id.edit_weather_comp_offset_layout).setVisibility(false);
//                        serviceView.findViewById(R.id.edit_weather_comp_TF1_layout).setVisibility(false);
//                        serviceView.findViewById(R.id.edit_weather_comp_TF2_layout).setVisibility(false);
//                        serviceView.findViewById(R.id.edit_weather_comp_slope_layout).setVisibility(false);
//                    }
//                }
//            }
//
//
//        findViewById(R.id.c_circuit_layout).setVisibility(false);
//        findViewById(R.id.h_circuit_layout).setVisibility(false);
//        View circuitView;
//        for( DDC.Circuit.Type type : DDC.Circuit.Type.values() )
//            if( DDCConfig.Builder.isCircuitInConfig(recConfig, type) )
//            {
//                switch(type)
//                {
//                    case COOL:
//                        circuitView = findViewById(R.id.c_circuit_layout);
//                        break;
//
//                    case HEAT:
//                        circuitView = findViewById(R.id.h_circuit_layout);
//                        break;
//
//                    default:
//                        continue;
//                }
//
//                circuitView.setVisibility(true);
//
//                builder.addCircuitGS(type);
//
//                if( DDCConfig.hasInitPowerPercentageToBeSet(recConfig, type) )
//                    builder.setInitPowerPercentageGS(type,
//                            new TextWatcherValidatorSetter(this, circuitView,
//                                    R.id.edit_init_power_percentage, R.id.edit_init_power_percentage_layout, readOnly));
//                else( (JSONUIVisible)widgets.get(
//                    circuitView.findViewById(R.id.edit_init_power_percentage_layout).setVisibility(false);
//
//                if( DDCConfig.hasDifferentialToBeSet(recConfig, type) )
//                    builder.setDifferentialGS(type,
//                            new TextWatcherValidatorSetter(this, circuitView,
//                                    R.id.edit_differential, R.id.edit_differential_layout, readOnly));
//                else
//                    circuitView.findViewById(R.id.edit_differential_layout).setVisibility(false);
//
//                View categoryView;
//                for( String c : new String[]{"1", "3"} )
//                {
//                    @IdRes int categoryId = getResources().getIdentifier("category_" + c + "_layout", "id", getPackageName());
//                    categoryView = circuitView.findViewById(categoryId);
//
//                    if( DDCConfig.Builder.isCategoryInConfig(recConfig, type, c) )
//                    {
//                        ((TextView)categoryView.findViewById(R.id.text_category_id)).setText(c);
//
//                        builder
//                                .addCategoryGS(type, c)
//                                .setCategoryNumOfStagesGS(type, c,
//                                        new TextWatcherValidatorSetter(this, categoryView,
//                                                R.id.edit_num_of_stages, R.id.edit_num_of_stages_layout, readOnly))
//                                .setCategoryMinRunTimeGS(type, c,
//                                        new TextWatcherValidatorSetter(this, categoryView,
//                                                R.id.edit_min_run_time, R.id.edit_min_run_time_layout, readOnly))
//                                .setCategoryResetIntegralGS(type, c,
//                                        new TextWatcherValidatorSetter(this, categoryView,
//                                                R.id.edit_reset_integral, R.id.edit_reset_integral_layout, readOnly))
//                                .setCategoryReleaseIntegralGS(type, c,
//                                        new TextWatcherValidatorSetter(this, categoryView,
//                                                R.id.edit_release_integral, R.id.edit_release_integral_layout, readOnly));
//
//                    }
//                    else
//                    {
//                        categoryView.setVisibility(false);( (JSONUIVisible)widgets.get(
//                    }
//                }
//            }

        this.ddcConfig = builder.build();

    }

    /**
     *
     * @param identif
     * @param config
     * @return JSON representing widgets' state
     * @throws JSONException
     */
    public JSONObject load(JSONObject identif, JSONObject config) throws JSONException
    {
        ddcConfig.loadJSON(/* TODO:? identif, */ config, false);

        return toJSON();
    }

    public void restore(JSONObject identif, JSONObject config, JSONObject window) throws JSONException
    {
        ddcConfig.loadJSON(/* TODO: identif? ,*/ config, false);

        for (Map.Entry<String, JSONUIValidable> widget : widgets.entrySet())
        {
            widget.getValue().fromJSON(
                    window.getJSONObject( widget.getKey() ));
        }
    }

    /**
     *
     * @param widget: id and value are enough
     * @return JSON representing widgets' state
     * @throws JSONException
     */
    public JSONObject update(JSONObject widget) throws JSONException
    {
        ( (JSONUIGetterSetter) widgets.get( widget.getString("id")) )
                .setValue( widget.getString("value") );

        return toJSON();
    }

    /**
     * @param identif
     * @param recConfig
     * @return JSON to be sent to Roburberry
     * @throws JSONException
     */
    public JSONObject save(JSONObject identif, JSONObject recConfig) throws JSONException
    {
        return ddcConfig.saveJSON(/* identif, */ recConfig, false);
    }

    protected JSONObject toJSON() throws JSONException
    {
        JSONObject state = new JSONObject();

        for (Map.Entry<String, JSONUIValidable> widget : widgets.entrySet())
        {
            state.put(
                    widget.getKey(),
                    widget.getValue().toJSON() );
        }

        return state;
    }

}
