/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 3.0.12
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package it.robur.test.physicalDCC.t32api;

public final class etResIgnBoxResult {
  public final static etResIgnBoxResult rrNone = new etResIgnBoxResult("rrNone");
  public final static etResIgnBoxResult rrOk = new etResIgnBoxResult("rrOk");
  public final static etResIgnBoxResult rrLocked = new etResIgnBoxResult("rrLocked");
  public final static etResIgnBoxResult rrDamaged = new etResIgnBoxResult("rrDamaged");
  public final static etResIgnBoxResult rrTooManyResets = new etResIgnBoxResult("rrTooManyResets");
  public final static etResIgnBoxResult rrTimeoutExceeded = new etResIgnBoxResult("rrTimeoutExceeded");
  public final static etResIgnBoxResult rrNoLockout = new etResIgnBoxResult("rrNoLockout");
  public final static etResIgnBoxResult rrNotAcceptedDueToEN14459 = new etResIgnBoxResult("rrNotAcceptedDueToEN14459");

  public final int swigValue() {
    return swigValue;
  }

  public String toString() {
    return swigName;
  }

  public static etResIgnBoxResult swigToEnum(int swigValue) {
    if (swigValue < swigValues.length && swigValue >= 0 && swigValues[swigValue].swigValue == swigValue)
      return swigValues[swigValue];
    for (int i = 0; i < swigValues.length; i++)
      if (swigValues[i].swigValue == swigValue)
        return swigValues[i];
    throw new IllegalArgumentException("No enum " + etResIgnBoxResult.class + " with value " + swigValue);
  }

  private etResIgnBoxResult(String swigName) {
    this.swigName = swigName;
    this.swigValue = swigNext++;
  }

  private etResIgnBoxResult(String swigName, int swigValue) {
    this.swigName = swigName;
    this.swigValue = swigValue;
    swigNext = swigValue+1;
  }

  private etResIgnBoxResult(String swigName, etResIgnBoxResult swigEnum) {
    this.swigName = swigName;
    this.swigValue = swigEnum.swigValue;
    swigNext = this.swigValue+1;
  }

  private static etResIgnBoxResult[] swigValues = { rrNone, rrOk, rrLocked, rrDamaged, rrTooManyResets, rrTimeoutExceeded, rrNoLockout, rrNotAcceptedDueToEN14459 };
  private static int swigNext = 0;
  private final int swigValue;
  private final String swigName;
}

