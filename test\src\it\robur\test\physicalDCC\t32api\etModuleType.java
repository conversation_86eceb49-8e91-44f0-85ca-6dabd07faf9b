/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 3.0.12
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package it.robur.test.physicalDCC.t32api;

public final class etModuleType {
  public final static etModuleType mtACF_USA_STD = new etModuleType("mtACF_USA_STD");
  public final static etModuleType mtACF_USA_HT = new etModuleType("mtACF_USA_HT");
  public final static etModuleType mtACF_USA_R = new etModuleType("mtACF_USA_R");
  public final static etModuleType mtACF_ITA_STD = new etModuleType("mtACF_ITA_STD");
  public final static etModuleType mtACF_ITA_HT = new etModuleType("mtACF_ITA_HT");
  public final static etModuleType mtACF_ITA_LB = new etModuleType("mtACF_ITA_LB");
  public final static etModuleType mtACF_ITA_TK = new etModuleType("mtACF_ITA_TK");
  public final static etModuleType mtAY_Apen_Farel = new etModuleType("mtAY_Apen_Farel");
  public final static etModuleType mtAY_Robur = new etModuleType("mtAY_Robur");
  public final static etModuleType mtWW_ITA_STD = new etModuleType("mtWW_ITA_STD");
  public final static etModuleType mtGAHPA_ITA_STD = new etModuleType("mtGAHPA_ITA_STD");
  public final static etModuleType mtGAHPAR_ITA_STD = new etModuleType("mtGAHPAR_ITA_STD");
  public final static etModuleType mtWW_ITA_LB = new etModuleType("mtWW_ITA_LB");
  public final static etModuleType mtACFHR_ITA_STD = new etModuleType("mtACFHR_ITA_STD");
  public final static etModuleType mtHeatRecuperator = new etModuleType("mtHeatRecuperator");
  public final static etModuleType mtWW_ITA_STD_Modulating = new etModuleType("mtWW_ITA_STD_Modulating");
  public final static etModuleType mtWW_ITA_LB_Modulating = new etModuleType("mtWW_ITA_LB_Modulating");
  public final static etModuleType mtGAHPAR_ITA_STD_Modulating = new etModuleType("mtGAHPAR_ITA_STD_Modulating");
  public final static etModuleType mtGAHPA_ITA_STD_Modulating = new etModuleType("mtGAHPA_ITA_STD_Modulating");
  public final static etModuleType mtAY_Robur_Condensing = new etModuleType("mtAY_Robur_Condensing");
  public final static etModuleType mtWW_ITA_WS_Modulating = new etModuleType("mtWW_ITA_WS_Modulating");
  public final static etModuleType mtGAHPA_ITA_LT_Modulating = new etModuleType("mtGAHPA_ITA_LT_Modulating");
  public final static etModuleType mtNonRoburWithCircAndError = new etModuleType("mtNonRoburWithCircAndError");
  public final static etModuleType mtNonRoburWithCircAndErrorSep = new etModuleType("mtNonRoburWithCircAndErrorSep");
  public final static etModuleType mtNonRoburWithCirc = new etModuleType("mtNonRoburWithCirc");
  public final static etModuleType mtNonRoburWithCircSep = new etModuleType("mtNonRoburWithCircSep");
  public final static etModuleType mtNonRoburWithError = new etModuleType("mtNonRoburWithError");
  public final static etModuleType mtNonRoburWithErrorSep = new etModuleType("mtNonRoburWithErrorSep");
  public final static etModuleType mtNonRoburSimple = new etModuleType("mtNonRoburSimple");
  public final static etModuleType mtNonRoburSimpleSep = new etModuleType("mtNonRoburSimpleSep");
  public final static etModuleType mtGAHPA18Hyb_ITA_STD_Modul = new etModuleType("mtGAHPA18Hyb_ITA_STD_Modul");
  public final static etModuleType mtMaxModuleType = new etModuleType("mtMaxModuleType");
  public final static etModuleType mtVoid = new etModuleType("mtVoid", 0xFFFF);

  public final int swigValue() {
    return swigValue;
  }

  public String toString() {
    return swigName;
  }

  public static etModuleType swigToEnum(int swigValue) {
    if (swigValue < swigValues.length && swigValue >= 0 && swigValues[swigValue].swigValue == swigValue)
      return swigValues[swigValue];
    for (int i = 0; i < swigValues.length; i++)
      if (swigValues[i].swigValue == swigValue)
        return swigValues[i];
    throw new IllegalArgumentException("No enum " + etModuleType.class + " with value " + swigValue);
  }

  private etModuleType(String swigName) {
    this.swigName = swigName;
    this.swigValue = swigNext++;
  }

  private etModuleType(String swigName, int swigValue) {
    this.swigName = swigName;
    this.swigValue = swigValue;
    swigNext = swigValue+1;
  }

  private etModuleType(String swigName, etModuleType swigEnum) {
    this.swigName = swigName;
    this.swigValue = swigEnum.swigValue;
    swigNext = this.swigValue+1;
  }

  private static etModuleType[] swigValues = { mtACF_USA_STD, mtACF_USA_HT, mtACF_USA_R, mtACF_ITA_STD, mtACF_ITA_HT, mtACF_ITA_LB, mtACF_ITA_TK, mtAY_Apen_Farel, mtAY_Robur, mtWW_ITA_STD, mtGAHPA_ITA_STD, mtGAHPAR_ITA_STD, mtWW_ITA_LB, mtACFHR_ITA_STD, mtHeatRecuperator, mtWW_ITA_STD_Modulating, mtWW_ITA_LB_Modulating, mtGAHPAR_ITA_STD_Modulating, mtGAHPA_ITA_STD_Modulating, mtAY_Robur_Condensing, mtWW_ITA_WS_Modulating, mtGAHPA_ITA_LT_Modulating, mtNonRoburWithCircAndError, mtNonRoburWithCircAndErrorSep, mtNonRoburWithCirc, mtNonRoburWithCircSep, mtNonRoburWithError, mtNonRoburWithErrorSep, mtNonRoburSimple, mtNonRoburSimpleSep, mtGAHPA18Hyb_ITA_STD_Modul, mtMaxModuleType, mtVoid };
  private static int swigNext = 0;
  private final int swigValue;
  private final String swigName;
}

