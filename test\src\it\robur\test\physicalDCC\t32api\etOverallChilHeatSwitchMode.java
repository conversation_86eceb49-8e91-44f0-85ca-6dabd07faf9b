/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 3.0.12
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package it.robur.test.physicalDCC.t32api;

public final class etOverallChilHeatSwitchMode {
  public final static etOverallChilHeatSwitchMode osmButton = new etOverallChilHeatSwitchMode("osmButton");
  public final static etOverallChilHeatSwitchMode osmRYWAuto = new etOverallChilHeatSwitchMode("osmRYWAuto");
  public final static etOverallChilHeatSwitchMode osmBMSControl = new etOverallChilHeatSwitchMode("osmBMSControl");
  public final static etOverallChilHeatSwitchMode osmRaControl = new etOverallChilHeatSwitchMode("osmRaControl");

  public final int swigValue() {
    return swigValue;
  }

  public String toString() {
    return swigName;
  }

  public static etOverallChilHeatSwitchMode swigToEnum(int swigValue) {
    if (swigValue < swigValues.length && swigValue >= 0 && swigValues[swigValue].swigValue == swigValue)
      return swigValues[swigValue];
    for (int i = 0; i < swigValues.length; i++)
      if (swigValues[i].swigValue == swigValue)
        return swigValues[i];
    throw new IllegalArgumentException("No enum " + etOverallChilHeatSwitchMode.class + " with value " + swigValue);
  }

  private etOverallChilHeatSwitchMode(String swigName) {
    this.swigName = swigName;
    this.swigValue = swigNext++;
  }

  private etOverallChilHeatSwitchMode(String swigName, int swigValue) {
    this.swigName = swigName;
    this.swigValue = swigValue;
    swigNext = swigValue+1;
  }

  private etOverallChilHeatSwitchMode(String swigName, etOverallChilHeatSwitchMode swigEnum) {
    this.swigName = swigName;
    this.swigValue = swigEnum.swigValue;
    swigNext = this.swigValue+1;
  }

  private static etOverallChilHeatSwitchMode[] swigValues = { osmButton, osmRYWAuto, osmBMSControl, osmRaControl };
  private static int swigNext = 0;
  private final int swigValue;
  private final String swigName;
}

