#!/bin/bash

echo "========================================"
echo "    Compilazione jserialtool"
echo "========================================"

# Pulisce build precedenti
if [ -d "build" ]; then
    echo "Pulizia build precedente..."
    rm -rf build
fi

# Crea directory per i file compilati
mkdir -p build/classes

# Costruisce il classpath con tutte le librerie
echo "Costruzione classpath..."
CLASSPATH="."
for jar in libs/*.jar; do
    CLASSPATH="$CLASSPATH:$jar"
done
echo "Classpath: $CLASSPATH"

# Trova tutti i file .java
echo "Ricerca file sorgente..."
find src -name "*.java" > build/sources.txt

# Conta i file
count=$(wc -l < build/sources.txt)
echo "Trovati $count file sorgente"

# Compila tutti i file Java
echo ""
echo "Compilazione in corso..."
javac -cp "$CLASSPATH" -d build/classes @build/sources.txt

if [ $? -ne 0 ]; then
    echo ""
    echo "========================================"
    echo "    ERRORE: Compilazione fallita!"
    echo "========================================"
    echo "Controlla gli errori sopra riportati."
    exit 1
fi

# Copia le risorse se esistono
if [ -d "res" ]; then
    echo "Copia risorse..."
    cp -r res build/classes/
fi

# Crea il manifest per il JAR
echo "Creazione manifest..."
echo "Main-Class: it.robur.jserialtool.JSerialTool" > build/manifest.txt
echo "Class-Path: $CLASSPATH" >> build/manifest.txt

# Crea il JAR con manifest
echo "Creazione JAR..."
cd build/classes
jar cfm ../jserialtool-lib.jar ../manifest.txt *
cd ../..

# Crea anche un JAR eseguibile con tutte le dipendenze
echo "Creazione JAR eseguibile con dipendenze..."
mkdir -p build/temp
cd build/temp

# Estrae tutte le librerie
for jar in ../../libs/*.jar; do
    echo "Estrazione $jar..."
    jar xf "$jar"
done

# Copia le classi compilate
cp -r ../classes/* .

# Crea JAR finale
jar cfm ../jserialtool-complete.jar ../manifest.txt *
cd ../..

# Pulizia
rm -rf build/temp

echo ""
echo "========================================"
echo "    Compilazione completata con successo!"
echo "========================================"
echo "JAR libreria: build/jserialtool-lib.jar"
echo "JAR completo: build/jserialtool-complete.jar"
echo ""
