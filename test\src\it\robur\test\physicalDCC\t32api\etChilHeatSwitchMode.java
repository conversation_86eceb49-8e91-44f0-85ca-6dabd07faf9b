/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 3.0.12
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package it.robur.test.physicalDCC.t32api;

public final class etChilHeatSwitchMode {
  public final static etChilHeatSwitchMode smButton = new etChilHeatSwitchMode("smButton");
  public final static etChilHeatSwitchMode smRYWAuto = new etChilHeatSwitchMode("smRYWAuto");

  public final int swigValue() {
    return swigValue;
  }

  public String toString() {
    return swigName;
  }

  public static etChilHeatSwitchMode swigToEnum(int swigValue) {
    if (swigValue < swigValues.length && swigValue >= 0 && swigValues[swigValue].swigValue == swigValue)
      return swigValues[swigValue];
    for (int i = 0; i < swigValues.length; i++)
      if (swigValues[i].swigValue == swigValue)
        return swigValues[i];
    throw new IllegalArgumentException("No enum " + etChilHeatSwitchMode.class + " with value " + swigValue);
  }

  private etChilHeatSwitchMode(String swigName) {
    this.swigName = swigName;
    this.swigValue = swigNext++;
  }

  private etChilHeatSwitchMode(String swigName, int swigValue) {
    this.swigName = swigName;
    this.swigValue = swigValue;
    swigNext = swigValue+1;
  }

  private etChilHeatSwitchMode(String swigName, etChilHeatSwitchMode swigEnum) {
    this.swigName = swigName;
    this.swigValue = swigEnum.swigValue;
    swigNext = this.swigValue+1;
  }

  private static etChilHeatSwitchMode[] swigValues = { smButton, smRYWAuto };
  private static int swigNext = 0;
  private final int swigValue;
  private final String swigName;
}

