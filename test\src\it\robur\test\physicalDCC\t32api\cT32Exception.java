/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 3.0.12
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package it.robur.test.physicalDCC.t32api;

public class cT32Exception extends java.io.IOException {
  private transient long swigCPtr;
  protected transient boolean swigCMemOwn;

  protected cT32Exception(long cPtr, boolean cMemoryOwn) {
    swigCMemOwn = cMemoryOwn;
    swigCPtr = cPtr;
  }

  protected static long getCPtr(cT32Exception obj) {
    return (obj == null) ? 0 : obj.swigCPtr;
  }

  protected void finalize() {
    delete();
  }

  public synchronized void delete() {
    if (swigCPtr != 0) {
      if (swigCMemOwn) {
        swigCMemOwn = false;
        t32apiJNI.delete_cT32Exception(swigCPtr);
      }
      swigCPtr = 0;
    }
  }

    public String toString() {
      return getMessage();
    }
  
  public cT32Exception(String message, int result) {
    this(t32apiJNI.new_cT32Exception(message, result), true);
  }

  public String getMessage() {
    return t32apiJNI.cT32Exception_getMessage(swigCPtr, this);
  }

  public String getMessageCStr() {
    return t32apiJNI.cT32Exception_getMessageCStr(swigCPtr, this);
  }

  public int GetResult() {
    return t32apiJNI.cT32Exception_GetResult(swigCPtr, this);
  }

}
