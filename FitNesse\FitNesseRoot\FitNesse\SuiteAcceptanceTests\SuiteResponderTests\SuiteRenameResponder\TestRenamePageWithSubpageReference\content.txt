!3 When a page is renamed, any links to that page from subpages pages are changed.

First build parent & child pages where one refers to the other.

!|Page creator.|
|Page name.|Page contents.|valid?|
|ParentPage|refer to >ChildPage|true|
|ParentPage.ChildPage|whatever|true|

Then rename the target page.

!|Response Requester.|
|uri   |status?|
|ParentPage.ChildPage?responder=renamePage&newName=NewChild&refactorReferences=on||

Next fetch the Source page.

!|Response Requester.|
|uri|valid?|contents?|
|ParentPage|true||

Make sure that the new target name is present and that the old name is not.

!|Response Examiner.|
|type  |pattern|matches?|
|contents|>NewChild|true|
|contents|href="ParentPage\.NewChild"|true|
|contents|>ChildPage|false|


