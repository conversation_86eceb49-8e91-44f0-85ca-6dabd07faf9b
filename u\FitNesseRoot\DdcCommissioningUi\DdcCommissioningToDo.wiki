!contents -R2 -g -p -f -h


!1 DDC Commissioning UI


[...]

!3 Load and Save

|it.robur.test.fitnesse.ddc.UserToDDCConfigFixture                                                                                                                                                                           |
|Load_Identif                     |Load_HP?|Load_AR?|Load_Climate?|Climate    |Building_Isolation|Save_Build_Config?                   |Weather_Comp_Slope?|Min_Set_Point?|Max_Set_Point?|Save_DDC_Config?                   |
|BothHeatGroupsAndChil_ddc_identif|true    |false   |<empty>      |continental|high              |json:build_bothplants_contin_highisol|1.75               |30            |60            |json:ddc_bothplants_contin_highisol|
|BothHeatGroupsAndChil_ddc_identif|true    |false   |<empty>      |continental|low               |json:build_bothplants_contin_lowisol |3.50               |30            |60            |json:ddc_bothplants_contin_lowisol |
|GWLT_ddc_identif                 |false   |false   |mountain     |mountain   |low               |json:build_GWLT_lowisol              |3.50               |30            |60            |json:ddc_GWLT_lowisol              |
|GWLT_ddc_identif                 |false   |false   |mountain     |continental|low               |json:build_GWLT_cont_lowisol         |1.60               |30            |60            |json:ddc_GWLT_cont_lowisol         |


!3 Load

UI must be able to load previously saved building configuration.

|it.robur.test.fitnesse.ddc.UserToDDCConfigFixture                                                                          |
|Load_Identif                     |Load_HP?|Load_AR?|Load_Build_Config               |Load_Climate?|Load_Building_Isolation?|
|BothHeatGroupsAndChil_ddc_identif|true    |false   |build_bothplants_contin_highisol|continental  |high                    |
|BothHeatGroupsAndChil_ddc_identif|true    |false   |build_bothplants_contin_lowisol |continental  |low                     |
|GWLT_ddc_identif                 |false   |false   |ddc_GWLT_lowisol                |mountain     |low                     |
|GWLT_ddc_identif                 |false   |false   |ddc_GWLT_cont_lowisol           |continental  |low                     |


!3 Default

Climate is pre-set only for LT units. (NOT really, just an example of what can be done.)

|it.robur.test.fitnesse.ddc.UserToDDCConfigFixture                                         |
|Load_Identif                     |Load_HP?|Load_AR?|Load_Climate?|Load_Building_Isolation?|
|BothHeatGroupsAndChil_ddc_identif|true    |false   |<empty>      |medium                  |
|GWLT_ddc_identif                 |false   |false   |mountain     |medium                  |


