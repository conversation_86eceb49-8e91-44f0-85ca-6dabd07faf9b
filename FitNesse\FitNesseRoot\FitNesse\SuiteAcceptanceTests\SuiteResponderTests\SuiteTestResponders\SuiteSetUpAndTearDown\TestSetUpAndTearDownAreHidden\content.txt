!3 [[!-SetUp and TearDown-!][.FitNesse.SuiteAcceptanceTests.SuiteResponderTests.SuiteTestResponders.SuiteSetUpAndTearDown]] are rendered as [[Collapsable Sections][.FitNesse.MarkupCollapsableSection]] that are collapsed by default.

 * First create a normal page, plus header and footer pages.
|Page creator.|
|Page name.|Page contents.|Page attributes.|valid?|
|!-TestPage-!|test||true|
|!-SetUp-!|set up||true|
|!-TearDown-!|tear down||true|

 * Now request the test page
|Response Requester.|
|uri|valid?|
|!-TestPage-!|true|

 * Ensure that the setup and and teardown text appear in the test page along with the header and footer.
|Response Examiner.|
|type|pattern|matches?|wrapped html?|
|contents|.*(set up).*(test).*(tear down).*|true||

!|Response Examiner.|
|type|pattern|matches?|
|contents|<div class="collapsible closed">.*<div>set up</div>|true|
|contents|<div class="collapsible closed">.*<div>tear down</div>|true|
