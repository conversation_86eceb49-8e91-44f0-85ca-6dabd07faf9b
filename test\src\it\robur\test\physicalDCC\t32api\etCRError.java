/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 3.0.12
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package it.robur.test.physicalDCC.t32api;

public final class etCRError {
  public final static etCRError creReconfig = new etCRError("creReconfig");
  public final static etCRError creOffline = new etCRError("creOffline");
  public final static etCRError creAllOffline = new etCRError("creAllOffline");
  public final static etCRError creMaxTemp = new etCRError("creMaxTemp");
  public final static etCRError creMinTemp = new etCRError("creMinTemp");
  public final static etCRError creBothRYRW = new etCRError("creBothRYRW");
  public final static etCRError creAmbTempProbe = new etCRError("creAmbTempProbe");
  public final static etCRError creModMismatch = new etCRError("creModMismatch");
  public final static etCRError creMasterOffline = new etCRError("creMasterOffline");
  public final static etCRError creSlaveOffline = new etCRError("creSlaveOffline");
  public final static etCRError creLogClear = new etCRError("creLogClear");
  public final static etCRError crePower = new etCRError("crePower");
  public final static etCRError creLogRestart = new etCRError("creLogRestart");
  public final static etCRError creSepAllOffline = new etCRError("creSepAllOffline");
  public final static etCRError creCHValveMismatch = new etCRError("creCHValveMismatch");
  public final static etCRError creCHValveOffline = new etCRError("creCHValveOffline");
  public final static etCRError creCHValveTimeout = new etCRError("creCHValveTimeout");
  public final static etCRError creCHValveFeedbackError = new etCRError("creCHValveFeedbackError");
  public final static etCRError creCHValveWrongOut = new etCRError("creCHValveWrongOut");
  public final static etCRError creSepValveMismatch = new etCRError("creSepValveMismatch");
  public final static etCRError creSepValveOffline = new etCRError("creSepValveOffline");
  public final static etCRError creSepValveTimeout = new etCRError("creSepValveTimeout");
  public final static etCRError creSepValveFeedbackError = new etCRError("creSepValveFeedbackError");
  public final static etCRError creWaterTempProbe = new etCRError("creWaterTempProbe");
  public final static etCRError creRequestedTemp = new etCRError("creRequestedTemp");
  public final static etCRError creEstTempProbe = new etCRError("creEstTempProbe");
  public final static etCRError creCRMismatch = new etCRError("creCRMismatch");
  public final static etCRError creOEM1OffLimitAlarm = new etCRError("creOEM1OffLimitAlarm");
  public final static etCRError creNonRoburUnitErr = new etCRError("creNonRoburUnitErr");
  public final static etCRError creIOModProbeError = new etCRError("creIOModProbeError");
  public final static etCRError creSepIOModProbeError = new etCRError("creSepIOModProbeError");
  public final static etCRError creChilInProbeOffline = new etCRError("creChilInProbeOffline");
  public final static etCRError creChilOutProbeOffline = new etCRError("creChilOutProbeOffline");
  public final static etCRError creHeatInProbeOffline = new etCRError("creHeatInProbeOffline");
  public final static etCRError creHeatOutProbeOffline = new etCRError("creHeatOutProbeOffline");
  public final static etCRError creSepInProbeOffline = new etCRError("creSepInProbeOffline");
  public final static etCRError creSepOutProbeOffline = new etCRError("creSepOutProbeOffline");
  public final static etCRError creHeatHighEffAddInProbeOffline = new etCRError("creHeatHighEffAddInProbeOffline");
  public final static etCRError creChilInProbeMismatch = new etCRError("creChilInProbeMismatch");
  public final static etCRError creChilOutProbeMismatch = new etCRError("creChilOutProbeMismatch");
  public final static etCRError creHeatInProbeMismatch = new etCRError("creHeatInProbeMismatch");
  public final static etCRError creHeatOutProbeMismatch = new etCRError("creHeatOutProbeMismatch");
  public final static etCRError creSepInProbeMismatch = new etCRError("creSepInProbeMismatch");
  public final static etCRError creSepOutProbeMismatch = new etCRError("creSepOutProbeMismatch");
  public final static etCRError creHeatHighEffAddInProbeMismatch = new etCRError("creHeatHighEffAddInProbeMismatch");
  public final static etCRError creChilInProbeFault = new etCRError("creChilInProbeFault");
  public final static etCRError creChilOutProbeFault = new etCRError("creChilOutProbeFault");
  public final static etCRError creHeatInProbeFault = new etCRError("creHeatInProbeFault");
  public final static etCRError creHeatOutProbeFault = new etCRError("creHeatOutProbeFault");
  public final static etCRError creSepInProbeFault = new etCRError("creSepInProbeFault");
  public final static etCRError creSepOutProbeFault = new etCRError("creSepOutProbeFault");
  public final static etCRError creHeatHighEffAddInProbeFault = new etCRError("creHeatHighEffAddInProbeFault");
  public final static etCRError creChilCircOffline = new etCRError("creChilCircOffline");
  public final static etCRError creHeatCircOffline = new etCRError("creHeatCircOffline");
  public final static etCRError creSepCircOffline = new etCRError("creSepCircOffline");
  public final static etCRError creChilCircMismatch = new etCRError("creChilCircMismatch");
  public final static etCRError creHeatCircMismatch = new etCRError("creHeatCircMismatch");
  public final static etCRError creSepCircMismatch = new etCRError("creSepCircMismatch");
  public final static etCRError creIOModProbeNotInstalled = new etCRError("creIOModProbeNotInstalled");
  public final static etCRError creSepIOModProbeNotInstalled = new etCRError("creSepIOModProbeNotInstalled");
  public final static etCRError creIOModCircNotInstalled = new etCRError("creIOModCircNotInstalled");
  public final static etCRError creSepIOModCircNotInstalled = new etCRError("creSepIOModCircNotInstalled");
  public final static etCRError creSecondaryPlantChilCircOffline = new etCRError("creSecondaryPlantChilCircOffline");
  public final static etCRError creSecondaryPlantHeatCircOffline = new etCRError("creSecondaryPlantHeatCircOffline");
  public final static etCRError creSecondaryPlantChilCircMismatch = new etCRError("creSecondaryPlantChilCircMismatch");
  public final static etCRError creSecondaryPlantHeatCircMismatch = new etCRError("creSecondaryPlantHeatCircMismatch");
  public final static etCRError creSomeInOffLimit = new etCRError("creSomeInOffLimit");
  public final static etCRError creSepSomeInOffLimit = new etCRError("creSepSomeInOffLimit");
  public final static etCRError creSWdogRes = new etCRError("creSWdogRes");
  public final static etCRError creMaintenanceDue = new etCRError("creMaintenanceDue");
  public final static etCRError creMaxCRErrors = new etCRError("creMaxCRErrors");

  public final int swigValue() {
    return swigValue;
  }

  public String toString() {
    return swigName;
  }

  public static etCRError swigToEnum(int swigValue) {
    if (swigValue < swigValues.length && swigValue >= 0 && swigValues[swigValue].swigValue == swigValue)
      return swigValues[swigValue];
    for (int i = 0; i < swigValues.length; i++)
      if (swigValues[i].swigValue == swigValue)
        return swigValues[i];
    throw new IllegalArgumentException("No enum " + etCRError.class + " with value " + swigValue);
  }

  private etCRError(String swigName) {
    this.swigName = swigName;
    this.swigValue = swigNext++;
  }

  private etCRError(String swigName, int swigValue) {
    this.swigName = swigName;
    this.swigValue = swigValue;
    swigNext = swigValue+1;
  }

  private etCRError(String swigName, etCRError swigEnum) {
    this.swigName = swigName;
    this.swigValue = swigEnum.swigValue;
    swigNext = this.swigValue+1;
  }

  private static etCRError[] swigValues = { creReconfig, creOffline, creAllOffline, creMaxTemp, creMinTemp, creBothRYRW, creAmbTempProbe, creModMismatch, creMasterOffline, creSlaveOffline, creLogClear, crePower, creLogRestart, creSepAllOffline, creCHValveMismatch, creCHValveOffline, creCHValveTimeout, creCHValveFeedbackError, creCHValveWrongOut, creSepValveMismatch, creSepValveOffline, creSepValveTimeout, creSepValveFeedbackError, creWaterTempProbe, creRequestedTemp, creEstTempProbe, creCRMismatch, creOEM1OffLimitAlarm, creNonRoburUnitErr, creIOModProbeError, creSepIOModProbeError, creChilInProbeOffline, creChilOutProbeOffline, creHeatInProbeOffline, creHeatOutProbeOffline, creSepInProbeOffline, creSepOutProbeOffline, creHeatHighEffAddInProbeOffline, creChilInProbeMismatch, creChilOutProbeMismatch, creHeatInProbeMismatch, creHeatOutProbeMismatch, creSepInProbeMismatch, creSepOutProbeMismatch, creHeatHighEffAddInProbeMismatch, creChilInProbeFault, creChilOutProbeFault, creHeatInProbeFault, creHeatOutProbeFault, creSepInProbeFault, creSepOutProbeFault, creHeatHighEffAddInProbeFault, creChilCircOffline, creHeatCircOffline, creSepCircOffline, creChilCircMismatch, creHeatCircMismatch, creSepCircMismatch, creIOModProbeNotInstalled, creSepIOModProbeNotInstalled, creIOModCircNotInstalled, creSepIOModCircNotInstalled, creSecondaryPlantChilCircOffline, creSecondaryPlantHeatCircOffline, creSecondaryPlantChilCircMismatch, creSecondaryPlantHeatCircMismatch, creSomeInOffLimit, creSepSomeInOffLimit, creSWdogRes, creMaintenanceDue, creMaxCRErrors };
  private static int swigNext = 0;
  private final int swigValue;
  private final String swigName;
}

