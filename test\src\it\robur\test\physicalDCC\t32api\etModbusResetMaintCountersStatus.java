/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 3.0.12
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package it.robur.test.physicalDCC.t32api;

public final class etModbusResetMaintCountersStatus {
  public final static etModbusResetMaintCountersStatus mbRMCStatusCommandImpossible = new etModbusResetMaintCountersStatus("mbRMCStatusCommandImpossible", 1);
  public final static etModbusResetMaintCountersStatus mbRMCStatusCommandSucceeded = new etModbusResetMaintCountersStatus("mbRMCStatusCommandSucceeded", 2);
  public final static etModbusResetMaintCountersStatus mbRMCStatusCommandFailed = new etModbusResetMaintCountersStatus("mbRMCStatusCommandFailed", 3);
  public final static etModbusResetMaintCountersStatus mbRMCStatusCommandWrongCode = new etModbusResetMaintCountersStatus("mbRMCStatusCommandWrongCode", 4);

  public final int swigValue() {
    return swigValue;
  }

  public String toString() {
    return swigName;
  }

  public static etModbusResetMaintCountersStatus swigToEnum(int swigValue) {
    if (swigValue < swigValues.length && swigValue >= 0 && swigValues[swigValue].swigValue == swigValue)
      return swigValues[swigValue];
    for (int i = 0; i < swigValues.length; i++)
      if (swigValues[i].swigValue == swigValue)
        return swigValues[i];
    throw new IllegalArgumentException("No enum " + etModbusResetMaintCountersStatus.class + " with value " + swigValue);
  }

  private etModbusResetMaintCountersStatus(String swigName) {
    this.swigName = swigName;
    this.swigValue = swigNext++;
  }

  private etModbusResetMaintCountersStatus(String swigName, int swigValue) {
    this.swigName = swigName;
    this.swigValue = swigValue;
    swigNext = swigValue+1;
  }

  private etModbusResetMaintCountersStatus(String swigName, etModbusResetMaintCountersStatus swigEnum) {
    this.swigName = swigName;
    this.swigValue = swigEnum.swigValue;
    swigNext = this.swigValue+1;
  }

  private static etModbusResetMaintCountersStatus[] swigValues = { mbRMCStatusCommandImpossible, mbRMCStatusCommandSucceeded, mbRMCStatusCommandFailed, mbRMCStatusCommandWrongCode };
  private static int swigNext = 0;
  private final int swigValue;
  private final String swigName;
}

