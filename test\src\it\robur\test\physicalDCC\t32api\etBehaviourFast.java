/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 3.0.12
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package it.robur.test.physicalDCC.t32api;

public final class etBehaviourFast {
  public final static etBehaviourFast befNone = new etBehaviourFast("befNone");
  public final static etBehaviourFast befSendAnalog = new etBehaviourFast("befSendAnalog");
  public final static etBehaviourFast befSendDigital = new etBehaviourFast("befSendDigital");
  public final static etBehaviourFast befSendAnalogDigital = new etBehaviourFast("befSendAnalogDigital");

  public final int swigValue() {
    return swigValue;
  }

  public String toString() {
    return swigName;
  }

  public static etBehaviourFast swigToEnum(int swigValue) {
    if (swigValue < swigValues.length && swigValue >= 0 && swigValues[swigValue].swigValue == swigValue)
      return swigValues[swigValue];
    for (int i = 0; i < swigValues.length; i++)
      if (swigValues[i].swigValue == swigValue)
        return swigValues[i];
    throw new IllegalArgumentException("No enum " + etBehaviourFast.class + " with value " + swigValue);
  }

  private etBehaviourFast(String swigName) {
    this.swigName = swigName;
    this.swigValue = swigNext++;
  }

  private etBehaviourFast(String swigName, int swigValue) {
    this.swigName = swigName;
    this.swigValue = swigValue;
    swigNext = swigValue+1;
  }

  private etBehaviourFast(String swigName, etBehaviourFast swigEnum) {
    this.swigName = swigName;
    this.swigValue = swigEnum.swigValue;
    swigNext = this.swigValue+1;
  }

  private static etBehaviourFast[] swigValues = { befNone, befSendAnalog, befSendDigital, befSendAnalogDigital };
  private static int swigNext = 0;
  private final int swigValue;
  private final String swigName;
}

