/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 3.0.12
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package it.robur.test.physicalDCC.t32api;

public final class etAnalogCode {
  public final static etAnalogCode acRectTemp = new etAnalogCode("acRectTemp");
  public final static etAnalogCode acCntTemp = new etAnalogCode("acCntTemp");
  public final static etAnalogCode acAux1Temp = new etAnalogCode("acAux1Temp");
  public final static etAnalogCode acAux2Temp = new etAnalogCode("acAux2Temp");
  public final static etAnalogCode acVoltage = new etAnalogCode("acVoltage");
  public final static etAnalogCode acPumpRPM = new etAnalogCode("acPumpRPM");
  public final static etAnalogCode acFanValue = new etAnalogCode("acFanValue");
  public final static etAnalogCode acAbsTemp = new etAnalogCode("acAbsTemp");
  public final static etAnalogCode acEvapTemp = new etAnalogCode("acEvapTemp");
  public final static etAnalogCode acPrimaryOutTemp = new etAnalogCode("acPrimaryOutTemp");
  public final static etAnalogCode acBlowerSpeed = new etAnalogCode("acBlowerSpeed");
  public final static etAnalogCode acWaterFlow = new etAnalogCode("acWaterFlow");
  public final static etAnalogCode acModulatingCircVoltage = new etAnalogCode("acModulatingCircVoltage");
  public final static etAnalogCode acAnalogInVoltage = new etAnalogCode("acAnalogInVoltage");
  public final static etAnalogCode acAmbTemp = new etAnalogCode("acAmbTemp");
  public final static etAnalogCode acMixTemp = new etAnalogCode("acMixTemp");
  public final static etAnalogCode acFlueTemp = new etAnalogCode("acFlueTemp");
  public final static etAnalogCode acCondensateVoltage = new etAnalogCode("acCondensateVoltage");
  public final static etAnalogCode acGenFinsTemp = new etAnalogCode("acGenFinsTemp");
  public final static etAnalogCode acIonizationCurrent = new etAnalogCode("acIonizationCurrent");
  public final static etAnalogCode acExtVoltage1 = new etAnalogCode("acExtVoltage1");
  public final static etAnalogCode acExtVoltage2 = new etAnalogCode("acExtVoltage2");
  public final static etAnalogCode acOptTemp2 = new etAnalogCode("acOptTemp2");
  public final static etAnalogCode acExtSetpoint = new etAnalogCode("acExtSetpoint");
  public final static etAnalogCode acCurrentSetpoint = new etAnalogCode("acCurrentSetpoint");
  public final static etAnalogCode acOutTemp_OtherMod = new etAnalogCode("acOutTemp_OtherMod");
  public final static etAnalogCode acInTemp_OtherMod = new etAnalogCode("acInTemp_OtherMod");
  public final static etAnalogCode acGUE = new etAnalogCode("acGUE");
  public final static etAnalogCode acGasValveTemp = new etAnalogCode("acGasValveTemp");
  public final static etAnalogCode acMaxAnalogs = new etAnalogCode("acMaxAnalogs");
  public final static etAnalogCode acNone = new etAnalogCode("acNone", 31);

  public final int swigValue() {
    return swigValue;
  }

  public String toString() {
    return swigName;
  }

  public static etAnalogCode swigToEnum(int swigValue) {
    if (swigValue < swigValues.length && swigValue >= 0 && swigValues[swigValue].swigValue == swigValue)
      return swigValues[swigValue];
    for (int i = 0; i < swigValues.length; i++)
      if (swigValues[i].swigValue == swigValue)
        return swigValues[i];
    throw new IllegalArgumentException("No enum " + etAnalogCode.class + " with value " + swigValue);
  }

  private etAnalogCode(String swigName) {
    this.swigName = swigName;
    this.swigValue = swigNext++;
  }

  private etAnalogCode(String swigName, int swigValue) {
    this.swigName = swigName;
    this.swigValue = swigValue;
    swigNext = swigValue+1;
  }

  private etAnalogCode(String swigName, etAnalogCode swigEnum) {
    this.swigName = swigName;
    this.swigValue = swigEnum.swigValue;
    swigNext = this.swigValue+1;
  }

  private static etAnalogCode[] swigValues = { acRectTemp, acCntTemp, acAux1Temp, acAux2Temp, acVoltage, acPumpRPM, acFanValue, acAbsTemp, acEvapTemp, acPrimaryOutTemp, acBlowerSpeed, acWaterFlow, acModulatingCircVoltage, acAnalogInVoltage, acAmbTemp, acMixTemp, acFlueTemp, acCondensateVoltage, acGenFinsTemp, acIonizationCurrent, acExtVoltage1, acExtVoltage2, acOptTemp2, acExtSetpoint, acCurrentSetpoint, acOutTemp_OtherMod, acInTemp_OtherMod, acGUE, acGasValveTemp, acMaxAnalogs, acNone };
  private static int swigNext = 0;
  private final int swigValue;
  private final String swigName;
}

