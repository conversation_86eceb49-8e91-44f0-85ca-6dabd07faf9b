!3 When you try to move a page under a parent that does not exist, you should get a nice error message.

!3 We should be able to move a page from one location to another.

First build a page, a subpage to move, but ''no'' target page to move it to.

|Page creator.|
|Page name.          |Page contents.|valid?|
|!-ParentPage-!        |x           |true   |
|!-ParentPage.SubPage-!|sub page    |true   |

Then try move that page to a non-existent target..

|Response Requester.|
|uri   |status?|
|!-ParentPage.SubPage?responder=movePage&newLocation=NewParentPage-!|400|

Make sure we get an error message.

|Response Examiner.|
|type  |pattern|matches?|wrapped html?|
|contents|Cannot move|true||

Make sure that the sub page is still beneath !-ParentPage-!.

|Response Requester.|
|uri|valid?|contents?|
|!-ParentPage.SubPage-!|true||

