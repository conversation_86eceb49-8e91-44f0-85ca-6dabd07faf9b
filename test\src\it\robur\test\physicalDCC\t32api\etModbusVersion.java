/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 3.0.12
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package it.robur.test.physicalDCC.t32api;

public final class etModbusVersion {
  public final static etModbusVersion ModbusVersion10 = new etModbusVersion("ModbusVersion10");
  public final static etModbusVersion ModbusVersion20 = new etModbusVersion("ModbusVersion20");

  public final int swigValue() {
    return swigValue;
  }

  public String toString() {
    return swigName;
  }

  public static etModbusVersion swigToEnum(int swigValue) {
    if (swigValue < swigValues.length && swigValue >= 0 && swigValues[swigValue].swigValue == swigValue)
      return swigValues[swigValue];
    for (int i = 0; i < swigValues.length; i++)
      if (swigValues[i].swigValue == swigValue)
        return swigValues[i];
    throw new IllegalArgumentException("No enum " + etModbusVersion.class + " with value " + swigValue);
  }

  private etModbusVersion(String swigName) {
    this.swigName = swigName;
    this.swigValue = swigNext++;
  }

  private etModbusVersion(String swigName, int swigValue) {
    this.swigName = swigName;
    this.swigValue = swigValue;
    swigNext = swigValue+1;
  }

  private etModbusVersion(String swigName, etModbusVersion swigEnum) {
    this.swigName = swigName;
    this.swigValue = swigEnum.swigValue;
    swigNext = this.swigValue+1;
  }

  private static etModbusVersion[] swigValues = { ModbusVersion10, ModbusVersion20 };
  private static int swigNext = 0;
  private final int swigValue;
  private final String swigName;
}

