/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 3.0.12
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package it.robur.test.physicalDCC.t32api;

public final class etDataType {
  public final static etDataType dtCanId = new etDataType("dtCanId");
  public final static etDataType dtInteger = new etDataType("dtInteger");
  public final static etDataType dtBoolean = new etDataType("dtBoolean");
  public final static etDataType dtDecTemp = new etDataType("dtDecTemp");
  public final static etDataType dtTime = new etDataType("dtTime");
  public final static etDataType dtPW = new etDataType("dtPW");
  public final static etDataType dtUField16 = new etDataType("dtUField16");
  public final static etDataType dtUField24 = new etDataType("dtUField24");
  public final static etDataType dtUField32 = new etDataType("dtUField32");
  public final static etDataType dtDecimal = new etDataType("dtDecimal");
  public final static etDataType dtDecTempDiff = new etDataType("dtDecTempDiff");
  public final static etDataType dtCentesimal = new etDataType("dtCentesimal");
  public final static etDataType dtRCId = new etDataType("dtRCId");

  public final int swigValue() {
    return swigValue;
  }

  public String toString() {
    return swigName;
  }

  public static etDataType swigToEnum(int swigValue) {
    if (swigValue < swigValues.length && swigValue >= 0 && swigValues[swigValue].swigValue == swigValue)
      return swigValues[swigValue];
    for (int i = 0; i < swigValues.length; i++)
      if (swigValues[i].swigValue == swigValue)
        return swigValues[i];
    throw new IllegalArgumentException("No enum " + etDataType.class + " with value " + swigValue);
  }

  private etDataType(String swigName) {
    this.swigName = swigName;
    this.swigValue = swigNext++;
  }

  private etDataType(String swigName, int swigValue) {
    this.swigName = swigName;
    this.swigValue = swigValue;
    swigNext = swigValue+1;
  }

  private etDataType(String swigName, etDataType swigEnum) {
    this.swigName = swigName;
    this.swigValue = swigEnum.swigValue;
    swigNext = this.swigValue+1;
  }

  private static etDataType[] swigValues = { dtCanId, dtInteger, dtBoolean, dtDecTemp, dtTime, dtPW, dtUField16, dtUField24, dtUField32, dtDecimal, dtDecTempDiff, dtCentesimal, dtRCId };
  private static int swigNext = 0;
  private final int swigValue;
  private final String swigName;
}

