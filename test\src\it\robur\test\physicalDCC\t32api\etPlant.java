/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 3.0.12
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package it.robur.test.physicalDCC.t32api;

public final class etPlant {
  public final static etPlant chil = new etPlant("chil");
  public final static etPlant heat = new etPlant("heat");
  public final static etPlant MAX_PLANTS = new etPlant("MAX_PLANTS");

  public final int swigValue() {
    return swigValue;
  }

  public String toString() {
    return swigName;
  }

  public static etPlant swigToEnum(int swigValue) {
    if (swigValue < swigValues.length && swigValue >= 0 && swigValues[swigValue].swigValue == swigValue)
      return swigValues[swigValue];
    for (int i = 0; i < swigValues.length; i++)
      if (swigValues[i].swigValue == swigValue)
        return swigValues[i];
    throw new IllegalArgumentException("No enum " + etPlant.class + " with value " + swigValue);
  }

  private etPlant(String swigName) {
    this.swigName = swigName;
    this.swigValue = swigNext++;
  }

  private etPlant(String swigName, int swigValue) {
    this.swigName = swigName;
    this.swigValue = swigValue;
    swigNext = swigValue+1;
  }

  private etPlant(String swigName, etPlant swigEnum) {
    this.swigName = swigName;
    this.swigValue = swigEnum.swigValue;
    swigNext = this.swigValue+1;
  }

  private static etPlant[] swigValues = { chil, heat, MAX_PLANTS };
  private static int swigNext = 0;
  private final int swigValue;
  private final String swigName;
}

