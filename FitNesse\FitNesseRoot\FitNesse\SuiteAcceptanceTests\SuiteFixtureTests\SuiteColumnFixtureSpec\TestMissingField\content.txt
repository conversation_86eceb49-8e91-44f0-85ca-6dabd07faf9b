!3 When testing a column fixture, if the header row mentions a field that is not in the fixture, then the following message should appear in that cell:
{{{Could not find field: fieldname.}}}

 * Here is a fitnesse page that should generate the error
!note The !-!path-! must point to fitnesse-standalone.jar
!note !-ColumnFixtureTestFixture-! is a special class used for testing Column fixtures.
|Action fixture|
|start|Page builder|
|enter|attributes|Test=true|
|enter|line|${SUT_PATH}|
|enter|line|!-|Import|-!|
|enter|line|!-|fitnesse.fixtures|-!|
|enter|line||
|enter|line|!-|Column fixture test fixture|-!|
|enter|line|!-|no such field|-!|
|enter|page|!-ColumnFixtureTestPage-!|

!|Response Requester|
|uri|status?|
|ColumnFixtureTestPage?test|200|

 * The error message should show up in the response

!|Response examiner|
|type|pattern|matches?|contents?|
|contents|Could not find field: no such field|true||
