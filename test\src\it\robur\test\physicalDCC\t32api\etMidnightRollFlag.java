/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 3.0.12
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package it.robur.test.physicalDCC.t32api;

public final class etMidnightRollFlag {
  public final static etMidnightRollFlag NotMidnight = new etMidnightRollFlag("NotMidnight");
  public final static etMidnightRollFlag AtMidnightRoll = new etMidnightRollFlag("AtMidnightRoll");
  public final static etMidnightRollFlag PastMidnightRoll = new etMidnightRollFlag("PastMidnightRoll");

  public final int swigValue() {
    return swigValue;
  }

  public String toString() {
    return swigName;
  }

  public static etMidnightRollFlag swigToEnum(int swigValue) {
    if (swigValue < swigValues.length && swigValue >= 0 && swigValues[swigValue].swigValue == swigValue)
      return swigValues[swigValue];
    for (int i = 0; i < swigValues.length; i++)
      if (swigValues[i].swigValue == swigValue)
        return swigValues[i];
    throw new IllegalArgumentException("No enum " + etMidnightRollFlag.class + " with value " + swigValue);
  }

  private etMidnightRollFlag(String swigName) {
    this.swigName = swigName;
    this.swigValue = swigNext++;
  }

  private etMidnightRollFlag(String swigName, int swigValue) {
    this.swigName = swigName;
    this.swigValue = swigValue;
    swigNext = swigValue+1;
  }

  private etMidnightRollFlag(String swigName, etMidnightRollFlag swigEnum) {
    this.swigName = swigName;
    this.swigValue = swigEnum.swigValue;
    swigNext = this.swigValue+1;
  }

  private static etMidnightRollFlag[] swigValues = { NotMidnight, AtMidnightRoll, PastMidnightRoll };
  private static int swigNext = 0;
  private final int swigValue;
  private final String swigName;
}

