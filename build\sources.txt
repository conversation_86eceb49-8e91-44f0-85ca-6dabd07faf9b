C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\ddc\DDC.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\ddc\JSONDDC.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\ddc\config\DDCConfig.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\ddc\config\DDCPlantControl2PipesSwitchConfig.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\ddc\config\DDCPlantControlConfig.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\ddc\config\DDCThermostatationAndSetpointLimitsConfig.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\ddc\config\DDCWaterTimerConfig.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\ddc\config\DDCWorkingModeConfig.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\ddc\config\UserConfig.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\ddc\config\UserToDDCConfig.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\ddc\generated\DDCDailyDataConsumerMulti.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\ddc\generated\DDCEventsConsumerMulti.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\ddc\generated\DDCIdentificationConsumerMulti.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\ddc\generated\DDCSampleConfigConsumerMulti.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\ddc\generated\DDCSampleConsumerMulti.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\ddc\generated\DDCSomeOtherConsumerMulti.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\ddc\generated\DDCStatisticsConsumerMulti.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\flashers\BT578Settings.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\flashers\BTDongleSettings.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\flashers\GEN10SerialFlasher.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\flashers\GHP10SerialFlasher.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\flashers\SubprocessFlasher.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\flashers\TerminalSerial.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\gen10\GEN10.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\gen10\JSONGEN10.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\gen10\StatefulGEN10.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\gen10\generated\GEN10DailyDataConsumerMulti.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\gen10\generated\GEN10EventsConsumerMulti.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\gen10\generated\GEN10IdentificationConsumerMulti.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\gen10\generated\GEN10ParamsConsumerMulti.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\gen10\generated\GEN10SampleConsumerMulti.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\gen10\generated\GEN10StatisticsConsumerMulti.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\ghp10\GHP10.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\ghp10\JSONGHP10.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\ghp10\generated\GHP10DailyDataConsumerMulti.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\ghp10\generated\GHP10EventsConsumerMulti.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\ghp10\generated\GHP10IdentificationConsumerMulti.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\ghp10\generated\GHP10ParamsConsumerMulti.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\ghp10\generated\GHP10SampleConsumerMulti.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\ghp10\generated\GHP10StatisticsConsumerMulti.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\interfaces\Op.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\interfaces\OpGenericKV.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\interfaces\OpGenericKVMulti.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\interfaces\OpGenericKVtoR.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\interfaces\OpGenericV.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\interfaces\OpGenericVMulti.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\interfaces\OpGroupMultiable.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\interfaces\OpInt.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\interfaces\OpJSON.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\interfaces\OpLong.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\interfaces\OpLongBool.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\interfaces\OpLongStrDArr.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\interfaces\OpLongStrDArrMulti.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\interfaces\OpMulti.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\interfaces\OpPolyStrCLongCIntInt.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\interfaces\OpPolyStringInt.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\interfaces\OpPolyStrStrInt.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\interfaces\OpPolyStrStrIntStrDArr.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\interfaces\OpStr.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\interfaces\OpStrArrIntArr.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\interfaces\OpStrCLong.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\interfaces\OpStrCLongCInt.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\interfaces\OpStrCLongCIntInt.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\interfaces\OpStrCLongCIntMulti.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\interfaces\OpStrCLongInt.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\interfaces\OpStrDArr.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\interfaces\OpStrDate.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\interfaces\OpStrDateInt.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\interfaces\OpStrDateMulti.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\interfaces\OpStrInt.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\interfaces\OpStrIntCBool.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\interfaces\OpStrIntCInt.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\interfaces\OpStrIntInt.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\interfaces\OpStrIntMulti.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\interfaces\OpStrMulti.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\interfaces\OpStrStr.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\interfaces\OpStrStrCBool.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\interfaces\OpStrStrCBoolMulti.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\interfaces\OpStrStrInt.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\interfaces\OpStrStrIntStrDArr.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\interfaces\OpStrStrMulti.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\interfaces\OpStrStrOrStrIntInt.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\interfaces\OpStrStrOrStrIntIntConcr.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\interfaces\OpStrStrReturnList.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\interfaces\OpStrStrReturnMap.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\interfaces\OpStrStrStrDArr.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\interfaces\OpStrStrStrDArrMulti.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\interfaces\OpStrStrToBool.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\interfaces\OpStrToStr.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\interfaces\OpToBool.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\interfaces\SimpleStringConsumer.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\interfaces\TpStrArrIntArr.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\interfaces\TpStrArrStrArr.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\interfaces\TpStrInt.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\jserialtool\ApplicationPreference.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\jserialtool\CCILogUIConsPC.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\jserialtool\CloudConnectedPC.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\jserialtool\CommandLineOptions.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\jserialtool\ConfigurationView.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\jserialtool\DDCLogUIConsPC.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\jserialtool\ErrorDialog.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\jserialtool\FirmwareFileView.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\jserialtool\GEN10LogUIConsPC.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\jserialtool\GHP10LogUIConsPC.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\jserialtool\GHP10LogUIConsPrint.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\jserialtool\JSerialTool.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\jserialtool\JSONDDCKalpaZipToXML.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\jserialtool\JSONRoburZipToXML.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\jserialtool\LogConvResolverPC.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\jserialtool\LogFileView.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\jserialtool\MainController.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\jserialtool\MainTable.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\jserialtool\MainView.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\jserialtool\Manifest.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\jserialtool\ModbusView.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\jserialtool\MqttClientPCBuilder.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\jserialtool\SerialPurejavacomm.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\jserialtool\SettingsModel.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\jserialtool\SettingsView.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\jserialtool\SharerPC.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\jserialtool\StatusView.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\jserialtool\TcpConnectionTypes.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\jserialtool\USRBridgePC.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\jserialtool\board\Board.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\jserialtool\board\CCI.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\jserialtool\board\DDC.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\jserialtool\board\K18.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\jserialtool\board\NEXTG.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\jserialtool\board\ROBURBERRY.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\jserialtool\board\TargetBoards.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\jserialtool\ui\CheckBoxGetterSetter.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\jserialtool\ui\ComboBoxAndTextValidatorSetter.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\jserialtool\ui\ComboBoxValidatorSetter.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\jserialtool\ui\TextFieldAppendableSetter.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\jserialtool\ui\TextFieldValidatorSetter.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\jserialtool\ui\VisibleSetter.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\jserialtool\ui\VisibleValidable.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\jsonui\DDCConfigJSONUIStatelessWindow.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\jsonui\DDCConfigJSONUIWindow.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\jsonui\DDCUserConfigJSONUIStatelessWindow.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\jsonui\DDCUserConfigJSONUIWindow.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\jsonui\JSONUIGetterSetter.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\jsonui\JSONUIGetterSetterWithItems.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\jsonui\JSONUISetter.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\jsonui\JSONUIStatelessWindow.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\jsonui\JSONUIValidable.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\jsonui\JSONUIVisible.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\jstcertif\JSerialTool.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\jstcertif\MainController.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\jstcertif\ModbusView.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\loggers\AllByTagJSONLogPublisher.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\loggers\CCIJSONLogCons.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\loggers\CCILogConsumer.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\loggers\CCILogger.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\loggers\DDCJSONLogCons.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\loggers\DDCJSONReceive.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\loggers\DDCLogConsumer.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\loggers\DDCLogCopy.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\loggers\DDCLogger.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\loggers\DDCLogProducer.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\loggers\DumpJSONLogPublisher.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\loggers\GEN10JSONLogCons.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\loggers\GEN10LogConsumer.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\loggers\GEN10LogCopy.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\loggers\GEN10Logger.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\loggers\GEN10LogProducer.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\loggers\GHP10JSONLogCons.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\loggers\GHP10JSONReceive.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\loggers\GHP10LogConsumer.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\loggers\GHP10LogCopy.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\loggers\GHP10Logger.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\loggers\GHP10LogProducer.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\loggers\JSONDDCKalpaZip.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\loggers\JSONDDCKalpaZippedZip.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\loggers\JSONDDCKalpaZipToXML.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\loggers\JSONDDCRoburZip.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\loggers\JSONDDCRoburZippedZip.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\loggers\JSONGEN10RoburZip.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\loggers\JSONGHP10RoburZip.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\loggers\JSONGHP10RoburZippedZip.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\loggers\JSONPublisher.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\loggers\JSONPublisherDisableableDecorator.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\loggers\JSONPublisherMulti.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\loggers\JSONPublisherResponseDecorator.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\loggers\JSONReceive.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\loggers\JSONRoburZipToXML.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\loggers\JSONUtil.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\loggers\LastByTagJSONLogPublisher.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\loggers\LogConsts.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\loggers\LogFilesUtil.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\loggers\LogPoller.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\loggers\LogPollerSleep.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\loggers\LogPollerSync.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\loggers\NDDCJSONReceive.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\loggers\NextPanelJSONLogCons.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\loggers\NextPanelJSONReceive.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\loggers\NextPanelLogConsumer.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\loggers\SimpleJSONLogPublisher.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\loggers\Timer.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\loggers\TimerSleep.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\modbus\ModbusCCI.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\modbus\ModbusDDC.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\modbus\ModbusErrorResponseException.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\modbus\ModbusGEN10.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\modbus\ModbusGHP10.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\modbus\ModbusInstrument.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\modbus\ModbusSerial.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\nddc\CommandLineOptions.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\nddc\DDCStateConfiguredLogger.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\nddc\DDCStateContext.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\nddc\DDCStateDiscovery.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\nddc\DDCStatePostDiscovery.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\nddc\DDCStateUnconfigured.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\nddc\DDCStateWaitingForMicroUpdate.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\nddc\GUIServer.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\nddc\Manifest.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\nddc\NDDCProcess.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\nddc\NDDCServerFaker.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\net\ConfServer.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\net\Connected.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\net\ConnectedMulti.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\net\DiscoverableSettableTCPInterface.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\net\DiscoverableTCPInterface.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\net\MQTTClientBuilder.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\net\MQTTClientLogPublisherReceiver.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\net\MQTTClientLogPublisherReceiverNonBlockingConnect.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\net\SerialOverTCP.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\net\SettableTCPInterface.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\net\USRBridgeMultiplatform.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\nextpanel\JSONNextPanel.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\nextpanel\JSONtoNextPanelLogConsumer.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\nextpanel\NextHeater.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\nextpanel\NextModule.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\nextpanel\NextPanel.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\nextpanel\NextPanelController.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\nextpanel\NextPanelProducerConsumer.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\nextpanel\SetHostConfigPostbox.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\nextpanel\SetModAndConfigPostbox.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\nextpanel\StatefulNextPanel.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\nextpanel\StatefulNextZone.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\npcontrolprocess\CommandLineOptions.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\npcontrolprocess\ConfServer.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\npcontrolprocess\GUIServer.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\npcontrolprocess\Manifest.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\npcontrolprocess\MQTTClientLogPublisherReceiverNonBlockingConnect.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\npcontrolprocess\NPControlProcess.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\npcontrolprocess\NPServerFaker.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\npcontrolprocess\SaveJSONPublisher.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\rberry\DDCJSONMessagesParser.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\rberry\DeviceConfig.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\rberry\FullJSONtoGEN10LogConsumer.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\rberry\JSONParser.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\rberry\JSONtoCCILogConsumer.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\rberry\JSONtoGEN10LogConsumer.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\rberry\JSONtoGHP10LogConsumer.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\rberry\RberryComm.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\rberry\SocketRberryComm.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\strings\Language.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\strings\StringGetter.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\ui\AtLeastOneBoolValidator.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\ui\BasicBoolValidator.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\ui\BasicStringValidator.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\ui\BoolValidator.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\ui\DDCOrK18Validator.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\ui\FrameworkAppendableSetter.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\ui\FrameworkBoolGetterSetter.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\ui\FrameworkBoolSetter.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\ui\FrameworkBoolValidable.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\ui\FrameworkGetterSetter.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\ui\FrameworkGetterSetterWithItems.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\ui\FrameworkGetterSetterWithItemsLoader.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\ui\FrameworkStringGetter.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\ui\FrameworkStringSetter.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\ui\FrameworkStringValidable.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\ui\FrameworkValidable.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\ui\IPAddressOrEmptyValidator.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\ui\KnownValuesValidator.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\ui\MinNumValidator.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\ui\NetmaskOrEmptyValidator.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\ui\NumNotEmptyValidator.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\ui\PassBoolValidator.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\ui\PassStringValidator.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\ui\PeriodValidator.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\ui\RangeFixPointSaturatedValidator.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\ui\RangeFixPointValidator.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\ui\RangeNumValidator.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\ui\Strings.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\ui\StringValidator.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\ui\StrNotEmptyValidator.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\ui\TargetValidator.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\ui\ValidablesBoolValidatorDecorator.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\ui\ValidablesStringValidatorDecorator.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\ui\Validator.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\ui\ValidatorGroup.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\ui\ValidatorWithFixedPointMinMax.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\ui\WifiPasswordValidator.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\util\DebugLog.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\util\DebugLogLazyDailySplit.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\util\Documents.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\util\ErrorUtil.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\util\ReverseLineInputStream.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\util\ServerFaker.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\util\StringBuilderWriter.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\util\Util.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\xml\CCIEventsXMLLogFile.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\xml\CCIMetricsXMLLogFile.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\xml\DDCEventsXMLLogFile.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\xml\DDCMetricsXMLLogFile.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\xml\DDCXMLLogCons.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\xml\EventsXMLLogFile.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\xml\GEN10EventsXMLLogFile.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\xml\GEN10MetricsXMLLogFile.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\xml\GEN10XMLLogCons.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\xml\GHP10XMLLogCons.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\xml\MetricsXMLLogFile.java
C:\Users\<USER>\Desktop\work\robur\jserialtool\src\it\robur\xml\XMLLogFile.java
