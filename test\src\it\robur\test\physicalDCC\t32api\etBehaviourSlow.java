/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 3.0.12
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package it.robur.test.physicalDCC.t32api;

public final class etBehaviourSlow {
  public final static etBehaviourSlow besNone = new etBehaviourSlow("besNone");
  public final static etBehaviourSlow besSendErr = new etBehaviourSlow("besSendErr");
  public final static etBehaviourSlow besSendErrConf = new etBehaviourSlow("besSendErrConf");
  public final static etBehaviourSlow besSendParam = new etBehaviourSlow("besSendParam");
  public final static etBehaviourSlow besNoneAndStartParamReceive = new etBehaviourSlow("besNoneAndStartParamReceive");
  public final static etBehaviourSlow besNoneAndAbortParamReceive = new etBehaviourSlow("besNoneAndAbortParamReceive");
  public final static etBehaviourSlow besSendExtraInfo = new etBehaviourSlow("besSendExtraInfo");
  public final static etBehaviourSlow besSendDefaultParam = new etBehaviourSlow("besSendDefaultParam");

  public final int swigValue() {
    return swigValue;
  }

  public String toString() {
    return swigName;
  }

  public static etBehaviourSlow swigToEnum(int swigValue) {
    if (swigValue < swigValues.length && swigValue >= 0 && swigValues[swigValue].swigValue == swigValue)
      return swigValues[swigValue];
    for (int i = 0; i < swigValues.length; i++)
      if (swigValues[i].swigValue == swigValue)
        return swigValues[i];
    throw new IllegalArgumentException("No enum " + etBehaviourSlow.class + " with value " + swigValue);
  }

  private etBehaviourSlow(String swigName) {
    this.swigName = swigName;
    this.swigValue = swigNext++;
  }

  private etBehaviourSlow(String swigName, int swigValue) {
    this.swigName = swigName;
    this.swigValue = swigValue;
    swigNext = swigValue+1;
  }

  private etBehaviourSlow(String swigName, etBehaviourSlow swigEnum) {
    this.swigName = swigName;
    this.swigValue = swigEnum.swigValue;
    swigNext = this.swigValue+1;
  }

  private static etBehaviourSlow[] swigValues = { besNone, besSendErr, besSendErrConf, besSendParam, besNoneAndStartParamReceive, besNoneAndAbortParamReceive, besSendExtraInfo, besSendDefaultParam };
  private static int swigNext = 0;
  private final int swigValue;
  private final String swigName;
}

