/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 3.0.12
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package it.robur.test.physicalDCC.t32api;

public class cDDCConf {
  private transient long swigCPtr;
  protected transient boolean swigCMemOwn;

  protected cDDCConf(long cPtr, boolean cMemoryOwn) {
    swigCMemOwn = cMemoryOwn;
    swigCPtr = cPtr;
  }

  protected static long getCPtr(cDDCConf obj) {
    return (obj == null) ? 0 : obj.swigCPtr;
  }

  protected void finalize() {
    delete();
  }

  public synchronized void delete() {
    if (swigCPtr != 0) {
      if (swigCMemOwn) {
        swigCMemOwn = false;
        t32apiJNI.delete_cDDCConf(swigCPtr);
      }
      swigCPtr = 0;
    }
  }

  public static String GetCurrLocalConf() {
    return t32apiJNI.cDDCConf_GetCurrLocalConf();
  }

  public static String GetCurGlobalConf() {
    return t32apiJNI.cDDCConf_GetCurGlobalConf();
  }

  public static short GetCurrLocalConfResult() {
    return t32apiJNI.cDDCConf_GetCurrLocalConfResult();
  }

  public static short GetCurGlobalConfResult() {
    return t32apiJNI.cDDCConf_GetCurGlobalConfResult();
  }

  public static short GetCurrLocalConfError() {
    return t32apiJNI.cDDCConf_GetCurrLocalConfError();
  }

  public static short GetCurGlobalConfError() {
    return t32apiJNI.cDDCConf_GetCurGlobalConfError();
  }

  public static short GetLastResult() {
    return t32apiJNI.cDDCConf_GetLastResult();
  }

  public static void RequestMap() throws java.io.IOException {
    t32apiJNI.cDDCConf_RequestMap();
  }

  public static void ReleaseMap() throws java.io.IOException {
    t32apiJNI.cDDCConf_ReleaseMap();
  }

  public static short ZeroConf(short expected) throws java.io.IOException {
    return t32apiJNI.cDDCConf_ZeroConf__SWIG_0(expected);
  }

  public static short ZeroConf() throws java.io.IOException {
    return t32apiJNI.cDDCConf_ZeroConf__SWIG_1();
  }

  public static short LoadLocalConf(String filename, short expectedRes, short expectedErr) throws java.io.IOException {
    return t32apiJNI.cDDCConf_LoadLocalConf__SWIG_0(filename, expectedRes, expectedErr);
  }

  public static short LoadLocalConf(String filename, short expectedRes) throws java.io.IOException {
    return t32apiJNI.cDDCConf_LoadLocalConf__SWIG_1(filename, expectedRes);
  }

  public static short LoadLocalConf(String filename) throws java.io.IOException {
    return t32apiJNI.cDDCConf_LoadLocalConf__SWIG_2(filename);
  }

  public static short LoadGlobalConf(String filename, short expectedRes, short expectedErr) throws java.io.IOException {
    return t32apiJNI.cDDCConf_LoadGlobalConf__SWIG_0(filename, expectedRes, expectedErr);
  }

  public static short LoadGlobalConf(String filename, short expectedRes) throws java.io.IOException {
    return t32apiJNI.cDDCConf_LoadGlobalConf__SWIG_1(filename, expectedRes);
  }

  public static short LoadGlobalConf(String filename) throws java.io.IOException {
    return t32apiJNI.cDDCConf_LoadGlobalConf__SWIG_2(filename);
  }

  public static short SetMasterhood(etPlant plant, boolean value, short expected) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetMasterhood__SWIG_0(plant.swigValue(), value, expected);
  }

  public static short SetMasterhood(etPlant plant, boolean value) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetMasterhood__SWIG_1(plant.swigValue(), value);
  }

  public static short SetWorkingMode(etPlant plant, short value, short expected) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetWorkingMode__SWIG_0(plant.swigValue(), value, expected);
  }

  public static short SetWorkingMode(etPlant plant, short value) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetWorkingMode__SWIG_1(plant.swigValue(), value);
  }

  public static short SetWorkingModeCustom(etPlant plant, SWIGTYPE_p_bool enableLocalOnOffButton, SWIGTYPE_p_bool enableGlobalOnOffButton, SWIGTYPE_p_bool instEnableTamCrono, SWIGTYPE_p_bool instEnableLocalFasce, SWIGTYPE_p_bool instEnableGlobalFasce, SWIGTYPE_p_bool enableRYRWConsent, SWIGTYPE_p_bool enableTES, SWIGTYPE_p_bool enableRYWAuto, SWIGTYPE_p_bool monitored, SWIGTYPE_p_bool instEnableWeatherCompens, SWIGTYPE_p_bool snifferMode, short expected) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetWorkingModeCustom__SWIG_0(plant.swigValue(), SWIGTYPE_p_bool.getCPtr(enableLocalOnOffButton), SWIGTYPE_p_bool.getCPtr(enableGlobalOnOffButton), SWIGTYPE_p_bool.getCPtr(instEnableTamCrono), SWIGTYPE_p_bool.getCPtr(instEnableLocalFasce), SWIGTYPE_p_bool.getCPtr(instEnableGlobalFasce), SWIGTYPE_p_bool.getCPtr(enableRYRWConsent), SWIGTYPE_p_bool.getCPtr(enableTES), SWIGTYPE_p_bool.getCPtr(enableRYWAuto), SWIGTYPE_p_bool.getCPtr(monitored), SWIGTYPE_p_bool.getCPtr(instEnableWeatherCompens), SWIGTYPE_p_bool.getCPtr(snifferMode), expected);
  }

  public static short SetWorkingModeCustom(etPlant plant, SWIGTYPE_p_bool enableLocalOnOffButton, SWIGTYPE_p_bool enableGlobalOnOffButton, SWIGTYPE_p_bool instEnableTamCrono, SWIGTYPE_p_bool instEnableLocalFasce, SWIGTYPE_p_bool instEnableGlobalFasce, SWIGTYPE_p_bool enableRYRWConsent, SWIGTYPE_p_bool enableTES, SWIGTYPE_p_bool enableRYWAuto, SWIGTYPE_p_bool monitored, SWIGTYPE_p_bool instEnableWeatherCompens, SWIGTYPE_p_bool snifferMode) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetWorkingModeCustom__SWIG_1(plant.swigValue(), SWIGTYPE_p_bool.getCPtr(enableLocalOnOffButton), SWIGTYPE_p_bool.getCPtr(enableGlobalOnOffButton), SWIGTYPE_p_bool.getCPtr(instEnableTamCrono), SWIGTYPE_p_bool.getCPtr(instEnableLocalFasce), SWIGTYPE_p_bool.getCPtr(instEnableGlobalFasce), SWIGTYPE_p_bool.getCPtr(enableRYRWConsent), SWIGTYPE_p_bool.getCPtr(enableTES), SWIGTYPE_p_bool.getCPtr(enableRYWAuto), SWIGTYPE_p_bool.getCPtr(monitored), SWIGTYPE_p_bool.getCPtr(instEnableWeatherCompens), SWIGTYPE_p_bool.getCPtr(snifferMode));
  }

  public static short SetEnableInternalPrimaryRequest(etPlant plant, boolean enable, short expected) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetEnableInternalPrimaryRequest__SWIG_0(plant.swigValue(), enable, expected);
  }

  public static short SetEnableInternalPrimaryRequest(etPlant plant, boolean enable) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetEnableInternalPrimaryRequest__SWIG_1(plant.swigValue(), enable);
  }

  public static short SetEnableExternalPrimaryRequest(etPlant plant, boolean enable, short expected) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetEnableExternalPrimaryRequest__SWIG_0(plant.swigValue(), enable, expected);
  }

  public static short SetEnableExternalPrimaryRequest(etPlant plant, boolean enable) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetEnableExternalPrimaryRequest__SWIG_1(plant.swigValue(), enable);
  }

  public static short SetSetPoint(etPlant plant, short setPoint, short expected) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetSetPoint__SWIG_0(plant.swigValue(), setPoint, expected);
  }

  public static short SetSetPoint(etPlant plant, short setPoint) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetSetPoint__SWIG_1(plant.swigValue(), setPoint);
  }

  public static short SetGradini(etPlant plant, short gradini, short expected) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetGradini__SWIG_0(plant.swigValue(), gradini, expected);
  }

  public static short SetGradini(etPlant plant, short gradini) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetGradini__SWIG_1(plant.swigValue(), gradini);
  }

  public static short SetDifferenziale(etPlant plant, short differenziale, short expected) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetDifferenziale__SWIG_0(plant.swigValue(), differenziale, expected);
  }

  public static short SetDifferenziale(etPlant plant, short differenziale) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetDifferenziale__SWIG_1(plant.swigValue(), differenziale);
  }

  public static short SetInTherm(etPlant plant, long inTherm, short expected) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetInTherm__SWIG_0(plant.swigValue(), inTherm, expected);
  }

  public static short SetInTherm(etPlant plant, long inTherm) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetInTherm__SWIG_1(plant.swigValue(), inTherm);
  }

  public static short SetTESSetPoint(etPlant plant, short TESSetPoint, short expected) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetTESSetPoint__SWIG_0(plant.swigValue(), TESSetPoint, expected);
  }

  public static short SetTESSetPoint(etPlant plant, short TESSetPoint) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetTESSetPoint__SWIG_1(plant.swigValue(), TESSetPoint);
  }

  public static short SetTESDifferenziale(etPlant plant, short TESDifferenziale, short expected) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetTESDifferenziale__SWIG_0(plant.swigValue(), TESDifferenziale, expected);
  }

  public static short SetTESDifferenziale(etPlant plant, short TESDifferenziale) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetTESDifferenziale__SWIG_1(plant.swigValue(), TESDifferenziale);
  }

  public static short SetGlobalCrono(etPlant plant, long globalCrono, short expected) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetGlobalCrono__SWIG_0(plant.swigValue(), globalCrono, expected);
  }

  public static short SetGlobalCrono(etPlant plant, long globalCrono) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetGlobalCrono__SWIG_1(plant.swigValue(), globalCrono);
  }

  public static short SetPartialNetworking(etPlant plant, long partialNetworking, short expected) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetPartialNetworking__SWIG_0(plant.swigValue(), partialNetworking, expected);
  }

  public static short SetPartialNetworking(etPlant plant, long partialNetworking) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetPartialNetworking__SWIG_1(plant.swigValue(), partialNetworking);
  }

  public static short SetIndipCirc(etPlant plant, long indipCirc, short expected) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetIndipCirc__SWIG_0(plant.swigValue(), indipCirc, expected);
  }

  public static short SetIndipCirc(etPlant plant, long indipCirc) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetIndipCirc__SWIG_1(plant.swigValue(), indipCirc);
  }

  public static short SetPrimaryOutSetPointUpperLimit(etPlant plant, short OutSetPointLimit, short expected) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetPrimaryOutSetPointUpperLimit__SWIG_0(plant.swigValue(), OutSetPointLimit, expected);
  }

  public static short SetPrimaryOutSetPointUpperLimit(etPlant plant, short OutSetPointLimit) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetPrimaryOutSetPointUpperLimit__SWIG_1(plant.swigValue(), OutSetPointLimit);
  }

  public static short SetPrimaryInSetPointUpperLimit(etPlant plant, short InSetPointLimit, short expected) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetPrimaryInSetPointUpperLimit__SWIG_0(plant.swigValue(), InSetPointLimit, expected);
  }

  public static short SetPrimaryInSetPointUpperLimit(etPlant plant, short InSetPointLimit) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetPrimaryInSetPointUpperLimit__SWIG_1(plant.swigValue(), InSetPointLimit);
  }

  public static short SetSecondaryOutSetPointUpperLimit(etPlant plant, short OutSetPointLimit, short expected) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetSecondaryOutSetPointUpperLimit__SWIG_0(plant.swigValue(), OutSetPointLimit, expected);
  }

  public static short SetSecondaryOutSetPointUpperLimit(etPlant plant, short OutSetPointLimit) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetSecondaryOutSetPointUpperLimit__SWIG_1(plant.swigValue(), OutSetPointLimit);
  }

  public static short SetCronoDifferenziale(etPlant plant, short cronoDifferenziale, short expected) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetCronoDifferenziale__SWIG_0(plant.swigValue(), cronoDifferenziale, expected);
  }

  public static short SetCronoDifferenziale(etPlant plant, short cronoDifferenziale) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetCronoDifferenziale__SWIG_1(plant.swigValue(), cronoDifferenziale);
  }

  public static short SetSendDig(etPlant plant, long sendDig, short expected) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetSendDig__SWIG_0(plant.swigValue(), sendDig, expected);
  }

  public static short SetSendDig(etPlant plant, long sendDig) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetSendDig__SWIG_1(plant.swigValue(), sendDig);
  }

  public static short SetAlarmPoint(etPlant plant, short alarmPoint, short expected) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetAlarmPoint__SWIG_0(plant.swigValue(), alarmPoint, expected);
  }

  public static short SetAlarmPoint(etPlant plant, short alarmPoint) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetAlarmPoint__SWIG_1(plant.swigValue(), alarmPoint);
  }

  public static short SetInTempAlarm(etPlant plant, long inTempAlarm, short expected) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetInTempAlarm__SWIG_0(plant.swigValue(), inTempAlarm, expected);
  }

  public static short SetInTempAlarm(etPlant plant, long inTempAlarm) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetInTempAlarm__SWIG_1(plant.swigValue(), inTempAlarm);
  }

  public static short SetAlarmReleLogic(etPlant plant, short alarmReleLogic, short expected) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetAlarmReleLogic__SWIG_0(plant.swigValue(), alarmReleLogic, expected);
  }

  public static short SetAlarmReleLogic(etPlant plant, short alarmReleLogic) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetAlarmReleLogic__SWIG_1(plant.swigValue(), alarmReleLogic);
  }

  public static short SetEnableTempAlarm(etPlant plant, long enableTempAlarm, short expected) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetEnableTempAlarm__SWIG_0(plant.swigValue(), enableTempAlarm, expected);
  }

  public static short SetEnableTempAlarm(etPlant plant, long enableTempAlarm) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetEnableTempAlarm__SWIG_1(plant.swigValue(), enableTempAlarm);
  }

  public static short SetSetPointArbitration(etPlant plant, SWIGTYPE_p_tSetPointArbitration setPointArbitration, short expected) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetSetPointArbitration__SWIG_0(plant.swigValue(), SWIGTYPE_p_tSetPointArbitration.getCPtr(setPointArbitration), expected);
  }

  public static short SetSetPointArbitration(etPlant plant, SWIGTYPE_p_tSetPointArbitration setPointArbitration) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetSetPointArbitration__SWIG_1(plant.swigValue(), SWIGTYPE_p_tSetPointArbitration.getCPtr(setPointArbitration));
  }

  public static short SetAvailableWhileOppositeMode(etPlant plant, long AvailableWhileOppositeMode, short expected) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetAvailableWhileOppositeMode__SWIG_0(plant.swigValue(), AvailableWhileOppositeMode, expected);
  }

  public static short SetAvailableWhileOppositeMode(etPlant plant, long AvailableWhileOppositeMode) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetAvailableWhileOppositeMode__SWIG_1(plant.swigValue(), AvailableWhileOppositeMode);
  }

  public static short SetSepGroupGradini(etPlant plant, short gradini, short expected) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetSepGroupGradini__SWIG_0(plant.swigValue(), gradini, expected);
  }

  public static short SetSepGroupGradini(etPlant plant, short gradini) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetSepGroupGradini__SWIG_1(plant.swigValue(), gradini);
  }

  public static short SetSepGroupDifferenziale(etPlant plant, short differenziale, short expected) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetSepGroupDifferenziale__SWIG_0(plant.swigValue(), differenziale, expected);
  }

  public static short SetSepGroupDifferenziale(etPlant plant, short differenziale) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetSepGroupDifferenziale__SWIG_1(plant.swigValue(), differenziale);
  }

  public static short SetSepGroupValveTimeoutThres(etPlant plant, int sepValveTimeoutThres, short expected) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetSepGroupValveTimeoutThres__SWIG_0(plant.swigValue(), sepValveTimeoutThres, expected);
  }

  public static short SetSepGroupValveTimeoutThres(etPlant plant, int sepValveTimeoutThres) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetSepGroupValveTimeoutThres__SWIG_1(plant.swigValue(), sepValveTimeoutThres);
  }

  public static short SetSepGroupForceOffTimeThres(etPlant plant, int forceOffTimeThres, short expected) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetSepGroupForceOffTimeThres__SWIG_0(plant.swigValue(), forceOffTimeThres, expected);
  }

  public static short SetSepGroupForceOffTimeThres(etPlant plant, int forceOffTimeThres) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetSepGroupForceOffTimeThres__SWIG_1(plant.swigValue(), forceOffTimeThres);
  }

  public static short SetSepGroupForceOffAverageAddTimeThres(etPlant plant, int forceOffAverageAddTimeThres, short expected) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetSepGroupForceOffAverageAddTimeThres__SWIG_0(plant.swigValue(), forceOffAverageAddTimeThres, expected);
  }

  public static short SetSepGroupForceOffAverageAddTimeThres(etPlant plant, int forceOffAverageAddTimeThres) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetSepGroupForceOffAverageAddTimeThres__SWIG_1(plant.swigValue(), forceOffAverageAddTimeThres);
  }

  public static short SetSepGroupOutSetPointUpperLimit(etPlant plant, short OutSetPointLimit, short expected) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetSepGroupOutSetPointUpperLimit__SWIG_0(plant.swigValue(), OutSetPointLimit, expected);
  }

  public static short SetSepGroupOutSetPointUpperLimit(etPlant plant, short OutSetPointLimit) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetSepGroupOutSetPointUpperLimit__SWIG_1(plant.swigValue(), OutSetPointLimit);
  }

  public static short SetHeatGroupType(boolean separable, etGroupType groupType, short expected) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetHeatGroupType__SWIG_0(separable, groupType.swigValue(), expected);
  }

  public static short SetHeatGroupType(boolean separable, etGroupType groupType) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetHeatGroupType__SWIG_1(separable, groupType.swigValue());
  }

  public static short SetCHValveTimeoutThres(int CHValveTimeoutThres, short expected) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetCHValveTimeoutThres__SWIG_0(CHValveTimeoutThres, expected);
  }

  public static short SetCHValveTimeoutThres(int CHValveTimeoutThres) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetCHValveTimeoutThres__SWIG_1(CHValveTimeoutThres);
  }

  public static short SetCronoSubstSetPoint(etPlant plant, short setPoint, short expected) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetCronoSubstSetPoint__SWIG_0(plant.swigValue(), setPoint, expected);
  }

  public static short SetCronoSubstSetPoint(etPlant plant, short setPoint) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetCronoSubstSetPoint__SWIG_1(plant.swigValue(), setPoint);
  }

  public static short SetWeatherCompensOffset(etPlant plant, short offset, short expected) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetWeatherCompensOffset__SWIG_0(plant.swigValue(), offset, expected);
  }

  public static short SetWeatherCompensOffset(etPlant plant, short offset) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetWeatherCompensOffset__SWIG_1(plant.swigValue(), offset);
  }

  public static short SetWeatherCompensHeatSlope(int slope, short expected) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetWeatherCompensHeatSlope__SWIG_0(slope, expected);
  }

  public static short SetWeatherCompensHeatSlope(int slope) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetWeatherCompensHeatSlope__SWIG_1(slope);
  }

  public static short SetWeatherCompensChilTF(short tf1, short tf2, short expected) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetWeatherCompensChilTF__SWIG_0(tf1, tf2, expected);
  }

  public static short SetWeatherCompensChilTF(short tf1, short tf2) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetWeatherCompensChilTF__SWIG_1(tf1, tf2);
  }

  public static short SetOTCPT100(etPlant plant, short T100, short expected) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetOTCPT100__SWIG_0(plant.swigValue(), T100, expected);
  }

  public static short SetOTCPT100(etPlant plant, short T100) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetOTCPT100__SWIG_1(plant.swigValue(), T100);
  }

  public static short SetOTCPT0(etPlant plant, short T0, short expected) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetOTCPT0__SWIG_0(plant.swigValue(), T0, expected);
  }

  public static short SetOTCPT0(etPlant plant, short T0) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetOTCPT0__SWIG_1(plant.swigValue(), T0);
  }

  public static short SetOTCPOutTempDelayTicks(etPlant plant, int outTempDelayTicks, short expected) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetOTCPOutTempDelayTicks__SWIG_0(plant.swigValue(), outTempDelayTicks, expected);
  }

  public static short SetOTCPOutTempDelayTicks(etPlant plant, int outTempDelayTicks) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetOTCPOutTempDelayTicks__SWIG_1(plant.swigValue(), outTempDelayTicks);
  }

  public static short SetOTCPEnableOutTempLimit(etPlant plant, boolean enableOutTempLimit, short expected) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetOTCPEnableOutTempLimit__SWIG_0(plant.swigValue(), enableOutTempLimit, expected);
  }

  public static short SetOTCPEnableOutTempLimit(etPlant plant, boolean enableOutTempLimit) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetOTCPEnableOutTempLimit__SWIG_1(plant.swigValue(), enableOutTempLimit);
  }

  public static short SetOTCPOnlyHighEffCategory(etPlant plant, boolean onlyHighEffCategory, short expected) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetOTCPOnlyHighEffCategory__SWIG_0(plant.swigValue(), onlyHighEffCategory, expected);
  }

  public static short SetOTCPOnlyHighEffCategory(etPlant plant, boolean onlyHighEffCategory) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetOTCPOnlyHighEffCategory__SWIG_1(plant.swigValue(), onlyHighEffCategory);
  }

  public static short SetOTCPEnableOutTempThreshold(etPlant plant, boolean enableOutTempThreshold, short expected) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetOTCPEnableOutTempThreshold__SWIG_0(plant.swigValue(), enableOutTempThreshold, expected);
  }

  public static short SetOTCPEnableOutTempThreshold(etPlant plant, boolean enableOutTempThreshold) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetOTCPEnableOutTempThreshold__SWIG_1(plant.swigValue(), enableOutTempThreshold);
  }

  public static short SetOTCPOutTempThreshold(etPlant plant, short outTempThreshold, short expected) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetOTCPOutTempThreshold__SWIG_0(plant.swigValue(), outTempThreshold, expected);
  }

  public static short SetOTCPOutTempThreshold(etPlant plant, short outTempThreshold) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetOTCPOutTempThreshold__SWIG_1(plant.swigValue(), outTempThreshold);
  }

  public static short SetCatReleaseIntegral(etPlant plant, boolean separable, short category, int releaseIntegral, short expected) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetCatReleaseIntegral__SWIG_0(plant.swigValue(), separable, category, releaseIntegral, expected);
  }

  public static short SetCatReleaseIntegral(etPlant plant, boolean separable, short category, int releaseIntegral) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetCatReleaseIntegral__SWIG_1(plant.swigValue(), separable, category, releaseIntegral);
  }

  public static short SetCatResetIntegral(etPlant plant, boolean separable, short category, int resetIntegral, short expected) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetCatResetIntegral__SWIG_0(plant.swigValue(), separable, category, resetIntegral, expected);
  }

  public static short SetCatResetIntegral(etPlant plant, boolean separable, short category, int resetIntegral) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetCatResetIntegral__SWIG_1(plant.swigValue(), separable, category, resetIntegral);
  }

  public static short SetCatLockingTime(etPlant plant, boolean separable, short category, int lockingTime, short expected) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetCatLockingTime__SWIG_0(plant.swigValue(), separable, category, lockingTime, expected);
  }

  public static short SetCatLockingTime(etPlant plant, boolean separable, short category, int lockingTime) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetCatLockingTime__SWIG_1(plant.swigValue(), separable, category, lockingTime);
  }

  public static short SetCatSingleUnitPowerWatt(etPlant plant, boolean separable, short category, int singleUnitPowerWatt, short expected) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetCatSingleUnitPowerWatt__SWIG_0(plant.swigValue(), separable, category, singleUnitPowerWatt, expected);
  }

  public static short SetCatSingleUnitPowerWatt(etPlant plant, boolean separable, short category, int singleUnitPowerWatt) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetCatSingleUnitPowerWatt__SWIG_1(plant.swigValue(), separable, category, singleUnitPowerWatt);
  }

  public static short SetCatNumberOfStages(etPlant plant, boolean separable, short category, short numberOfStages, short expected) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetCatNumberOfStages__SWIG_0(plant.swigValue(), separable, category, numberOfStages, expected);
  }

  public static short SetCatNumberOfStages(etPlant plant, boolean separable, short category, short numberOfStages) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetCatNumberOfStages__SWIG_1(plant.swigValue(), separable, category, numberOfStages);
  }

  public static short SetCatMinRuntime(etPlant plant, boolean separable, short category, int minRuntime, short expected) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetCatMinRuntime__SWIG_0(plant.swigValue(), separable, category, minRuntime, expected);
  }

  public static short SetCatMinRuntime(etPlant plant, boolean separable, short category, int minRuntime) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetCatMinRuntime__SWIG_1(plant.swigValue(), separable, category, minRuntime);
  }

  public static short SetNightModeOptions(etPlant plant, boolean enable, boolean disableSilentiable, boolean disableNonSilentiable, short expected) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetNightModeOptions__SWIG_0(plant.swigValue(), enable, disableSilentiable, disableNonSilentiable, expected);
  }

  public static short SetNightModeOptions(etPlant plant, boolean enable, boolean disableSilentiable, boolean disableNonSilentiable) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetNightModeOptions__SWIG_1(plant.swigValue(), enable, disableSilentiable, disableNonSilentiable);
  }

  public static short SetInstNightModeOptions(etPlant plant, boolean enable, short expected) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetInstNightModeOptions__SWIG_0(plant.swigValue(), enable, expected);
  }

  public static short SetInstNightModeOptions(etPlant plant, boolean enable) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetInstNightModeOptions__SWIG_1(plant.swigValue(), enable);
  }

  public static short SetUserEnableGlobalFasce(etPlant plant, boolean enable, short expected) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetUserEnableGlobalFasce__SWIG_0(plant.swigValue(), enable, expected);
  }

  public static short SetUserEnableGlobalFasce(etPlant plant, boolean enable) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetUserEnableGlobalFasce__SWIG_1(plant.swigValue(), enable);
  }

  public static short SetUserEnableWeatherCompens(etPlant plant, boolean enable, short expected) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetUserEnableWeatherCompens__SWIG_0(plant.swigValue(), enable, expected);
  }

  public static short SetUserEnableWeatherCompens(etPlant plant, boolean enable) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetUserEnableWeatherCompens__SWIG_1(plant.swigValue(), enable);
  }

  public static short SetChilHeatSwitchRB100Id(int id10, short expected) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetChilHeatSwitchRB100Id__SWIG_0(id10, expected);
  }

  public static short SetChilHeatSwitchRB100Id(int id10) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetChilHeatSwitchRB100Id__SWIG_1(id10);
  }

  public static void SetTESFilterTimeConstant(long TESFilterTimeConstant) throws java.io.IOException {
    t32apiJNI.cDDCConf_SetTESFilterTimeConstant(TESFilterTimeConstant);
  }

  public static short SetLocalOnButton(etPlant plant, long start, short expected) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetLocalOnButton__SWIG_0(plant.swigValue(), start, expected);
  }

  public static short SetLocalOnButton(etPlant plant, long start) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetLocalOnButton__SWIG_1(plant.swigValue(), start);
  }

  public static short SetGlobalOnButton(etPlant plant, long start, short expected) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetGlobalOnButton__SWIG_0(plant.swigValue(), start, expected);
  }

  public static short SetGlobalOnButton(etPlant plant, long start) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetGlobalOnButton__SWIG_1(plant.swigValue(), start);
  }

  public static short SetChilHeatButton(etPlant mode, short expected) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetChilHeatButton__SWIG_0(mode.swigValue(), expected);
  }

  public static short SetChilHeatButton(etPlant mode) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetChilHeatButton__SWIG_1(mode.swigValue());
  }

  public static short SetSanitaryReqOnButton(boolean separable, boolean on, short expected) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetSanitaryReqOnButton__SWIG_0(separable, on, expected);
  }

  public static short SetSanitaryReqOnButton(boolean separable, boolean on) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetSanitaryReqOnButton__SWIG_1(separable, on);
  }

  public static void ResetTESFilter() {
    t32apiJNI.cDDCConf_ResetTESFilter();
  }

  public static short SetFasceGlobaliFillUp(etPlant plant, short setPoint, short expected) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetFasceGlobaliFillUp__SWIG_0(plant.swigValue(), setPoint, expected);
  }

  public static short SetFasceGlobaliFillUp(etPlant plant, short setPoint) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetFasceGlobaliFillUp__SWIG_1(plant.swigValue(), setPoint);
  }

  public static short SetFasceSanitaryFillUp(boolean separable, short setPoint, short expected) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetFasceSanitaryFillUp__SWIG_0(separable, setPoint, expected);
  }

  public static short SetFasceSanitaryFillUp(boolean separable, short setPoint) throws java.io.IOException {
    return t32apiJNI.cDDCConf_SetFasceSanitaryFillUp__SWIG_1(separable, setPoint);
  }

  public static short GMTakeIOMod(long id10, short expected) throws java.io.IOException {
    return t32apiJNI.cDDCConf_GMTakeIOMod__SWIG_0(id10, expected);
  }

  public static short GMTakeIOMod(long id10) throws java.io.IOException {
    return t32apiJNI.cDDCConf_GMTakeIOMod__SWIG_1(id10);
  }

  public static short EnableModbusBMSControl(etPlant plant, boolean enable, short expected) throws java.io.IOException {
    return t32apiJNI.cDDCConf_EnableModbusBMSControl__SWIG_0(plant.swigValue(), enable, expected);
  }

  public static short EnableModbusBMSControl(etPlant plant, boolean enable) throws java.io.IOException {
    return t32apiJNI.cDDCConf_EnableModbusBMSControl__SWIG_1(plant.swigValue(), enable);
  }

  public static short EnableModbusRAControl(etPlant plant, boolean enable, short expected) throws java.io.IOException {
    return t32apiJNI.cDDCConf_EnableModbusRAControl__SWIG_0(plant.swigValue(), enable, expected);
  }

  public static short EnableModbusRAControl(etPlant plant, boolean enable) throws java.io.IOException {
    return t32apiJNI.cDDCConf_EnableModbusRAControl__SWIG_1(plant.swigValue(), enable);
  }

  public static short EnableModbusProtocol2_0(boolean RS232, short expected) throws java.io.IOException {
    return t32apiJNI.cDDCConf_EnableModbusProtocol2_0__SWIG_0(RS232, expected);
  }

  public static short EnableModbusProtocol2_0(boolean RS232) throws java.io.IOException {
    return t32apiJNI.cDDCConf_EnableModbusProtocol2_0__SWIG_1(RS232);
  }

  public static short EnableModbusProtocol2_0() throws java.io.IOException {
    return t32apiJNI.cDDCConf_EnableModbusProtocol2_0__SWIG_2();
  }

  public cDDCConf() {
    this(t32apiJNI.new_cDDCConf(), true);
  }

}
