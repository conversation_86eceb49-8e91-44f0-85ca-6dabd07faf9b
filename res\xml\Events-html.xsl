<?xml version="1.0" encoding="UTF-8"?>
<xsl:stylesheet version="1.0" xmlns:xsl="http://www.w3.org/1999/XSL/Transform">

<xsl:param name="vLanguage" 	select="root/events/@language"/>

<xsl:variable name="vStrFile"  	select="root/events/@string_table"/>
<xsl:variable name="vUNStrFile" select="'UnitName-strings.xml'"/>

<xsl:variable name="vStrings" 		select="document($vStrFile)/event_strings/language_set[@language=$vLanguage]/*"/>
<xsl:variable name="vUnitNameStrs" 	select="document($vUNStrFile)/unit_name_strings/language_set[@language=$vLanguage]/string"/>

<xsl:variable name="vRTCIsSet" select="boolean(number(root/events/@RTC_is_set))"/>

<xsl:template match="/root">
<html>
<title>K18 Event Logger</title>
<body>
<xsl:apply-templates select="identification"/>  
<xsl:apply-templates select="related_files"/>  
 <h2>K18 Event Logger</h2>
    <table border="1">
      <tr bgcolor="#9acd32">
	<xsl:choose>
	<xsl:when test="$vRTCIsSet">
		<th>Date</th>
		<th>Time</th>
	</xsl:when>
	<xsl:otherwise>
		<th>Days</th>
		<th>hh:mm:ss</th>
	</xsl:otherwise>
	</xsl:choose>
        <th>Event</th>
        <th>Activation</th>
        <th>Description</th>
      </tr>
      <xsl:for-each select="events/event">
      <tr>
	<xsl:choose>
	<xsl:when test="$vRTCIsSet">
		<xsl:choose>
		<xsl:when test="boolean(number(timestamp/@updated))">
			<td align="right"><xsl:value-of select="timestamp/@date"/></td>
			<td align="right"><xsl:value-of select="timestamp/@time"/></td>
		</xsl:when>
		<xsl:otherwise>
			<td align="right" bgcolor="#C0C0C0"><xsl:value-of select="timestamp/@date"/></td>
			<td align="right" bgcolor="#C0C0C0"><xsl:value-of select="timestamp/@time"/></td>
		</xsl:otherwise>
		</xsl:choose>	
	</xsl:when>
	<xsl:otherwise>
		<td align="right"><xsl:value-of select="timestamp/@days"/></td>
		<td align="right"><xsl:value-of select="timestamp/@hh_mm_ss"/></td>
	</xsl:otherwise>
	</xsl:choose>       
	<td><b><xsl:value-of select="category"/><xsl:value-of select="format-number(id, '000')"/></b></td>
	<td><xsl:if test="act != 'INSTANT'"> <xsl:value-of select="act"/> </xsl:if></td>
	<xsl:variable name="vCode" select="string_code"/>		
        <td><xsl:value-of select="$vStrings[number($vCode)]"/></td> <!-- both string codes and XSL arrays start from 1 --> 
      </tr>
      </xsl:for-each>
    </table>
</body>
</html>
</xsl:template>

<!-- TO DO: use id string file? --> 
<xsl:template match="identification">
	<table border="1">
		<tr bgcolor="#9acd32">
			<th>Name</th>
			<th>Appliance Serial</th>
			<th>OEM</th>
			<th>OEM Compatibility</th>
			<th>Board Serial</th>
			<th>FW Version</th>
			<th>HW Version</th>
			<th>BL Version</th>
			<th>Unit Type</th>
			<th>Heat Module Type Major</th>
			<th>Heat Module Type Minor</th>
		</tr>
		<tr>
			<xsl:variable name="utCode" select="unit_type"/>	<!-- variable needed for number function -->
			<td align="right"><xsl:value-of select="$vUnitNameStrs[number($utCode+1)]"/></td>
			<td align="right"><xsl:value-of select="serial"/></td>
			<td align="right"><xsl:value-of select="OEM"/></td>
			<td align="right"><xsl:value-of select="OEM_compatibility"/></td>
			<td align="right"><xsl:value-of select="board_serial"/></td>
			<td align="right"><xsl:value-of select="FW_version"/></td>
			<td align="right"><xsl:value-of select="HW_version"/></td>
			<td align="right"><xsl:value-of select="BL_version"/></td>
			<td align="right"><xsl:value-of select="unit_type"/></td>
			<td align="right"><xsl:value-of select="heat_module_type_major"/></td>
			<td align="right"><xsl:value-of select="heat_module_type_minor"/></td>
		</tr>
	</table>
	<br />
</xsl:template>

<xsl:template match="related_files">
<xsl:for-each select="*">
	<xsl:variable name="finName" select="concat(substring-before(.,'.xml'),'.html')"/>
	<a href="{$finName}"><xsl:value-of select="$finName"/></a><br/>
</xsl:for-each>
<br/>
</xsl:template>

</xsl:stylesheet>
