/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 3.0.12
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package it.robur.test.physicalDCC.t32api;

public final class etOEM {
  public final static etOEM oemNone = new etOEM("oemNone");
  public final static etOEM oemDeDietrich = new etOEM("oemDeDietrich");
  public final static etOEM oemBuderus = new etOEM("oemBuderus");
  public final static etOEM oemBosch = new etOEM("oemBosch");
  public final static etOEM MAX_OEMS = new etOEM("MAX_OEMS");

  public final int swigValue() {
    return swigValue;
  }

  public String toString() {
    return swigName;
  }

  public static etOEM swigToEnum(int swigValue) {
    if (swigValue < swigValues.length && swigValue >= 0 && swigValues[swigValue].swigValue == swigValue)
      return swigValues[swigValue];
    for (int i = 0; i < swigValues.length; i++)
      if (swigValues[i].swigValue == swigValue)
        return swigValues[i];
    throw new IllegalArgumentException("No enum " + etOEM.class + " with value " + swigValue);
  }

  private etOEM(String swigName) {
    this.swigName = swigName;
    this.swigValue = swigNext++;
  }

  private etOEM(String swigName, int swigValue) {
    this.swigName = swigName;
    this.swigValue = swigValue;
    swigNext = swigValue+1;
  }

  private etOEM(String swigName, etOEM swigEnum) {
    this.swigName = swigName;
    this.swigValue = swigEnum.swigValue;
    swigNext = this.swigValue+1;
  }

  private static etOEM[] swigValues = { oemNone, oemDeDietrich, oemBuderus, oemBosch, MAX_OEMS };
  private static int swigNext = 0;
  private final int swigValue;
  private final String swigName;
}

