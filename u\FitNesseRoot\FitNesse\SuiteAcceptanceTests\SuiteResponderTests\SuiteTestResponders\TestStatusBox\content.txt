When you execute a test page, a status box will appear in the upper right hand corner that will provide a link to the error log.

----

Create a simple test page

|script|Page Builder|
|line|${SUT_PATH}|
|line|!-|!-fitnesse.testutil.PassFixture-!-!!-|-!|
|page|!-TestPage-!|

Now run the test page.

|Response Requester.|
|uri   |valid?|
|!-TestPage?responder=test-!|true|

|Response Examiner.|
|contents?|
||

Check to make sure the box exists.

|Response Examiner.                       |
|type    |pattern                |matches?|
|contents|<span id="test-action">|true    |
