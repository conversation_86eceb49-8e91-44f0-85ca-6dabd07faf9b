/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 3.0.12
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package it.robur.test.physicalDCC.t32api;

public final class etResIgnBoxState {
  public final static etResIgnBoxState rsNone = new etResIgnBoxState("rsNone");
  public final static etResIgnBoxState rsSendRequest = new etResIgnBoxState("rsSendRequest");
  public final static etResIgnBoxState rsWaitAnswer = new etResIgnBoxState("rsWaitAnswer");

  public final int swigValue() {
    return swigValue;
  }

  public String toString() {
    return swigName;
  }

  public static etResIgnBoxState swigToEnum(int swigValue) {
    if (swigValue < swigValues.length && swigValue >= 0 && swigValues[swigValue].swigValue == swigValue)
      return swigValues[swigValue];
    for (int i = 0; i < swigValues.length; i++)
      if (swigValues[i].swigValue == swigValue)
        return swigValues[i];
    throw new IllegalArgumentException("No enum " + etResIgnBoxState.class + " with value " + swigValue);
  }

  private etResIgnBoxState(String swigName) {
    this.swigName = swigName;
    this.swigValue = swigNext++;
  }

  private etResIgnBoxState(String swigName, int swigValue) {
    this.swigName = swigName;
    this.swigValue = swigValue;
    swigNext = swigValue+1;
  }

  private etResIgnBoxState(String swigName, etResIgnBoxState swigEnum) {
    this.swigName = swigName;
    this.swigValue = swigEnum.swigValue;
    swigNext = this.swigValue+1;
  }

  private static etResIgnBoxState[] swigValues = { rsNone, rsSendRequest, rsWaitAnswer };
  private static int swigNext = 0;
  private final int swigValue;
  private final String swigName;
}

