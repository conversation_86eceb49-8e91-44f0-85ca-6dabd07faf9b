/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 3.0.12
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package it.robur.test.physicalDCC.t32api;

public final class etTransientMode {
  public final static etTransientMode tmNone = new etTransientMode("tmNone");
  public final static etTransientMode tmTransientAndOff = new etTransientMode("tmTransientAndOff");
  public final static etTransientMode tmSwitchAll = new etTransientMode("tmSwitchAll");

  public final int swigValue() {
    return swigValue;
  }

  public String toString() {
    return swigName;
  }

  public static etTransientMode swigToEnum(int swigValue) {
    if (swigValue < swigValues.length && swigValue >= 0 && swigValues[swigValue].swigValue == swigValue)
      return swigValues[swigValue];
    for (int i = 0; i < swigValues.length; i++)
      if (swigValues[i].swigValue == swigValue)
        return swigValues[i];
    throw new IllegalArgumentException("No enum " + etTransientMode.class + " with value " + swigValue);
  }

  private etTransientMode(String swigName) {
    this.swigName = swigName;
    this.swigValue = swigNext++;
  }

  private etTransientMode(String swigName, int swigValue) {
    this.swigName = swigName;
    this.swigValue = swigValue;
    swigNext = swigValue+1;
  }

  private etTransientMode(String swigName, etTransientMode swigEnum) {
    this.swigName = swigName;
    this.swigValue = swigEnum.swigValue;
    swigNext = this.swigValue+1;
  }

  private static etTransientMode[] swigValues = { tmNone, tmTransientAndOff, tmSwitchAll };
  private static int swigNext = 0;
  private final int swigValue;
  private final String swigName;
}

