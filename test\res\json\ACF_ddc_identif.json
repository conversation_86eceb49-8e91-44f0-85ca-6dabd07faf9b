{"at": "2019-10-16T12:04:39Z", "identification": {"sub_plants": [{"id": "sp_cool", "plant_idref": "p_cool", "type": "cool", "num_of_modules": "2"}], "ddc": {"VPN_id": "64:51:06:5f:da:17", "FW_version": "4.019", "serial": "7694", "can_address": "960", "C_H_2pipes": "false", "FW_version_internal": "0", "HW_version": "2", "BL_version": "4", "num_of_machines": "2"}, "sub_circuits": [{"id": "sc_cool_base", "type": "cool_base", "circuit_idref": ["c_cool"], "num_of_modules": "2"}], "plants": [{"ddc_is_master": "true", "can_address": "4", "id": "p_cool", "type": "cool"}], "super_services": [{"id": "ss_cool_space", "type": "cooling"}], "services": [{"super_service_idref": "ss_cool_space", "id": "s_cool_space", "circuit_idref": "c_cool", "type": "cooling"}], "circuits": [{"sub_plant_idref": "sp_cool", "id": "c_cool", "type": "cool"}], "modules": [{"module_type_major": "3", "can_address": "11", "sub_circuit_idref": "sc_cool_base", "id": "C011", "type": "cool", "unit_type": "0"}, {"module_type_major": "13", "can_address": "14", "sub_circuit_idref": "sc_cool_base", "id": "C014", "type": "cool", "unit_type": "9"}]}, "serial": "7694", "metric_ids": {"digital_ids": [{"code": 0, "id": "not_config"}, {"code": 1, "id": "excluded"}, {"code": 2, "id": "offline"}, {"code": 3, "id": "on"}, {"code": 4, "id": "defrost"}, {"code": 5, "id": "off_limit"}, {"code": 6, "id": "flow_switch"}, {"code": 7, "id": "alarm"}, {"code": 8, "id": "warning"}, {"code": 9, "id": "error"}, {"code": 10, "id": "DHW"}, {"code": 11, "id": "flame"}, {"code": 12, "id": "silent_mode"}], "circuit_analog_ids": [{"code": 0, "id": "out_t", "measure_unit": "°C"}, {"code": 1, "id": "in_t", "measure_unit": "°C"}, {"code": 2, "id": "ext_amb_t", "measure_unit": "°C"}, {"code": 3, "id": "current_setpoint", "measure_unit": "°C"}, {"code": 4, "id": "internal_amb_t", "measure_unit": "°C"}], "derived_data_ids": [{"code": 0, "id": "separated"}, {"code": 1, "id": "thermal_input"}, {"code": 2, "id": "thermal_output"}], "super_service_digital_ids": [{"code": 0, "id": "on"}], "circuit_enum_ids": [{"code": 0, "id": "alert"}, {"code": 1, "id": "error_sem"}], "analog_ids": [{"code": 0, "id": "out_t", "measure_unit": "°C"}, {"code": 1, "id": "in_t", "measure_unit": "°C"}, {"code": 2, "id": "ext_amb_body_t", "measure_unit": "°C"}, {"code": 3, "id": "gen_t", "measure_unit": "°C"}, {"code": 4, "id": "condens_t", "measure_unit": "°C"}, {"code": 5, "id": "aux1_t", "measure_unit": "°C"}, {"code": 6, "id": "aux2_t", "measure_unit": "°C"}, {"code": 7, "id": "supply_volt", "measure_unit": "V"}, {"code": 8, "id": "sol_pump_speed", "measure_unit": "RPM"}, {"code": 9, "id": "fan_volt", "measure_unit": "V"}, {"code": 10, "id": "absorb_t", "measure_unit": "°C"}, {"code": 11, "id": "evap_t", "measure_unit": "°C"}, {"code": 16, "id": "primary_out_t", "measure_unit": "°C"}, {"code": 17, "id": "blower_speed", "measure_unit": "RPM"}, {"code": 18, "id": "water_flow", "measure_unit": "l/h"}, {"code": 19, "id": "water_pump_volt", "measure_unit": "V"}, {"code": 20, "id": "analog_in_volt", "measure_unit": "V"}, {"code": 21, "id": "power_feedback", "measure_unit": "%"}, {"code": 22, "id": "mix_t", "measure_unit": "°C"}, {"code": 23, "id": "flue_t", "measure_unit": "°C"}, {"code": 24, "id": "cond_sensor_volt", "measure_unit": "V"}, {"code": 25, "id": "gen_fin_t", "measure_unit": "°C"}, {"code": 32, "id": "gahpw_cool_out_t", "measure_unit": "°C"}, {"code": 33, "id": "gahpw_cool_in_t", "measure_unit": "°C"}, {"code": 34, "id": "unkn_ana_34", "measure_unit": ""}, {"code": 35, "id": "unkn_ana_35", "measure_unit": ""}, {"code": 36, "id": "unkn_ana_36", "measure_unit": ""}], "circuit_digital_ids": [{"code": 0, "id": "active"}, {"code": 1, "id": "on"}], "sub_circuit_digital_ids": [{"code": 0, "id": "separated"}], "super_service_enum_ids": [{"code": 0, "id": "cool_heat_active"}], "ddc_analog_ids": [{"id": "supply_volt", "code": 0, "measure_unit": "V"}]}, "statistic_ids": [{"code": 0, "id": "working_time", "measure_unit": "s"}, {"code": 1, "id": "ignitions", "measure_unit": ""}, {"code": 2, "id": "defrostings", "measure_unit": ""}, {"code": 3, "id": "c_h_switchings", "measure_unit": ""}], "daily_ids": [{"code": 0, "id": "d_gue", "measure_unit": ""}, {"code": 1, "id": "d_thermal_input", "measure_unit": "W"}, {"code": 2, "id": "d_thermal_output", "measure_unit": "W"}], "configuration_ids": {"service_element_ids": [{"code": 0, "read_only": "false", "id": "default_setpoint", "measure_unit": "°C"}, {"code": 1, "id": "chrono_force_setpoint_level", "read_only": "false", "measure_unit": ""}, {"code": 2, "id": "max_setpoint_dhw", "read_only": "false", "measure_unit": "°C"}, {"code": 3, "id": "min_setpoint_dhw", "read_only": "true", "measure_unit": "°C"}], "power_lim_element_ids": [{"code": 0, "read_only": "false", "id": "enable_max_pow_lim", "measure_unit": ""}, {"code": 1, "read_only": "false", "id": "t_ext_at_max", "measure_unit": "°C"}, {"code": 2, "read_only": "false", "id": "t_ext_at_zero", "measure_unit": "°C"}, {"code": 3, "read_only": "false", "id": "delay", "measure_unit": "min"}, {"code": 4, "read_only": "false", "id": "enable_boiler_lock", "measure_unit": ""}, {"code": 5, "read_only": "false", "id": "threshold", "measure_unit": "°C"}], "plant_element_ids": [{"code": 0, "read_only": "false", "id": "water_pump_mode", "measure_unit": ""}], "module_element_ids": [{"code": 0, "read_only": "false", "id": "exclusion_switch", "measure_unit": ""}, {"code": 1, "read_only": "true", "id": "category", "measure_unit": ""}, {"code": 2, "read_only": "true", "id": "module_type_minor", "measure_unit": ""}, {"code": 3, "read_only": "true", "id": "FW_version_major", "measure_unit": ""}, {"code": 4, "read_only": "true", "id": "FW_version_minor", "measure_unit": ""}, {"code": 5, "read_only": "true", "id": "serial_number", "measure_unit": ""}, {"code": 6, "read_only": "true", "id": "electronics_serial", "measure_unit": ""}, {"code": 7, "id": "HW_version", "read_only": "true", "measure_unit": ""}, {"code": 8, "id": "HW_version_nordgas_if", "read_only": "true", "measure_unit": ""}, {"code": 9, "id": "FW_version_major_nordgas_if", "read_only": "true", "measure_unit": ""}, {"code": 10, "id": "FW_version_minor_nordgas_if", "read_only": "true", "measure_unit": ""}], "quiet_mode_timer_element_ids": [{"code": 0, "read_only": "false", "id": "installer_level_enable", "measure_unit": ""}, {"code": 1, "read_only": "false", "id": "user_level_enable", "measure_unit": ""}, {"code": 2, "read_only": "false", "id": "disable_quiet_units", "measure_unit": ""}, {"code": 3, "read_only": "false", "id": "disable_non_quiet_units", "measure_unit": ""}], "sub_plant_element_ids": [{"code": 0, "read_only": "false", "id": "thermostatation_mode", "measure_unit": ""}], "time_period_element_ids": [{"code": 0, "read_only": "false", "id": "on_time", "measure_unit": "min"}, {"code": 1, "read_only": "false", "id": "off_time", "measure_unit": "min"}, {"code": 2, "read_only": "false", "id": "enabled", "measure_unit": ""}, {"code": 3, "read_only": "false", "id": "setpoint", "measure_unit": "°C"}], "chrono_thermo_element_ids": [{"code": 0, "read_only": "false", "id": "t1", "measure_unit": "°C"}, {"code": 1, "read_only": "false", "id": "t2", "measure_unit": "°C"}, {"code": 2, "read_only": "false", "id": "t3", "measure_unit": "°C"}], "category_element_ids": [{"code": 0, "read_only": "false", "id": "power", "measure_unit": "kW"}, {"code": 1, "read_only": "false", "id": "priority", "measure_unit": ""}, {"code": 2, "read_only": "false", "id": "locking_time", "measure_unit": "min"}, {"code": 3, "read_only": "false", "id": "min_run_time", "measure_unit": "min"}, {"code": 4, "read_only": "false", "id": "num_of_stages", "measure_unit": ""}, {"code": 5, "read_only": "false", "id": "release_integral", "measure_unit": "°C * min"}, {"code": 6, "read_only": "false", "id": "reset_integral", "measure_unit": "°C * min"}, {"code": 7, "read_only": "false", "id": "circ_stop_delay", "measure_unit": "s"}, {"code": 8, "read_only": "false", "id": "out_temp_limit", "measure_unit": "°C"}, {"code": 9, "read_only": "false", "id": "in_temp_limit", "measure_unit": "°C"}], "super_service_element_ids": [{"code": 0, "read_only": "false", "id": "on_off_switch", "measure_unit": ""}, {"code": 1, "read_only": "false", "id": "cool_heat_switch", "measure_unit": ""}, {"code": 2, "read_only": "false", "id": "working_mode", "measure_unit": ""}, {"code": 3, "read_only": "false", "id": "enable_global_water_timer", "measure_unit": ""}, {"code": 4, "read_only": "false", "id": "wm_enable_global_on_off_button", "measure_unit": ""}, {"code": 5, "read_only": "false", "id": "wm_enable_local_on_off_button", "measure_unit": ""}, {"code": 6, "read_only": "false", "id": "wm_enable_global_water_timer", "measure_unit": ""}, {"code": 7, "read_only": "false", "id": "wm_enable_local_water_timer", "measure_unit": ""}, {"code": 8, "read_only": "false", "id": "wm_enable_crono_ambient_temp", "measure_unit": ""}, {"code": 9, "read_only": "false", "id": "wm_enable_crono_weather_comp", "measure_unit": ""}, {"code": 10, "read_only": "false", "id": "wm_enable_outdoor_temp", "measure_unit": ""}, {"code": 11, "read_only": "false", "id": "wm_enable_RY_RW_on_off_switch", "measure_unit": ""}, {"code": 12, "read_only": "false", "id": "wm_enable_RY_RW_cool_heat_switch", "measure_unit": ""}, {"code": 13, "read_only": "false", "id": "enable_chronothermostat", "measure_unit": ""}, {"code": 14, "read_only": "false", "id": "enable_internal_request", "measure_unit": ""}, {"code": 15, "read_only": "false", "id": "enable_external_request", "measure_unit": ""}], "weather_comp_element_ids": [{"code": 0, "read_only": "false", "id": "enable", "measure_unit": ""}, {"code": 1, "read_only": "false", "id": "TF1", "measure_unit": "°C"}, {"code": 2, "read_only": "false", "id": "TF2", "measure_unit": "°C"}, {"code": 3, "read_only": "false", "id": "slope", "measure_unit": ""}, {"code": 4, "read_only": "false", "id": "offset", "measure_unit": "°C"}, {"code": 5, "read_only": "false", "id": "min_in_setp", "measure_unit": "°C"}, {"code": 6, "read_only": "false", "id": "min_out_setp", "measure_unit": "°C"}, {"code": 7, "read_only": "false", "id": "max_in_setp", "measure_unit": "°C"}, {"code": 8, "read_only": "false", "id": "max_out_setp", "measure_unit": "°C"}], "ddc_element_ids": [{"code": 0, "read_only": "false", "id": "gahp_w_priority_switch", "measure_unit": ""}], "circuit_element_ids": [{"code": 0, "read_only": "false", "id": "differential", "measure_unit": "K"}, {"code": 1, "read_only": "false", "id": "init_power_percentage", "measure_unit": "%"}]}}