!3 A page may ''not'' be renamed to a name with dots in it.
!3 Another way of saying this is that renaming a page does not change it's position.

First build the page to rename, and another page to try to move it beneath.

|Page creator.|
|Page name.|Page contents.|valid?|
|!-MyPage-!|X|true|
|!-TargetPage-!|X|true|

Then rename that page.

|Response Requester.|
|uri   |status?|
|!-MyPage?responder=renamePage&newName=TargetPage.SomePage-!|400|

Make sure that we got an error message.

|Response Examiner.|
|type  |pattern|matches?|wrapped html?|
|contents|Cannot rename|true||

The renamed page should not exist.

!|Response Requester.|
|uri|valid?|
|TargetPage.SomePage?getPage&dontCreatePage|false|

The old page should still exist.

|Response Requester.|
|uri|valid?|contents?|
|!-MyPage-!|true||
