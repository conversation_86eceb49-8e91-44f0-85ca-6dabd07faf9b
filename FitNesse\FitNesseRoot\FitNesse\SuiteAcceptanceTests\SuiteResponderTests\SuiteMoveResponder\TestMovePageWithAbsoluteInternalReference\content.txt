!3 When we move a page from one location to another, all references to that page should be changed.
This includes absolute references on the parent page.

First build a page, a subpage to move, and a target page to move it to.

|Page creator.|
|Page name.          |Page contents.|valid?|
|!-ParentPage-!        |!-.ParentPage.SubPage-!           |true   |
|!-ParentPage.SubPage-!|sub page              |true   |
|!-NewParentPage-!     |x                     |true   |
|!-ReferingPage-!      |!-ParentPage.SubPage-!|true   |

Then move the sub page.

|Response Requester.|
|uri   |status?|
|!-ParentPage.SubPage?responder=movePage&newLocation=NewParentPage&refactorReferences=on-!||

Next fetch old parent page and make sure the reference has been changed.

|Response Requester.|
|uri|valid?|contents?|
|!-ParentPage-!|true||

|Response Examiner.|
|type  |pattern|matches?|wrapped html?|
|contents|!-NewParentPage.SubPage-!|true||
