/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 3.0.12
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package it.robur.test.physicalDCC.t32api;

public class cT32Pointer {
  private transient long swigCPtr;
  protected transient boolean swigCMemOwn;

  protected cT32Pointer(long cPtr, boolean cMemoryOwn) {
    swigCMemOwn = cMemoryOwn;
    swigCPtr = cPtr;
  }

  protected static long getCPtr(cT32Pointer obj) {
    return (obj == null) ? 0 : obj.swigCPtr;
  }

  protected void finalize() {
    delete();
  }

  public synchronized void delete() {
    if (swigCPtr != 0) {
      if (swigCMemOwn) {
        swigCMemOwn = false;
        t32apiJNI.delete_cT32Pointer(swigCPtr);
      }
      swigCPtr = 0;
    }
  }

  public void setPntr(SWIGTYPE_p_void value) {
    t32apiJNI.cT32Pointer_Pntr_set(swigCPtr, this, SWIGTYPE_p_void.getCPtr(value));
  }

  public SWIGTYPE_p_void getPntr() {
    long cPtr = t32apiJNI.cT32Pointer_Pntr_get(swigCPtr, this);
    return (cPtr == 0) ? null : new SWIGTYPE_p_void(cPtr, false);
  }

  public void setType(String value) {
    t32apiJNI.cT32Pointer_Type_set(swigCPtr, this, value);
  }

  public String getType() {
    return t32apiJNI.cT32Pointer_Type_get(swigCPtr, this);
  }

  public cT32Pointer(String type) {
    this(t32apiJNI.new_cT32Pointer(type), true);
  }

  public void Allocate() throws java.io.IOException {
    t32apiJNI.cT32Pointer_Allocate(swigCPtr, this);
  }

  public void AllocateSize(String baseType, long multiplier, boolean zero) throws java.io.IOException {
    t32apiJNI.cT32Pointer_AllocateSize__SWIG_0(swigCPtr, this, baseType, multiplier, zero);
  }

  public void AllocateSize(String baseType, long multiplier) throws java.io.IOException {
    t32apiJNI.cT32Pointer_AllocateSize__SWIG_1(swigCPtr, this, baseType, multiplier);
  }

  public void Free() throws java.io.IOException {
    t32apiJNI.cT32Pointer_Free(swigCPtr, this);
  }

  public String GetPntrStr() {
    return t32apiJNI.cT32Pointer_GetPntrStr(swigCPtr, this);
  }

  public String GetCastedPntrStr() {
    return t32apiJNI.cT32Pointer_GetCastedPntrStr(swigCPtr, this);
  }

}
