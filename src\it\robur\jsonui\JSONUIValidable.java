package it.robur.jsonui;

import org.json.JSONException;
import org.json.JSONObject;

import it.robur.ui.FrameworkStringValidable;
import it.robur.ui.StringValidator;

public class JSONUIValidable implements FrameworkStringValidable {

    protected JSONObject state = new JSONObject();
    protected StringValidator validator;

    public JSONUIValidable(String id) throws JSONException {
        state.put("id", id);
        state.put("value", "");
    }

    @Override
    public FrameworkStringValidable setValidator(StringValidator validator) {
        this.validator = validator;

        return this;
    }

    @Override
    public String getValidatedValue() {
        return validator.getValidatedValue();
    }

    @Override
    public String getErrorString() {
        return validator.getErrorString();
    }

    @Override
    public void getAndValidate() {
        try {
            validator.validate(state.getString("value"));
        } catch (JSONException excp) {
            validator.validate(null);
        }
    }

    @Override
    public void setFirstToBeInError() {
    }

    public JSONObject toJSON() {
        return this.state;
    }

    public void fromJSON(JSONObject state) {
        this.state = state;
    }
}
