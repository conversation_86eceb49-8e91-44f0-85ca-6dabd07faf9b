/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 3.0.12
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package it.robur.test.physicalDCC.t32api;

public final class etParamAck {
  public final static etParamAck paNone = new etParamAck("paNone");
  public final static etParamAck paStartSetOk = new etParamAck("paStartSetOk");
  public final static etParamAck paEndSetOk = new etParamAck("paEndSetOk");
  public final static etParamAck paAbortOk = new etParamAck("paAbortOk");
  public final static etParamAck paLocked = new etParamAck("paLocked");
  public final static etParamAck paMemoryFault = new etParamAck("paMemoryFault");
  public final static etParamAck paTimeoutExceeded = new etParamAck("paTimeoutExceeded");

  public final int swigValue() {
    return swigValue;
  }

  public String toString() {
    return swigName;
  }

  public static etParamAck swigToEnum(int swigValue) {
    if (swigValue < swigValues.length && swigValue >= 0 && swigValues[swigValue].swigValue == swigValue)
      return swigValues[swigValue];
    for (int i = 0; i < swigValues.length; i++)
      if (swigValues[i].swigValue == swigValue)
        return swigValues[i];
    throw new IllegalArgumentException("No enum " + etParamAck.class + " with value " + swigValue);
  }

  private etParamAck(String swigName) {
    this.swigName = swigName;
    this.swigValue = swigNext++;
  }

  private etParamAck(String swigName, int swigValue) {
    this.swigName = swigName;
    this.swigValue = swigValue;
    swigNext = swigValue+1;
  }

  private etParamAck(String swigName, etParamAck swigEnum) {
    this.swigName = swigName;
    this.swigValue = swigEnum.swigValue;
    swigNext = this.swigValue+1;
  }

  private static etParamAck[] swigValues = { paNone, paStartSetOk, paEndSetOk, paAbortOk, paLocked, paMemoryFault, paTimeoutExceeded };
  private static int swigNext = 0;
  private final int swigValue;
  private final String swigName;
}

