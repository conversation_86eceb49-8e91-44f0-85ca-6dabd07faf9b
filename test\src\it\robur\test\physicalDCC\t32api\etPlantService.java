/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 3.0.12
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package it.robur.test.physicalDCC.t32api;

public final class etPlantService {
  public final static etPlantService psPrimary = new etPlantService("psPrimary");
  public final static etPlantService psSecondaryIncludent = new etPlantService("psSecondaryIncludent");
  public final static etPlantService psSecondarySeparable = new etPlantService("psSecondarySeparable");

  public final int swigValue() {
    return swigValue;
  }

  public String toString() {
    return swigName;
  }

  public static etPlantService swigToEnum(int swigValue) {
    if (swigValue < swigValues.length && swigValue >= 0 && swigValues[swigValue].swigValue == swigValue)
      return swigValues[swigValue];
    for (int i = 0; i < swigValues.length; i++)
      if (swigValues[i].swigValue == swigValue)
        return swigValues[i];
    throw new IllegalArgumentException("No enum " + etPlantService.class + " with value " + swigValue);
  }

  private etPlantService(String swigName) {
    this.swigName = swigName;
    this.swigValue = swigNext++;
  }

  private etPlantService(String swigName, int swigValue) {
    this.swigName = swigName;
    this.swigValue = swigValue;
    swigNext = swigValue+1;
  }

  private etPlantService(String swigName, etPlantService swigEnum) {
    this.swigName = swigName;
    this.swigValue = swigEnum.swigValue;
    swigNext = this.swigValue+1;
  }

  private static etPlantService[] swigValues = { psPrimary, psSecondaryIncludent, psSecondarySeparable };
  private static int swigNext = 0;
  private final int swigValue;
  private final String swigName;
}

