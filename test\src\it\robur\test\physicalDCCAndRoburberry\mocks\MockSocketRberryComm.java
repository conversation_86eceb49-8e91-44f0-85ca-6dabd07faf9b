package it.robur.test.physicalDCCAndRoburberry.mocks;

import java.io.IOException;

import org.json.JSONObject;

import it.robur.rberry.SocketRberryComm;

public class MockSocketRberryComm extends SocketRberryComm 
{
	public MockSocketRberryComm(String ip) 
	{
		super(ip);
	}
	
	public void waitForConnection() throws IOException
	{
    	while(true)
    	{
    		try 
    		{
				connect();
				break;
			}
			catch(IOException excp) 
    		{
				try
				{
					Thread.sleep(60000);
				}
				catch(InterruptedException ecxcp)
				{}
    		}
    	}
	}

	public JSONObject waitForIdentification() throws IOException
	{
		return waitForMessageByFieldExists("identification");
	}
	
	public JSONObject waitForResponse() throws IOException
	{
		return waitForMessageByFieldContent("action", "response");
	}
	
	public JSONObject waitForMessageByFieldExists(String field) throws IOException
	{
		return waitForMessage(field, null, 10);
	}
	
	public JSONObject waitForMessageByFieldContent(String field, String content) throws IOException
	{
		return waitForMessage(field, content, 10);
	}
	
	public JSONObject waitForMessage(String field, String content, int maxMessages) throws IOException
	{
		int nMessages = 0;
		JSONObject response = null;
    	while( true )
    	{
    		nMessages++;
    		if(nMessages > maxMessages)
    			throw new IOException("waitForResponse: max messages exceeed");
    		
    		response = receiveJSONFile();
    		
    		String str = response.toString();;
    		if( response.has("action") )
    			str = "action : " + response.get("action") + " - " + str;
    		if( str.length() > 50)
    			str = str.substring(0, 50);
    		System.out.println(str);

    		
    		if( field != null )
    			if( ! response.has(field) )
    				continue;
    		
    		if( content != null )
    			if( ! response.get(field).equals(content) )
    				continue;

    		break;
    	}
    	
    	return response;
	}
	
	public JSONObject waitForMessage() throws IOException
	{
    	JSONObject response = null;
    	while( response == null) 
    		response = receiveJSONFile();
    	
    	return response;
	}


}
