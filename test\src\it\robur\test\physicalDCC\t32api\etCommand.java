/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 3.0.12
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package it.robur.test.physicalDCC.t32api;

public final class etCommand {
  public final static etCommand pres_cntrl = new etCommand("pres_cntrl");
  public final static etCommand ownership_1 = new etCommand("ownership_1");
  public final static etCommand ownership_2 = new etCommand("ownership_2");
  public final static etCommand coord_cntrl = new etCommand("coord_cntrl");
  public final static etCommand time_cntrl = new etCommand("time_cntrl");
  public final static etCommand pres_board_chil = new etCommand("pres_board_chil");
  public final static etCommand module_status = new etCommand("module_status");
  public final static etCommand module_analog = new etCommand("module_analog");
  public final static etCommand module_dig = new etCommand("module_dig");
  public final static etCommand request = new etCommand("request");
  public final static etCommand bus_test_cr = new etCommand("bus_test_cr");
  public final static etCommand module_err_conf = new etCommand("module_err_conf");
  public final static etCommand module_err_heat_1 = new etCommand("module_err_heat_1");
  public final static etCommand module_err_heat_2 = new etCommand("module_err_heat_2");
  public final static etCommand module_err_chil_1 = new etCommand("module_err_chil_1");
  public final static etCommand module_err_chil_2 = new etCommand("module_err_chil_2");
  public final static etCommand module_err_chil_3 = new etCommand("module_err_chil_3");
  public final static etCommand module_param = new etCommand("module_param");
  public final static etCommand module_param_set = new etCommand("module_param_set");
  public final static etCommand module_param_ack = new etCommand("module_param_ack");
  public final static etCommand download_start = new etCommand("download_start");
  public final static etCommand download = new etCommand("download");
  public final static etCommand upload_start = new etCommand("upload_start");
  public final static etCommand upload = new etCommand("upload");
  public final static etCommand hop_request = new etCommand("hop_request");
  public final static etCommand master_request = new etCommand("master_request");
  public final static etCommand cr_request = new etCommand("cr_request");
  public final static etCommand module_log = new etCommand("module_log");
  public final static etCommand module_resignbox_ack = new etCommand("module_resignbox_ack");
  public final static etCommand bus_test_board = new etCommand("bus_test_board");
  public final static etCommand pres_board_heat = new etCommand("pres_board_heat");
  public final static etCommand module_info = new etCommand("module_info");
  public final static etCommand module_err_heat_3 = new etCommand("module_err_heat_3");
  public final static etCommand func_test_board = new etCommand("func_test_board");
  public final static etCommand func_test_cr = new etCommand("func_test_cr");
  public final static etCommand pres_io_mod_0 = new etCommand("pres_io_mod_0");
  public final static etCommand pres_io_mod_1 = new etCommand("pres_io_mod_1");
  public final static etCommand io_mod_status = new etCommand("io_mod_status");
  public final static etCommand req_to_cr_heat_group = new etCommand("req_to_cr_heat_group");
  public final static etCommand req_to_cr_chil_group = new etCommand("req_to_cr_chil_group");
  public final static etCommand bus_test_io_mod = new etCommand("bus_test_io_mod");
  public final static etCommand module_status_2 = new etCommand("module_status_2");
  public final static etCommand pres_cr_group = new etCommand("pres_cr_group", 64);
  public final static etCommand max_commands = new etCommand("max_commands");

  public final int swigValue() {
    return swigValue;
  }

  public String toString() {
    return swigName;
  }

  public static etCommand swigToEnum(int swigValue) {
    if (swigValue < swigValues.length && swigValue >= 0 && swigValues[swigValue].swigValue == swigValue)
      return swigValues[swigValue];
    for (int i = 0; i < swigValues.length; i++)
      if (swigValues[i].swigValue == swigValue)
        return swigValues[i];
    throw new IllegalArgumentException("No enum " + etCommand.class + " with value " + swigValue);
  }

  private etCommand(String swigName) {
    this.swigName = swigName;
    this.swigValue = swigNext++;
  }

  private etCommand(String swigName, int swigValue) {
    this.swigName = swigName;
    this.swigValue = swigValue;
    swigNext = swigValue+1;
  }

  private etCommand(String swigName, etCommand swigEnum) {
    this.swigName = swigName;
    this.swigValue = swigEnum.swigValue;
    swigNext = this.swigValue+1;
  }

  private static etCommand[] swigValues = { pres_cntrl, ownership_1, ownership_2, coord_cntrl, time_cntrl, pres_board_chil, module_status, module_analog, module_dig, request, bus_test_cr, module_err_conf, module_err_heat_1, module_err_heat_2, module_err_chil_1, module_err_chil_2, module_err_chil_3, module_param, module_param_set, module_param_ack, download_start, download, upload_start, upload, hop_request, master_request, cr_request, module_log, module_resignbox_ack, bus_test_board, pres_board_heat, module_info, module_err_heat_3, func_test_board, func_test_cr, pres_io_mod_0, pres_io_mod_1, io_mod_status, req_to_cr_heat_group, req_to_cr_chil_group, bus_test_io_mod, module_status_2, pres_cr_group, max_commands };
  private static int swigNext = 0;
  private final int swigValue;
  private final String swigName;
}

