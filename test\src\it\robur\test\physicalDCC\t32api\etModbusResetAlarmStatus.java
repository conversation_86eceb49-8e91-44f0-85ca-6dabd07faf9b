/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 3.0.12
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package it.robur.test.physicalDCC.t32api;

public final class etModbusResetAlarmStatus {
  public final static etModbusResetAlarmStatus mbStatusCommandInProgress = new etModbusResetAlarmStatus("mbStatusCommandInProgress", 0);
  public final static etModbusResetAlarmStatus mbStatusCommandImpossible = new etModbusResetAlarmStatus("mbStatusCommandImpossible", 1);
  public final static etModbusResetAlarmStatus mbStatusCommandSucceeded = new etModbusResetAlarmStatus("mbStatusCommandSucceeded", 2);
  public final static etModbusResetAlarmStatus mbStatusCommandFailed = new etModbusResetAlarmStatus("mbStatusCommandFailed", 3);
  public final static etModbusResetAlarmStatus mbStatusCommandRefused = new etModbusResetAlarmStatus("mbStatusCommandRefused", 4);
  public final static etModbusResetAlarmStatus mbStatusCommandUseless = new etModbusResetAlarmStatus("mbStatusCommandUseless", 5);

  public final int swigValue() {
    return swigValue;
  }

  public String toString() {
    return swigName;
  }

  public static etModbusResetAlarmStatus swigToEnum(int swigValue) {
    if (swigValue < swigValues.length && swigValue >= 0 && swigValues[swigValue].swigValue == swigValue)
      return swigValues[swigValue];
    for (int i = 0; i < swigValues.length; i++)
      if (swigValues[i].swigValue == swigValue)
        return swigValues[i];
    throw new IllegalArgumentException("No enum " + etModbusResetAlarmStatus.class + " with value " + swigValue);
  }

  private etModbusResetAlarmStatus(String swigName) {
    this.swigName = swigName;
    this.swigValue = swigNext++;
  }

  private etModbusResetAlarmStatus(String swigName, int swigValue) {
    this.swigName = swigName;
    this.swigValue = swigValue;
    swigNext = swigValue+1;
  }

  private etModbusResetAlarmStatus(String swigName, etModbusResetAlarmStatus swigEnum) {
    this.swigName = swigName;
    this.swigValue = swigEnum.swigValue;
    swigNext = this.swigValue+1;
  }

  private static etModbusResetAlarmStatus[] swigValues = { mbStatusCommandInProgress, mbStatusCommandImpossible, mbStatusCommandSucceeded, mbStatusCommandFailed, mbStatusCommandRefused, mbStatusCommandUseless };
  private static int swigNext = 0;
  private final int swigValue;
  private final String swigName;
}

