Create a simple test page

!|script|Page Builder                                |
|line   |${SUT_PATH}                                 |
|line   |!-!|-!fitnesse.testutil.${FIXTURE_NAME}!-|-!|
|line   |!-|blah|-!                                  |
|page   |!-TestPage-!                                |

Now run the test page.

|Response Requester.               |
|uri                        |valid?|
|!-TestPage?responder=test-!|true  |

|Response Examiner.|
|contents?         |
|                  |

Check the status message.

|Response Examiner.                                                                            |
|type    |pattern                                                                     |matches?|
|contents|<a href=\\"\?executionLog\\" class=\\"${EXPECTED_STYLE}\\">Execution Log</a>|true    |
