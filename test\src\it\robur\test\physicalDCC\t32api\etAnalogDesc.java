/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 3.0.12
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package it.robur.test.physicalDCC.t32api;

public final class etAnalogDesc {
  public final static etAnalogDesc adOutTemp = new etAnalogDesc("adOutTemp");
  public final static etAnalogDesc adInTemp = new etAnalogDesc("adInTemp");
  public final static etAnalogDesc adRequestedPower = new etAnalogDesc("adRequestedPower");
  public final static etAnalogDesc adRectTemp = new etAnalogDesc("adRectTemp");
  public final static etAnalogDesc adCntTemp = new etAnalogDesc("adCntTemp");
  public final static etAnalogDesc adAux1Temp = new etAnalogDesc("adAux1Temp");
  public final static etAnalogDesc adAux2Temp = new etAnalogDesc("adAux2Temp");
  public final static etAnalogDesc adVoltage = new etAnalogDesc("adVoltage");
  public final static etAnalogDesc adPumpRPM = new etAnalogDesc("adPumpRPM");
  public final static etAnalogDesc adFanValue = new etAnalogDesc("adFanValue");
  public final static etAnalogDesc adAbsTemp = new etAnalogDesc("adAbsTemp");
  public final static etAnalogDesc adEvapTemp = new etAnalogDesc("adEvapTemp");
  public final static etAnalogDesc adPrimaryOutTemp = new etAnalogDesc("adPrimaryOutTemp");
  public final static etAnalogDesc adBlowerSpeed = new etAnalogDesc("adBlowerSpeed");
  public final static etAnalogDesc adWaterFlow = new etAnalogDesc("adWaterFlow");
  public final static etAnalogDesc adModulatingCircVoltage = new etAnalogDesc("adModulatingCircVoltage");
  public final static etAnalogDesc adAnalogInVoltage = new etAnalogDesc("adAnalogInVoltage");
  public final static etAnalogDesc adAmbTemp = new etAnalogDesc("adAmbTemp");
  public final static etAnalogDesc adMixTemp = new etAnalogDesc("adMixTemp");
  public final static etAnalogDesc adFlueTemp = new etAnalogDesc("adFlueTemp");
  public final static etAnalogDesc adCondensateVoltage = new etAnalogDesc("adCondensateVoltage");
  public final static etAnalogDesc adGenFinsTemp = new etAnalogDesc("adGenFinsTemp");
  public final static etAnalogDesc adIonizationCurrent = new etAnalogDesc("adIonizationCurrent");
  public final static etAnalogDesc adExtVoltage1 = new etAnalogDesc("adExtVoltage1");
  public final static etAnalogDesc adExtVoltage2 = new etAnalogDesc("adExtVoltage2");
  public final static etAnalogDesc adOptTemp2 = new etAnalogDesc("adOptTemp2");
  public final static etAnalogDesc adExtSetpoint = new etAnalogDesc("adExtSetpoint");
  public final static etAnalogDesc adCurrentSetpoint = new etAnalogDesc("adCurrentSetpoint");
  public final static etAnalogDesc adOutTemp_OtherMod = new etAnalogDesc("adOutTemp_OtherMod");
  public final static etAnalogDesc adInTemp_OtherMod = new etAnalogDesc("adInTemp_OtherMod");
  public final static etAnalogDesc adGUE = new etAnalogDesc("adGUE");
  public final static etAnalogDesc adGasValveTemp = new etAnalogDesc("adGasValveTemp");
  public final static etAnalogDesc adMaxAnalogs = new etAnalogDesc("adMaxAnalogs");
  public final static etAnalogDesc adNone = new etAnalogDesc("adNone", adMaxAnalogs);

  public final int swigValue() {
    return swigValue;
  }

  public String toString() {
    return swigName;
  }

  public static etAnalogDesc swigToEnum(int swigValue) {
    if (swigValue < swigValues.length && swigValue >= 0 && swigValues[swigValue].swigValue == swigValue)
      return swigValues[swigValue];
    for (int i = 0; i < swigValues.length; i++)
      if (swigValues[i].swigValue == swigValue)
        return swigValues[i];
    throw new IllegalArgumentException("No enum " + etAnalogDesc.class + " with value " + swigValue);
  }

  private etAnalogDesc(String swigName) {
    this.swigName = swigName;
    this.swigValue = swigNext++;
  }

  private etAnalogDesc(String swigName, int swigValue) {
    this.swigName = swigName;
    this.swigValue = swigValue;
    swigNext = swigValue+1;
  }

  private etAnalogDesc(String swigName, etAnalogDesc swigEnum) {
    this.swigName = swigName;
    this.swigValue = swigEnum.swigValue;
    swigNext = this.swigValue+1;
  }

  private static etAnalogDesc[] swigValues = { adOutTemp, adInTemp, adRequestedPower, adRectTemp, adCntTemp, adAux1Temp, adAux2Temp, adVoltage, adPumpRPM, adFanValue, adAbsTemp, adEvapTemp, adPrimaryOutTemp, adBlowerSpeed, adWaterFlow, adModulatingCircVoltage, adAnalogInVoltage, adAmbTemp, adMixTemp, adFlueTemp, adCondensateVoltage, adGenFinsTemp, adIonizationCurrent, adExtVoltage1, adExtVoltage2, adOptTemp2, adExtSetpoint, adCurrentSetpoint, adOutTemp_OtherMod, adInTemp_OtherMod, adGUE, adGasValveTemp, adMaxAnalogs, adNone };
  private static int swigNext = 0;
  private final int swigValue;
  private final String swigName;
}

