/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 3.0.12
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package it.robur.test.physicalDCC.t32api;

public final class etModbusSCIIntState {
  public final static etModbusSCIIntState mbsInitialState = new etModbusSCIIntState("mbsInitialState");
  public final static etModbusSCIIntState mbsIdle = new etModbusSCIIntState("mbsIdle");
  public final static etModbusSCIIntState mbsNotValid = new etModbusSCIIntState("mbsNotValid");

  public final int swigValue() {
    return swigValue;
  }

  public String toString() {
    return swigName;
  }

  public static etModbusSCIIntState swigToEnum(int swigValue) {
    if (swigValue < swigValues.length && swigValue >= 0 && swigValues[swigValue].swigValue == swigValue)
      return swigValues[swigValue];
    for (int i = 0; i < swigValues.length; i++)
      if (swigValues[i].swigValue == swigValue)
        return swigValues[i];
    throw new IllegalArgumentException("No enum " + etModbusSCIIntState.class + " with value " + swigValue);
  }

  private etModbusSCIIntState(String swigName) {
    this.swigName = swigName;
    this.swigValue = swigNext++;
  }

  private etModbusSCIIntState(String swigName, int swigValue) {
    this.swigName = swigName;
    this.swigValue = swigValue;
    swigNext = swigValue+1;
  }

  private etModbusSCIIntState(String swigName, etModbusSCIIntState swigEnum) {
    this.swigName = swigName;
    this.swigValue = swigEnum.swigValue;
    swigNext = this.swigValue+1;
  }

  private static etModbusSCIIntState[] swigValues = { mbsInitialState, mbsIdle, mbsNotValid };
  private static int swigNext = 0;
  private final int swigValue;
  private final String swigName;
}

