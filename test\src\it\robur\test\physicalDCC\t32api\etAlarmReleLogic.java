/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 3.0.12
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package it.robur.test.physicalDCC.t32api;

public final class etAlarmReleLogic {
  public final static etAlarmReleLogic arDisabled = new etAlarmReleLogic("arDisabled");
  public final static etAlarmReleLogic arWaterTempAlarm = new etAlarmReleLogic("arWaterTempAlarm");
  public final static etAlarmReleLogic arGenericAlarm = new etAlarmReleLogic("arGenericAlarm");

  public final int swigValue() {
    return swigValue;
  }

  public String toString() {
    return swigName;
  }

  public static etAlarmReleLogic swigToEnum(int swigValue) {
    if (swigValue < swigValues.length && swigValue >= 0 && swigValues[swigValue].swigValue == swigValue)
      return swigValues[swigValue];
    for (int i = 0; i < swigValues.length; i++)
      if (swigValues[i].swigValue == swigValue)
        return swigValues[i];
    throw new IllegalArgumentException("No enum " + etAlarmReleLogic.class + " with value " + swigValue);
  }

  private etAlarmReleLogic(String swigName) {
    this.swigName = swigName;
    this.swigValue = swigNext++;
  }

  private etAlarmReleLogic(String swigName, int swigValue) {
    this.swigName = swigName;
    this.swigValue = swigValue;
    swigNext = swigValue+1;
  }

  private etAlarmReleLogic(String swigName, etAlarmReleLogic swigEnum) {
    this.swigName = swigName;
    this.swigValue = swigEnum.swigValue;
    swigNext = this.swigValue+1;
  }

  private static etAlarmReleLogic[] swigValues = { arDisabled, arWaterTempAlarm, arGenericAlarm };
  private static int swigNext = 0;
  private final int swigValue;
  private final String swigName;
}

