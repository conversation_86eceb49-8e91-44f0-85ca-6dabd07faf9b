Table cells contain strings.  Fixtures deal with objects.  In order to convert the strings into the objects FIT needs to know how to parse the strings.  One way we accomplish this is to allow the objects to have the following method:{{{Object parse(String s);}}}The following table shows the result of adding two vectors.  Each vector is represented by an ordered pair which represents it's X and Y dimensions.  The class CartesianVector is used to parse, display, and sum the vectors.

!|fitnesse.testutil.VectorSum|
|v1|v2|sum?|
|(0,0)|(0,1)|(0,1)|
|(0,1)|(0,1)|(0,2)|
|(1,1)|(1,1)|(2,2)|

It is not always possible to add a parse method on the Object returned by the fixture.

For Ex. if your fixture returns java.awt.Point class which does not have a {{{Object parse(String s);}}} method, this approach won't work.

Following approach can be used to delegate the parse method to a different class (Parse Delegate class). The parse delegate class has the {{{Object parse(String s);}}} method which returns the Object we are interested in.

The following table shows the result of adding two points.  Each point is represented by an ordered pair which represents it's X and Y dimensions.

!|fitnesse.testutil.ObjectTranslatePoint|
|p1|p2|sum?|
|(0,0)|(0,1)|(0,1)|
|(0,1)|(0,1)|(0,2)|
|(1,1)|(1,1)|(2,2)|

In the ObjectTranslatePoint fixture, we have a static block which registers the parse delegate object for a give Class type.

Ex:
{{{static
{
        TypeAdapter.registerParseDelegate(java.awt.Point.class, new ObjectDelegatePointParser());
}
}}}Please note that we are passing a Object of the Parse Delegate class. It is also possible to pass a class instead of the object. Only difference being the parse delegate class should have a {{{public static Object parse(String s);}}} method.

!|fitnesse.testutil.ClassTranslatePoint|
|p1|p2|sum?|
|(0,0)|(0,1)|(0,1)|
|(0,1)|(0,1)|(0,2)|
|(1,1)|(1,1)|(2,2)|

In the ClassTranslatePoint fixture, we have a static block which registers the parse delegate class for a give Class type.

Ex:
{{{static
{
        TypeAdapter.registerParseDelegate(java.awt.Point.class, ClassDelegatePointParser.class);
}
}}}