/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 3.0.12
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package it.robur.test.physicalDCC.t32api;

public final class etGroupType {
  public final static etGroupType gtOnlyPrimary = new etGroupType("gtOnlyPrimary");
  public final static etGroupType gtOnlySecondary = new etGroupType("gtOnlySecondary");
  public final static etGroupType gtBothContemporary = new etGroupType("gtBothContemporary");
  public final static etGroupType gtBothNotContemporary = new etGroupType("gtBothNotContemporary");
  public final static etGroupType gtUnset = new etGroupType("gtUnset");
  public final static etGroupType MAX_GROUP_TYPE = new etGroupType("MAX_GROUP_TYPE");

  public final int swigValue() {
    return swigValue;
  }

  public String toString() {
    return swigName;
  }

  public static etGroupType swigToEnum(int swigValue) {
    if (swigValue < swigValues.length && swigValue >= 0 && swigValues[swigValue].swigValue == swigValue)
      return swigValues[swigValue];
    for (int i = 0; i < swigValues.length; i++)
      if (swigValues[i].swigValue == swigValue)
        return swigValues[i];
    throw new IllegalArgumentException("No enum " + etGroupType.class + " with value " + swigValue);
  }

  private etGroupType(String swigName) {
    this.swigName = swigName;
    this.swigValue = swigNext++;
  }

  private etGroupType(String swigName, int swigValue) {
    this.swigName = swigName;
    this.swigValue = swigValue;
    swigNext = swigValue+1;
  }

  private etGroupType(String swigName, etGroupType swigEnum) {
    this.swigName = swigName;
    this.swigValue = swigEnum.swigValue;
    swigNext = this.swigValue+1;
  }

  private static etGroupType[] swigValues = { gtOnlyPrimary, gtOnlySecondary, gtBothContemporary, gtBothNotContemporary, gtUnset, MAX_GROUP_TYPE };
  private static int swigNext = 0;
  private final int swigValue;
  private final String swigName;
}

