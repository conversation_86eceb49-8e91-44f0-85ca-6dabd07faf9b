package it.robur.jstcertif;

import java.awt.EventQueue;
import java.io.File;
import java.io.IOException;

import javax.swing.JOptionPane;
import javax.swing.UIManager;
import javax.swing.UnsupportedLookAndFeelException;

import it.robur.flashers.GHP10SerialFlasher;
import it.robur.flashers.TerminalSerial;
import it.robur.interfaces.OpLong;
import it.robur.interfaces.OpStr;
import it.robur.interfaces.OpStrStr;
import it.robur.jserialtool.ApplicationPreference;
import it.robur.jserialtool.CCILogUIConsPC;
import it.robur.jserialtool.CommandLineOptions;
import it.robur.net.ConfServer;
import it.robur.jserialtool.DDCLogUIConsPC;
import it.robur.jserialtool.GEN10LogUIConsPC;
import it.robur.jserialtool.GHP10LogUIConsPC;
import it.robur.jserialtool.MainView;
import it.robur.jserialtool.Manifest;
import it.robur.jserialtool.MqttClientPCBuilder;
import it.robur.jserialtool.SerialPurejavacomm;
import it.robur.jserialtool.SettingsModel;
import it.robur.jserialtool.board.TargetBoards;
import it.robur.loggers.CCIJSONLogCons;
import it.robur.loggers.CCILogger;
import it.robur.loggers.DDCJSONLogCons;
import it.robur.loggers.DDCJSONReceive;
import it.robur.loggers.DDCLogger;
import it.robur.loggers.DumpJSONLogPublisher;
import it.robur.loggers.GEN10JSONLogCons;
import it.robur.loggers.GEN10Logger;
import it.robur.loggers.GHP10JSONLogCons;
import it.robur.loggers.GHP10JSONReceive;
import it.robur.loggers.GHP10Logger;
import it.robur.loggers.JSONPublisherMulti;
import it.robur.loggers.JSONPublisherResponseDecorator;
import it.robur.loggers.LogPoller;
import it.robur.loggers.LogPollerSleep;
import it.robur.loggers.LogPollerSync;
import it.robur.modbus.ModbusCCI;
import it.robur.modbus.ModbusDDC;
import it.robur.modbus.ModbusGEN10;
import it.robur.modbus.ModbusGHP10;
import it.robur.modbus.ModbusSerial;
import it.robur.net.MQTTClientLogPublisherReceiver;
import it.robur.util.DebugLog;



public class JSerialTool
{	
	
	//Manage the parsing of the command line option, if -nogui is NOT present start the graphical mode.
	public static void main(String[] args)
	{		
		CommandLineOptions clo = new CommandLineOptions();
		
		//parsing command line
	 	try {
			clo.performCommandLineParsing(args);
		} catch (org.apache.commons.cli.ParseException e) {	
				System.err.println("Parsing failed.  Reason: " + e.getMessage() );
			return;
		}	
		
		//options interrogations
		if(clo.hasVersion()) {
			System.out.println("jserialtool " + Manifest.versionName );
			return;
		}	
		if(clo.hasHelp()) {
			clo.printCommandLineHelp();
			return;
		}		
		if(clo.hasNoGUI()) {
			commandLineExecute(clo);
		}
		else {
			createAndShowGUI(clo);
		}

	}
	
	/*
	 * This GUI is based on the Conceptual Framework of Function, the layout is
	 * standard: toolbar, status bar, main area. The code is organized with the
	 * MVC pattern.
	 * 
	 * The Model:
	 * The three GHP10/DDC/CCI consumer are the models, because they generate the data to
	 * be displayed.
	 * The fourth models are the settings. 
	 * 
	 * The View:
	 * Is composed by the Main windows: MainView. Two additional modal dialog 
	 * SettingView and ModbusView. Two stand alone JPanel LogFileView and FirmwareFileView.
	 * 
	 * The Control:
	 * There is only one controller for the button of MainView and ModbusView. The update
	 * firmware feature of the FirmwareFileView is managed also.
	 * 
	 */
	private static void createAndShowGUI(final CommandLineOptions aCmdline)
	{
		
		try {
			// Set the local system Look&Feel
			UIManager.setLookAndFeel(UIManager.getSystemLookAndFeelClassName());
		} 
		catch (UnsupportedLookAndFeelException excp) {
			JOptionPane.showMessageDialog(null, excp, "Error", JOptionPane.ERROR_MESSAGE);
		}
		catch (ClassNotFoundException excp) {
			JOptionPane.showMessageDialog(null, excp, "Error", JOptionPane.ERROR_MESSAGE);
		}
		catch (InstantiationException excp) {
			JOptionPane.showMessageDialog(null, excp, "Error", JOptionPane.ERROR_MESSAGE);
		}
		catch (IllegalAccessException excp) {
			JOptionPane.showMessageDialog(null, excp, "Error", JOptionPane.ERROR_MESSAGE);
		}
		class Starter implements Runnable {
			public void run() {
				SettingsModel model = new SettingsModel(aCmdline);
				GHP10LogUIConsPC ghp10Consumer = new GHP10LogUIConsPC();
				DDCLogUIConsPC ddcConsumer = new DDCLogUIConsPC();
				CCILogUIConsPC cciConsumer = new CCILogUIConsPC();
				GEN10LogUIConsPC gen10Consumer = new GEN10LogUIConsPC();
				MainView win = new MainView(ghp10Consumer, ddcConsumer, cciConsumer, gen10Consumer, model);
				MainController controller = new MainController(win, ghp10Consumer, ddcConsumer, cciConsumer, gen10Consumer, model);
				
				win.setVisible(true);
				controller.forceAutoStart(model.isAutoStart());
			}
		}		
		Runnable task = new Starter();
		EventQueue.invokeLater(task);
	}

	private static void commandLineExecute(CommandLineOptions aCmdline)
	{
		ApplicationPreference pref = new ApplicationPreference();		
		
		//command line option or application preferences
		boolean flashRequest = false;
		String flashPath = null;
		String flashParamName = null;
		String flashProgramName = null;		
		String logPath = null;
		String tempPath  = null;
		TargetBoards targetBoard;
		boolean isK18;
		boolean isDDC;
		boolean isCCI;
		boolean isNEXTG;
		String strPort;
		boolean portAvailable = false;
		int modbusAddress;		
		int pollingPeriod;		
		String savingTime;	
		String language;
		boolean cloudIsSelected = false;	
		boolean confserverIsSelected = false;
		boolean massMemoryFaultTolerance = false;
		boolean  testModeIsSelected = false;	
		boolean  allQuantities = false;
		boolean  downloadMode = false;
		//object needed for polling
		ModbusGHP10 mbGhp10  = null;
		ModbusDDC mbDDC  = null;
		ModbusCCI mbCCI  = null;
		ModbusGEN10 mbGEN10  = null;
		LogPollerSync poller = null;
		
		boolean verbose = false;
		if (aCmdline.hasVerbose())
			verbose = true;		
		final boolean finalVerbose = verbose;
		
		/*
		 * This inner class manage the shutdown for the command line polling. The instance of this class must be installed 
		 * thought Runtime.getRuntime().addShutdownHook(Thread name). In this way when system signal: SIGHUP (only for unix), 
		 * SIGINT (^C) and SIGTERM([x]) are generated for this process, the following thread is executed:
		 * 
		 * This thread say at the poller to stop, wait the end of the main thread an than halt the JVM.
		 * 
		 */
		class ShutDown extends Thread {
			LogPollerSync poller;
			Thread mainThread;			
			
			/*
			 * aPoller: the poller to stop
			 * aThread: the thread that start the poller
			 * 
			 */
			ShutDown(Thread aThread, LogPollerSync aPoller) {
				poller = aPoller;
				mainThread = aThread;
			}
			
			public void run() {
				
				//stop the poller
				new Thread(new Runnable() 
				{    		
		            public void run() 
		            {
		        		poller.setExit();
		            }
				}).start();
				
				if(finalVerbose)
					System.out.println("... shutdown, wait some second ...");
		        
		        /* 
		         * now wait the for the main thread to stop, because if this thread ends, the JVM halt
		         * independently that the other thread are terminated or not.
		         */		        
		        try {
					mainThread.join();
				} catch (InterruptedException e) {
					e.printStackTrace();
				}

		    }	
		}
		
		// flashing option management
		if(aCmdline.hasValidFlashpath()) {
			flashPath = aCmdline.getFlashpath();
			flashParamName = aCmdline.getFWParameterFile(); 
			flashProgramName = aCmdline.getFWProgramFile();
			// ensure files exists
			if((new File(flashPath).isDirectory()) )
			{
				if(!(new File(flashPath+ File.separator + flashParamName).isFile()))
					flashParamName = null;
					
				if(!(new File(flashPath+ File.separator + flashProgramName).isFile()))
					flashProgramName = null;
					
				if((flashParamName != null) || (flashProgramName != null))
					flashRequest = true;
				else
					if(finalVerbose)
						System.out.println("Nothing to flash.");
			}
			else
			{
				if(finalVerbose)
					System.out.println("Nothing to flash.");
			}
		}
		
		//set the paths
		if(aCmdline.hasValidLogpath()) {
			logPath = aCmdline.getLogpath() + File.separator + "Log";
			tempPath  = logPath + File.separator + ".." + File.separator + ".itroburtemp";
			if(finalVerbose) {
				System.out.println("Logpath: " + logPath);
				System.out.println("tempPath: " + tempPath);
			}
			// Ensure output DIRS exist
			new File(logPath).mkdirs();
			new File(tempPath).mkdirs();			
		} else {
			logPath = "Log"; 
			tempPath  = logPath + File.separator + ".." + File.separator + ".itroburtemp";
			if(finalVerbose) {
				System.out.println("Logpath: " + logPath);
				System.out.println("tempPath: " + tempPath);
			}
			// Ensure output DIRS exist
			new File(logPath).mkdirs();
			new File(tempPath).mkdirs();			
		}
		
		//set the target board
		if(aCmdline.hasValidTargetBoard()) {
			targetBoard = aCmdline.getTargetBoard();
			if (targetBoard.equals(TargetBoards.K18)) {
				if(finalVerbose)
					System.out.println("K18 board selected");
				isK18 = true;
				isDDC = false;				
				isCCI = false;
				isNEXTG = false;
			} else if (targetBoard.equals(TargetBoards.DDC)) {
				if(finalVerbose)
					System.out.println("DDC board selected");
				isK18 = false;
				isDDC = true;
				isCCI = false;
				isNEXTG = false;
			} else if (targetBoard.equals(TargetBoards.CCI)) {
				if(finalVerbose)
					System.out.println("CCI board selected");
				isK18 = false;
				isDDC = false;
				isCCI = true;
				isNEXTG = false;
			} else if (targetBoard.equals(TargetBoards.NEXTG)) {
				if(finalVerbose)
					System.out.println("NEXTG board selected");
				isK18 = false;
				isDDC = false;
				isCCI = false;
				isNEXTG = true;
			} else {
				System.err.println("Board not available: " + targetBoard);
				if(finalVerbose)
					System.out.println("Board not available: " + targetBoard);
				return;
			}
		} else {
			TargetBoards board = pref.getSavedTargetBoard();
			if (board.equals(TargetBoards.K18)) {
				if(finalVerbose)
					System.out.println("K18 board in use");
				isK18 = true;
				isDDC = false;				
				isCCI = false;
				isNEXTG = false;
			} else if (board.equals(TargetBoards.DDC)) {
				if(finalVerbose)
					System.out.println("DDC board in use");
				isK18 = false;
				isDDC = true;
				isCCI = false;
				isNEXTG = false;
			} else if (board.equals(TargetBoards.CCI)) {
				if(finalVerbose)
					System.out.println("CCI board in use");
				isK18 = false;
				isDDC = false;
				isCCI = true;
				isNEXTG = false;
			} else if (board.equals(TargetBoards.NEXTG)) {
				if(finalVerbose)
					System.out.println("NEXTG board in use");
				isK18 = false;
				isDDC = false;
				isCCI = false;
				isNEXTG = true;
			} else {
				System.err.println("Board not available");
				if(finalVerbose)
					System.out.println("Board not available");
				return;
			}
		}
		
		//set the serial port
		if(aCmdline.hasValidComPort()) {
			strPort = aCmdline.getComPort();	
			if(finalVerbose)
				System.out.println("Port selected: " + strPort);
		} else {
			strPort = pref.getSavedCOMPort();	
			if(finalVerbose)
				System.out.println("Port in use: " + strPort);	
		}
		
		//check if the serial port  is available
		String[] ports = SerialPurejavacomm.listPorts();
		for (int i = 0; i < ports.length; i++) {
			if (strPort.equals(ports[i])) {
				portAvailable = true;
				break;
			}
		}
		if(!portAvailable) {
			System.err.println("Port not available: " + strPort);
			if(finalVerbose)
				System.out.println("Port not available: " + strPort);
			
			System.out.println("Available ports: ");
			for (int i = 0; i < ports.length; i++)
				System.out.println(ports[i]);
			
			return;
		}
		
		//set the modbus address
		if(aCmdline.hasValidModbusAddress()) {
			modbusAddress = Integer.valueOf(aCmdline.getModbusAddress());	
			if(finalVerbose)
				System.out.println("Modbus selected: " + modbusAddress);
		} else {
			modbusAddress = pref.getSavedModbusAddress();	
			if(finalVerbose)
				System.out.println("Modbus in use: " + modbusAddress);	
		}
		
		//set the polling period
		if(aCmdline.hasValidPollingPeriod()) {
			pollingPeriod = Integer.valueOf(aCmdline.getPollingPeriod());	
			if(finalVerbose)
				System.out.println("Polling period selected: " + pollingPeriod);
		} else {
			pollingPeriod = pref.getSavedPollingPeriod();		
			if(finalVerbose)
				System.out.println("Polling period in use: " + pollingPeriod);	
		}	
		if( (pollingPeriod > 60) || (pollingPeriod < 10) ) {
			System.err.println("Polling period out of margins: " + pollingPeriod);
			if(finalVerbose)
				System.out.println("Polling period out of margins: " + pollingPeriod);	
			return;
		}
			
		//set the saving time
		/*if(aCmdline.hasOption("saving") && (aCmdline.getOptionValue("saving") != null )) {
			savingTime = Integer.valueOf(aCmdline.getOptionValue("saving"));			
			System.out.println("Saving time selected: " + savingTime);
		} else {*/
			savingTime = pref.getSavedSavingTime();	
			if(finalVerbose)
				System.out.println("Saving time in use: " + savingTime);	
		//}
			
		//Set the language
		language = pref.getSavedLanguage();	
		if(finalVerbose)
			System.out.println("Language in use: " + language);
			
		//set the cloud
		if(aCmdline.hasCloud()) {
		 	cloudIsSelected = true;
		 	if(finalVerbose)
		 		System.out.println("Cloud selected");
		} else {
			if(pref.getSavedCloudSelection()) {
				cloudIsSelected = true;
				if(finalVerbose)
					System.out.println("Cloud in use");	
			}
		}
		
		//set the confserver
		if(aCmdline.hasConfServer()) {
		 	confserverIsSelected = true;
		 	if(finalVerbose)
		 		System.out.println("Confserver selected");
		} else {
			if(pref.getSavedConfserverSelection()) {
				confserverIsSelected = true;
				if(finalVerbose)
					System.out.println("Confserver in use");	
			}
		}
		
		//set the massmemory
		if(aCmdline.hasMassMemoryFaultTolerance()) {
		 	massMemoryFaultTolerance = true;
		 	if(finalVerbose)
		 		System.out.println("Mass Memory fault tolerance selected");
		} else {
			if(pref.getSavedMassMemoryFaultTolerance()) {
				massMemoryFaultTolerance = true;
				if(finalVerbose)
					System.out.println("Mass Memory fault tolerance in use");	
			}
		}
			
		//set the test Mode
		if(aCmdline.hasTestMode()) {
		 	testModeIsSelected = true;
		 	if(finalVerbose)
		 		System.out.println("Test mode selected");
		} else {
			if(pref.getSavedTestModeSelection()) {
				testModeIsSelected = true;		
				if(finalVerbose)
					System.out.println("Test Mode in use");	
			}
		}
		
		//set the quantities
		if(aCmdline.hasAllQuantities()) {
		 	allQuantities = true;
		 	if(finalVerbose)
		 		System.out.println("All quantities selected");
		} else {
			if(pref.getSavedAllQuantitiesSelection()) {
				allQuantities = true;
				if(finalVerbose)
					System.out.println("All quantities in use");	
			}
		}
		
		//check the download mode 
		if(aCmdline.hasDownload()) {
		 	downloadMode = true;
		 	if(finalVerbose)
		 		System.out.println("Download mode selected");
		}		

		ModbusSerial mbSerial = null;
		TerminalSerial flashSerial = null;
		DebugLog debugLog = null;
		ConfServer confServer = null; // TODO volatile etc. per confServer.error() 
		
		try
		{
			debugLog = new DebugLog(tempPath, "applog", true);
			
			// RS232/485
			SerialPurejavacomm serial = new SerialPurejavacomm(strPort);
			mbSerial    = serial;		
			flashSerial = serial;
			
			if (confserverIsSelected) {
				confServer = new ConfServer(tempPath);
				confServer.connect();
				confServer.start();
				
				if(finalVerbose)
					System.out.println("Confserver connected");
			}
			
			int retainDays;
			if(cloudIsSelected)
				retainDays = 30;
			else
				retainDays = 0;
			
			
			// USED FOR TESTING PURPOSE FOR FLASHING VIA USR WIFI
			/*
			// FIXED USR IP ADDRESS
			ModbusSerialOverTCP serial = new ModbusSerialOverTCP(InetAddress.getByName("***********"), new USRBridgePC(debugLog));	
			mbSerial = serial;
			flashSerial = serial;
			*/
			
			/*
			// AUTO DISCOVERY USR
			serial = new ModbusSerialOverTCP(new USRBridgePC(debugLog));
			flashSerial = new ModbusSerialOverTCP(new USRBridgePC(debugLog));
			*/
					
			if (isDDC)
			{
				if (!downloadMode) // selected polling
				{
					// DDC Logger
					DDCJSONLogCons DDCJSONConsumer = null;
					try
					{	
						OpStrStr notifyCannotWriteXMLLog = null;
						
						JSONPublisherMulti.Builder jsonSamplePublisherBuilder  = new JSONPublisherMulti.Builder();
						JSONPublisherMulti.Builder jsonCloudResponsePublisherBuilder = new JSONPublisherMulti.Builder();

						MQTTClientLogPublisherReceiver mqttPublisherReceiver = null;
						if( cloudIsSelected ) { 
							mqttPublisherReceiver = new MQTTClientLogPublisherReceiver(
											"DDC", 
											new MqttClientPCBuilder(tempPath), 
											confServer, 
											pollingPeriod * 2, 
											debugLog);
							
							jsonSamplePublisherBuilder.addPublisher(mqttPublisherReceiver.samplePublisher);
							jsonCloudResponsePublisherBuilder.addPublisher(mqttPublisherReceiver.responsePublisher);
						}

						if( testModeIsSelected ) {
							jsonSamplePublisherBuilder.addPublisher(new DumpJSONLogPublisher(tempPath));
							jsonCloudResponsePublisherBuilder.addPublisher(new DumpJSONLogPublisher(tempPath));
						}
						
						if( confserverIsSelected ) {
							jsonSamplePublisherBuilder.addPublisher(confServer);
							notifyCannotWriteXMLLog = confServer.notifyCannotWriteXMLLog;
						}
						
						JSONPublisherMulti jsonSamplePublishers = jsonSamplePublisherBuilder.buildIfNeeded();
						if( jsonSamplePublishers != null )
							DDCJSONConsumer = new DDCJSONLogCons( jsonSamplePublishers, !allQuantities, false, debugLog);
	
						
						poller = new LogPollerSleep(pollingPeriod, LogPoller.timeStringToDate(savingTime));
						poller.setDebugLog(debugLog);						
						
						DDCLogger logger = new DDCLogger();
						
						if(finalVerbose)
							System.out.println("Logging started ...");	
						
						//install the shutdown callback to stop the logger by ^C, SIGHUP, SIGINT, SIGTERM
						ShutDown shutdown = new ShutDown(Thread.currentThread(), poller);
						Runtime.getRuntime().addShutdownHook(shutdown);
						
						
						mbDDC = logger.initLog(mbSerial, modbusAddress, 
												language, 
												logPath, tempPath, null, DDCJSONConsumer, null, false, retainDays,  notifyCannotWriteXMLLog, 
												debugLog);
						
						JSONPublisherResponseDecorator jsonCloudResponsePublisher = JSONPublisherResponseDecorator.buildIfNeeded(
																							jsonCloudResponsePublisherBuilder.buildIfNeeded(),
																							false);
						if( mqttPublisherReceiver != null && jsonCloudResponsePublisher != null)
							mqttPublisherReceiver.setMessageReceiver(
												new DDCJSONReceive(
														mbDDC,
														jsonCloudResponsePublisher,
														new DDCJSONLogCons(jsonCloudResponsePublisher, !allQuantities, false, null)));
												
						if( confServer != null )
						{
							JSONPublisherResponseDecorator jsonConfServerResponsePublisher = new JSONPublisherResponseDecorator(
																									confServer,
																									true);
							confServer.startListeningOnAnotherThread(
													new DDCJSONReceive(
															mbDDC,
															jsonConfServerResponsePublisher,
															new DDCJSONLogCons(jsonConfServerResponsePublisher, !allQuantities, false, null)));
						}
						
						logger.log(poller);	
						
						if(finalVerbose)
							System.out.println("Logging ended.");
					}
					catch(Exception excp)
					{
						if(finalVerbose)
							System.out.println(excp);
						System.err.println(excp);
						
						if( confServer != null)	
							confServer.error(excp.getMessage(), excp.toString());
					}
					finally
					{
						if (DDCJSONConsumer != null)
							DDCJSONConsumer.close();
						if (confServer != null)
							confServer.disconnect();
					}
					
				}
				else
				{
					// DDC Download
					try
					{
						if(finalVerbose)
							System.out.println("Downloading started ...");
						new DDCLogger().logSingleSample(mbSerial, modbusAddress,
														language,
														logPath, tempPath, debugLog);	
						if(finalVerbose)
							System.out.println("Downloading ended.");
					}			
					catch(IOException excp)
					{
						if(finalVerbose)
							System.out.println(excp);
						System.err.println(excp);
					}
				}
			}
			else if(isK18)
			{
				if(flashRequest)
				{
					try
					{
						OpLong notifyFileSize = new OpLong()
						{
							@Override
							public void op(long value)
							{
								if(finalVerbose)
									System.out.println("File size: " + value);
							}
						};

						OpLong notifySentChunk = new OpLong()
						{
							@Override
							public void op(long value)
							{
								if(finalVerbose)
									System.out.print("\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\bSent: " + value);
							}
						};
						
						OpStr printVerbose = null;
						if (finalVerbose)
						{
							printVerbose = new OpStr() 
							{							
								@Override
								public void op(String value) 
								{
									System.out.println(value);								
								}
							};
						}
						
						if( (flashPath == null) || ((flashParamName == null) && (flashProgramName == null)) )
						{
							if(finalVerbose)
								System.out.println("Nothing to flash");

							debugLog.sprintln(GHP10SerialFlasher.DEBUG_TAG, "Nothing to flash");
							return;
						}
					
						if(finalVerbose)
							System.out.println("Start GHP10 flashing sequence");

						ModbusGHP10 ghp10 = new ModbusGHP10(mbSerial, 1);
						
						new GHP10SerialFlasher(notifyFileSize, notifySentChunk, printVerbose, debugLog)
								.startFlashing(ghp10, flashSerial, flashPath, flashParamName, flashProgramName);
						
						if(finalVerbose)
							System.out.println("\nEnd GHP10 flashing sequence");

					}
					catch(Exception excp)
					{
						if(finalVerbose)
							System.out.println(excp);
						System.err.println(excp);
					}
				}
				else
				{
					if (!downloadMode) // selected polling
					{
						// GHP10 Logger
						GHP10JSONLogCons GHP10JSONConsumer = null;
						try
						{						
							OpStrStr notifyCannotWriteXMLLog = null;
							
							JSONPublisherMulti.Builder jsonSamplePublisherBuilder  = new JSONPublisherMulti.Builder();
							JSONPublisherMulti.Builder jsonCloudResponsePublisherBuilder = new JSONPublisherMulti.Builder();

							MQTTClientLogPublisherReceiver mqttPublisherReceiver = null;
							if( cloudIsSelected ) {
								mqttPublisherReceiver = new MQTTClientLogPublisherReceiver(
												"K18", 
												new MqttClientPCBuilder(tempPath),
												confServer, 
												pollingPeriod * 2, 
												debugLog);
							
								jsonSamplePublisherBuilder.addPublisher(mqttPublisherReceiver.samplePublisher);
								jsonCloudResponsePublisherBuilder.addPublisher(mqttPublisherReceiver.responsePublisher);
							}
	
							if( testModeIsSelected ) {
								jsonSamplePublisherBuilder.addPublisher(new DumpJSONLogPublisher(tempPath));
								jsonCloudResponsePublisherBuilder.addPublisher(new DumpJSONLogPublisher(tempPath));
							}
							
							if( confserverIsSelected ) {
								jsonSamplePublisherBuilder.addPublisher(confServer);
								notifyCannotWriteXMLLog = confServer.notifyCannotWriteXMLLog;
							}
							
							JSONPublisherMulti jsonSamplePublishers = jsonSamplePublisherBuilder.buildIfNeeded();
							if( jsonSamplePublishers != null )
								GHP10JSONConsumer = new GHP10JSONLogCons(jsonSamplePublishers, false, debugLog);

							
							poller = new LogPollerSleep(pollingPeriod, LogPoller.timeStringToDate(savingTime));
							poller.setDebugLog(debugLog);
							
							GHP10Logger logger = new GHP10Logger();
							
							if(finalVerbose)
								System.out.println("Logging started ...");
							
							//install the shutdown callback to stop the logger by ^C, SIGHUP, SIGINT, SIGTERM
							ShutDown shutdown = new ShutDown(Thread.currentThread(), poller);
							Runtime.getRuntime().addShutdownHook(shutdown);
							

							mbGhp10 = logger.initLog(mbSerial, modbusAddress,
														language,
														logPath, tempPath, 
														null, GHP10JSONConsumer, null, retainDays, notifyCannotWriteXMLLog, 
														debugLog); // returns ghp10
							
							JSONPublisherResponseDecorator jsonCloudResponsePublisher = JSONPublisherResponseDecorator.buildIfNeeded(
																								jsonCloudResponsePublisherBuilder.buildIfNeeded(),
																								false);
							if( mqttPublisherReceiver != null && jsonCloudResponsePublisher != null)
								mqttPublisherReceiver.setMessageReceiver(
												new GHP10JSONReceive(
														mbGhp10,
														jsonCloudResponsePublisher,
														new GHP10JSONLogCons(jsonCloudResponsePublisher, false, null)));
							
							if( confServer != null )
							{
								JSONPublisherResponseDecorator jsonConfServerResponsePublisher = new JSONPublisherResponseDecorator(
																										confServer,
																										true);
								confServer.startListeningOnAnotherThread(
													new GHP10JSONReceive(
															mbGhp10,
															jsonConfServerResponsePublisher,
															new GHP10JSONLogCons(jsonConfServerResponsePublisher, false, null)));
							}
								
							logger.log(poller);	
							
							if(finalVerbose)
								System.out.println("Logging ended.");
							
							Runtime.getRuntime().halt(3);
						}
						catch(Exception excp)
						{
							if(finalVerbose)
								System.out.println(excp);
							System.err.println(excp);
							
							if( confServer != null)	
								confServer.error(excp.getMessage(), excp.toString());
						}
						finally
						{
							if (GHP10JSONConsumer != null)
								GHP10JSONConsumer.close();
							if (confServer != null)
								confServer.disconnect();
						}
					} else {
						// GHP10 Download
						try
						{
							if(finalVerbose)
								System.out.println("Downloading started ...");
							new GHP10Logger().logSingleSample(mbSerial, modbusAddress, 
																language,
																logPath, tempPath, debugLog);		
							if(finalVerbose)
								System.out.println("Downloading ended.");
						}
						catch(IOException excp)
						{
							if(finalVerbose)
								System.out.println(excp);
							System.err.println(excp);
						}
					}				
				}		
			}
			else if(isCCI)
			{
				if (!downloadMode) // selected polling
				{
					// CCI Logger
					CCIJSONLogCons CCIJSONConsumer = null;
					try
					{						
						OpStrStr notifyCannotWriteXMLLog = null;
						
						JSONPublisherMulti.Builder jsonSamplePublisherBuilder  = new JSONPublisherMulti.Builder();

						// TODO Cloud not available yet
//						if( cloudIsSelected ) 
//							jsonPublisherBuilder.addPublisher(
//									new MQTTClientLogPublisher(
//											"CCI", 
//											new MqttClientPCBuilder(tempPath), 
//											new ConnectedMulti( new CloudConnectedStatus(), confServer), 
//											pollingPeriod * 2, 
//											debugLog));

						if( testModeIsSelected )
							jsonSamplePublisherBuilder.addPublisher(new DumpJSONLogPublisher(tempPath));
						
						if( confserverIsSelected ) {
							jsonSamplePublisherBuilder.addPublisher(confServer);
							notifyCannotWriteXMLLog = confServer.notifyCannotWriteXMLLog;
						}
						
						JSONPublisherMulti jsonSamplePublishers = jsonSamplePublisherBuilder.buildIfNeeded();
						if( jsonSamplePublishers != null )
							CCIJSONConsumer = new CCIJSONLogCons( jsonSamplePublishers, !allQuantities, false, debugLog);
					
						
						poller = new LogPollerSleep(pollingPeriod, LogPoller.timeStringToDate(savingTime));
						poller.setDebugLog(debugLog);						
						
						CCILogger logger = new CCILogger();
						
						if(finalVerbose)
							System.out.println("Logging started ...");	
						
						//install the shutdown callback to stop the logger by ^C, SIGHUP, SIGINT, SIGTERM
						ShutDown shutdown = new ShutDown(Thread.currentThread(), poller);
						Runtime.getRuntime().addShutdownHook(shutdown); 
													

						mbCCI = logger.initLog(mbSerial, modbusAddress, 
													language, 
													logPath, tempPath, null, CCIJSONConsumer, null, retainDays,  notifyCannotWriteXMLLog, 
													debugLog);

						logger.log(poller);	
						
						if(finalVerbose)
							System.out.println("Logging ended.");
					}
					catch(Exception excp)
					{
						if(finalVerbose)
							System.out.println(excp);
						System.err.println(excp);
						
						if( confServer != null)	
							confServer.error(excp.getMessage(), excp.toString());
					}
					finally
					{
						if (CCIJSONConsumer != null)
							CCIJSONConsumer.close();
						if (confServer != null)
							confServer.disconnect();
					}
				}				
				else
				{
					// CCI Download
					try
					{
						if(finalVerbose)
							System.out.println("Downloading started ...");
						new CCILogger().logSingleSample(mbSerial, modbusAddress,
														language,
														logPath, tempPath, debugLog);	
						if(finalVerbose)
							System.out.println("Downloading ended.");
					}			
					catch(IOException excp)
					{
						if(finalVerbose)
							System.out.println(excp);
						System.err.println(excp);
					}
				}
			}
			else if(isNEXTG)
			{
				if (!downloadMode) // selected polling
				{
					// NEXTG Logger
					GEN10JSONLogCons GEN10JSONConsumer = null;
					try
					{						
						OpStrStr notifyCannotWriteXMLLog = null;
						
						JSONPublisherMulti.Builder jsonSamplePublisherBuilder  = new JSONPublisherMulti.Builder();

						// TODO Cloud not available yet
//						if( cloudIsSelected ) 
//							jsonPublisherBuilder.addPublisher(
//									new MQTTClientLogPublisher(
//											"NEXTG", 
//											new MqttClientPCBuilder(tempPath), 
//											new ConnectedMulti( new CloudConnectedStatus(), confServer), 
//											pollingPeriod * 2, 
//											debugLog));

						if( testModeIsSelected )
							jsonSamplePublisherBuilder.addPublisher(new DumpJSONLogPublisher(tempPath));
						
						if( confserverIsSelected ) {
							jsonSamplePublisherBuilder.addPublisher(confServer);
							notifyCannotWriteXMLLog = confServer.notifyCannotWriteXMLLog;
						}
						
						JSONPublisherMulti jsonSamplePublishers = jsonSamplePublisherBuilder.buildIfNeeded();
						if( jsonSamplePublishers != null )
							GEN10JSONConsumer = new GEN10JSONLogCons( jsonSamplePublishers, false, debugLog);
					
						
						poller = new LogPollerSleep(pollingPeriod, LogPoller.timeStringToDate(savingTime));
						poller.setDebugLog(debugLog);						
						
						GEN10Logger logger = new GEN10Logger();
						
						if(finalVerbose)
							System.out.println("Logging started ...");	
						
						//install the shutdown callback to stop the logger by ^C, SIGHUP, SIGINT, SIGTERM
						ShutDown shutdown = new ShutDown(Thread.currentThread(), poller);
						Runtime.getRuntime().addShutdownHook(shutdown); 
													

						mbGEN10 = logger.initLog(mbSerial, modbusAddress, 
													language, 
													logPath, tempPath, null, GEN10JSONConsumer, null, retainDays,  notifyCannotWriteXMLLog, 
													debugLog);

						logger.log(poller);	
						
						if(finalVerbose)
							System.out.println("Logging ended.");
					}
					catch(Exception excp)
					{
						if(finalVerbose)
							System.out.println(excp);
						System.err.println(excp);
						
						if( confServer != null)	
							confServer.error(excp.getMessage(), excp.toString());
					}
					finally
					{
						if (GEN10JSONConsumer != null)
							GEN10JSONConsumer.close();
						if (confServer != null)
							confServer.disconnect();
					}
				}				
				else
				{
					// NETXG Download
					try
					{
						if(finalVerbose)
							System.out.println("Downloading started ...");
						new GEN10Logger().logSingleSample(mbSerial, modbusAddress,
														language,
														logPath, tempPath, debugLog);	
						if(finalVerbose)
							System.out.println("Downloading ended.");
					}			
					catch(IOException excp)
					{
						if(finalVerbose)
							System.out.println(excp);
						System.err.println(excp);
					}
				}
			}

		}
		catch(Exception excp)
		{
			if(finalVerbose)
				System.out.println(excp);
			System.err.println(excp);
		}
		finally
		{
			if(debugLog != null)
				debugLog.close();
		}

	}		
	
}
