/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 3.0.12
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package it.robur.test.physicalDCC.t32api;

public final class etBusAccessInControl {
  public final static etBusAccessInControl baicNo_Access = new etBusAccessInControl("baicNo_Access");
  public final static etBusAccessInControl baicBMS_Access = new etBusAccessInControl("baicBMS_Access");
  public final static etBusAccessInControl baicRA_Access = new etBusAccessInControl("baicRA_Access");

  public final int swigValue() {
    return swigValue;
  }

  public String toString() {
    return swigName;
  }

  public static etBusAccessInControl swigToEnum(int swigValue) {
    if (swigValue < swigValues.length && swigValue >= 0 && swigValues[swigValue].swigValue == swigValue)
      return swigValues[swigValue];
    for (int i = 0; i < swigValues.length; i++)
      if (swigValues[i].swigValue == swigValue)
        return swigValues[i];
    throw new IllegalArgumentException("No enum " + etBusAccessInControl.class + " with value " + swigValue);
  }

  private etBusAccessInControl(String swigName) {
    this.swigName = swigName;
    this.swigValue = swigNext++;
  }

  private etBusAccessInControl(String swigName, int swigValue) {
    this.swigName = swigName;
    this.swigValue = swigValue;
    swigNext = swigValue+1;
  }

  private etBusAccessInControl(String swigName, etBusAccessInControl swigEnum) {
    this.swigName = swigName;
    this.swigValue = swigEnum.swigValue;
    swigNext = this.swigValue+1;
  }

  private static etBusAccessInControl[] swigValues = { baicNo_Access, baicBMS_Access, baicRA_Access };
  private static int swigNext = 0;
  private final int swigValue;
  private final String swigName;
}

