/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 3.0.12
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package it.robur.test.physicalDCC.t32api;

public class cDDCModbus {
  private transient long swigCPtr;
  protected transient boolean swigCMemOwn;

  protected cDDCModbus(long cPtr, boolean cMemoryOwn) {
    swigCMemOwn = cMemoryOwn;
    swigCPtr = cPtr;
  }

  protected static long getCPtr(cDDCModbus obj) {
    return (obj == null) ? 0 : obj.swigCPtr;
  }

  protected void finalize() {
    delete();
  }

  public synchronized void delete() {
    if (swigCPtr != 0) {
      if (swigCMemOwn) {
        swigCMemOwn = false;
        t32apiJNI.delete_cDDCModbus(swigCPtr);
      }
      swigCPtr = 0;
    }
  }

  public static int SetModbusCoil(int address, short value) {
    return t32apiJNI.cDDCModbus_SetModbusCoil(address, value);
  }

  public static int GetModbusCoil(int address, short[] outValue) {
    return t32apiJNI.cDDCModbus_GetModbusCoil(address, outValue);
  }

  public static int GetModbusDiscreteInput(int address, short[] outValue) {
    return t32apiJNI.cDDCModbus_GetModbusDiscreteInput(address, outValue);
  }

  public static int GetModbusInputRegister(int address, int[] outValue) {
    return t32apiJNI.cDDCModbus_GetModbusInputRegister(address, outValue);
  }

  public static int SetModbusHoldingRegister(int address, int value) {
    return t32apiJNI.cDDCModbus_SetModbusHoldingRegister(address, value);
  }

  public static int GetModbusHoldingRegister(int address, int[] outValue) {
    return t32apiJNI.cDDCModbus_GetModbusHoldingRegister(address, outValue);
  }

  public cDDCModbus() {
    this(t32apiJNI.new_cDDCModbus(), true);
  }

}
