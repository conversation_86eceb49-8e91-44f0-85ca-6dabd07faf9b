package it.robur.test.physicalSerialGHP10;

import static org.junit.Assert.assertTrue;

import java.io.File;
import java.io.FilenameFilter;
import java.io.IOException;
import java.util.Arrays;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;

import it.robur.jserialtool.SerialPurejavacomm;
import it.robur.loggers.GHP10JSONLogCons;
import it.robur.loggers.GHP10LogCopy;
import it.robur.loggers.GHP10Logger.SingleSampleLogProducer;
import it.robur.loggers.JSONUtil;
import it.robur.modbus.ModbusGHP10;
import it.robur.test.multiplatform.util.JSONTestUtil;
import it.robur.test.multiplatform.util.TestUtil;

public class GHP10CopyWholeLogTestWithModbus 
{

	// WAS: for DDC:
//	private static JSONDDC jDdc;
//	private static DDCJSONLogCons jConsumer;
//	private static SimpleJSONLogPublisher jPublisher;
//	
//	private static MockDDCMetricsXMLLogFile xmlMetrics;
//	private static MockDDCEventsXMLLogFile xmlEvents;
//	private static DDCXMLLogCons xConsumer;
	
	private static ModbusGHP10 mGhp10;
	private static SerialPurejavacomm serial;
	
//	private static boolean T32ApiLoaded = false; 
//	
//	private String metricsLogFilename;
//	private String eventLogFilename;
		
//	@AfterClass
//	public static void teardownTest() throws IOException
//	{		
//		if( T32ApiLoaded )
//			cT32Api.Exit();
//	}
	
	@Before
	public void setup() throws IOException
	{
		// Modbus
		mGhp10 = null;
				
//    	// JSON
//		jGhp10       = null;
//		jConsumer  = null;
//		jPublisher = null;
//		
//		// XML
//		xmlMetrics       = null;
//		xConsumer = null;
		
	}
	
	@After
	public void teardown() throws IOException
	{
		// Modbus
		if( mGhp10 != null)
		{
			mGhp10.close();			
			mGhp10 = null;
		}
		
	}

	
	public void setupModbus() throws IOException
	{
		serial = new SerialPurejavacomm(Settings.getPort());
		mGhp10 = new ModbusGHP10(serial, 1);		
		    	
	}
	
//	public void setupJSON() throws IOException
//	{
//		jGhp10 = new JSONGHP10();
//		jPublisher = new SimpleJSONLogPublisher();
//		jConsumer = new GHP10JSONLogCons( jPublisher, false, true, null);
//		
//	}
//	
//	public void setupXML() throws IOException
//	{
//    	xmlMetrics = new MockGHP10MetricsXMLLogFile();
//    	xmlEvents = new MockGHP10EventsXMLLogFile();
//		xConsumer = new GHP10XMLLogCons(xmlMetrics, xmlEvents);
//		    	
//	}

    
    @Test
    public void testCopyWholeSingleLogFromModbusToJSON() throws IOException
    {
    	setupModbus();
    	
        mGhp10.init();
    	
    	final JSONTestUtil.TestFilesJSONLogPublisher jPublisher = new JSONTestUtil.TestFilesJSONLogPublisher(TestUtil.tmpPath);
    	final GHP10JSONLogCons jConsumer = new GHP10JSONLogCons( jPublisher, false, null);
		
		mGhp10.getIdentificationInfo();
		
		jConsumer.setAndConnect(Integer.toString(mGhp10.getApplianceSerial()), mGhp10);
    	
    	GHP10LogCopy.copyWholeLog(mGhp10, new SingleSampleLogProducer(mGhp10), jConsumer);
    	
        mGhp10.close();
        mGhp10 = null;
    	    	
    	
    	String K18370960035Path = "K18_sn370960035_2021.06.01/";    	
    	String sample01Partial = (K18370960035Path + "K18_sn370960035_2021.06.01_sample01/K18_sn370960035_2021.06.01_").replace("/", File.separator);

    	assertTrue(
    			JSONTestUtil.nearlySameAs(
    					JSONTestUtil.loadJSON(sample01Partial + "identif"),
    					JSONUtil.loadJSON(TestUtil.tmpPath, "identif")));    	
    	assertTrue(
    			JSONTestUtil.nearlySameAs(
    					JSONTestUtil.loadJSON(sample01Partial + "sample"),
    					JSONUtil.loadJSON(TestUtil.tmpPath, "sample")));
    	assertTrue(
    			JSONTestUtil.nearlySameAs(
    					JSONTestUtil.loadJSON(sample01Partial + "split"),
    					JSONUtil.loadJSON(TestUtil.tmpPath, "split")));

    	// TODO: how?
//    	assertTrue(
//    			JSONTestUtil.nearlySameAs(
//    					JSONTestUtil.loadJSON(sample01Partial + "events"),
//    					JSONTestUtil.loadJSON(outPath, getLastJSON(outPath, "events"))));    	
	
    }
    
    static protected String getLastJSON(final String path, final String prefix)
    {
    	File dir = new File(path);
    	
    	FilenameFilter filter = new FilenameFilter() 
    	{        		
			public boolean accept(File dir, String name) 
			{    				
				String lowercaseName = name.toLowerCase();
				
				if ( lowercaseName.toLowerCase().startsWith(prefix)
						&& lowercaseName.toLowerCase().endsWith(".json") )				
					return true;
					
				return false;
			}
		};
		
		File[] files = dir.listFiles(filter);
		
		if (files == null || files.length == 0)
			return null;
		else
		{
			Arrays.sort(files);
			return files[0].getName().replace(".json", "");
		}
		
    }

    
}
