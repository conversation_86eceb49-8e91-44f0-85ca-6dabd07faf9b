/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 3.0.12
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package it.robur.test.physicalDCC.t32api;

public final class etDecisionError {
  public final static etDecisionError deOk = new etDecisionError("deOk");
  public final static etDecisionError deUnknown = new etDecisionError("deUnknown");
  public final static etDecisionError deBothRYRW = new etDecisionError("deBothRYRW");
  public final static etDecisionError deSepValveOffline = new etDecisionError("deSepValveOffline");
  public final static etDecisionError deSepValveTimeout = new etDecisionError("deSepValveTimeout");
  public final static etDecisionError deSepValveFeedbackError = new etDecisionError("deSepValveFeedbackError");
  public final static etDecisionError deCHValveOffline = new etDecisionError("deCHValveOffline");
  public final static etDecisionError deCHValveTimeout = new etDecisionError("deCHValveTimeout");
  public final static etDecisionError deCHValveFeedbackError = new etDecisionError("deCHValveFeedbackError");
  public final static etDecisionError deCHValveWrongOut = new etDecisionError("deCHValveWrongOut");
  public final static etDecisionError deIOModCircOffline = new etDecisionError("deIOModCircOffline");
  public final static etDecisionError deIOModProbeNotInstalled = new etDecisionError("deIOModProbeNotInstalled");
  public final static etDecisionError deSepIOModProbeNotInstalled = new etDecisionError("deSepIOModProbeNotInstalled");
  public final static etDecisionError deIOModCircNotInstalled = new etDecisionError("deIOModCircNotInstalled");
  public final static etDecisionError deSepIOModCircNotInstalled = new etDecisionError("deSepIOModCircNotInstalled");

  public final int swigValue() {
    return swigValue;
  }

  public String toString() {
    return swigName;
  }

  public static etDecisionError swigToEnum(int swigValue) {
    if (swigValue < swigValues.length && swigValue >= 0 && swigValues[swigValue].swigValue == swigValue)
      return swigValues[swigValue];
    for (int i = 0; i < swigValues.length; i++)
      if (swigValues[i].swigValue == swigValue)
        return swigValues[i];
    throw new IllegalArgumentException("No enum " + etDecisionError.class + " with value " + swigValue);
  }

  private etDecisionError(String swigName) {
    this.swigName = swigName;
    this.swigValue = swigNext++;
  }

  private etDecisionError(String swigName, int swigValue) {
    this.swigName = swigName;
    this.swigValue = swigValue;
    swigNext = swigValue+1;
  }

  private etDecisionError(String swigName, etDecisionError swigEnum) {
    this.swigName = swigName;
    this.swigValue = swigEnum.swigValue;
    swigNext = this.swigValue+1;
  }

  private static etDecisionError[] swigValues = { deOk, deUnknown, deBothRYRW, deSepValveOffline, deSepValveTimeout, deSepValveFeedbackError, deCHValveOffline, deCHValveTimeout, deCHValveFeedbackError, deCHValveWrongOut, deIOModCircOffline, deIOModProbeNotInstalled, deSepIOModProbeNotInstalled, deIOModCircNotInstalled, deSepIOModCircNotInstalled };
  private static int swigNext = 0;
  private final int swigValue;
  private final String swigName;
}

