/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 3.0.12
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package it.robur.test.physicalDCC.t32api;

public final class etIOModType {
  public final static etIOModType iomtACSSepValveNoFeedback = new etIOModType("iomtACSSepValveNoFeedback");
  public final static etIOModType iomtACSSepValveWithFeedback = new etIOModType("iomtACSSepValveWithFeedback");
  public final static etIOModType iomtCHValveNoFeedback = new etIOModType("iomtCHValveNoFeedback");
  public final static etIOModType iomtCHValveWithFeedback = new etIOModType("iomtCHValveWithFeedback");
  public final static etIOModType iomtChilProbes = new etIOModType("iomtChilProbes");
  public final static etIOModType iomtHeatProbes = new etIOModType("iomtHeatProbes");
  public final static etIOModType iomtSepProbes = new etIOModType("iomtSepProbes");
  public final static etIOModType iomtHeatHighEffAddInProbe = new etIOModType("iomtHeatHighEffAddInProbe");
  public final static etIOModType iomtChilCirc = new etIOModType("iomtChilCirc");
  public final static etIOModType iomtHeatCirc = new etIOModType("iomtHeatCirc");
  public final static etIOModType iomtSepCirc = new etIOModType("iomtSepCirc");
  public final static etIOModType iomtSecondaryPlantChilCirc = new etIOModType("iomtSecondaryPlantChilCirc");
  public final static etIOModType iomtSecondaryPlantHeatCirc = new etIOModType("iomtSecondaryPlantHeatCirc");
  public final static etIOModType MAX_IO_MOD_TYPE = new etIOModType("MAX_IO_MOD_TYPE");
  public final static etIOModType iomtNonRoburSimpleSep = new etIOModType("iomtNonRoburSimpleSep", 119);
  public final static etIOModType iomtNonRoburSimple = new etIOModType("iomtNonRoburSimple", 120);
  public final static etIOModType iomtNonRoburWithErrorSep = new etIOModType("iomtNonRoburWithErrorSep", 121);
  public final static etIOModType iomtNonRoburWithError = new etIOModType("iomtNonRoburWithError", 122);
  public final static etIOModType iomtNonRoburWithCircSep = new etIOModType("iomtNonRoburWithCircSep", 123);
  public final static etIOModType iomtNonRoburWithCirc = new etIOModType("iomtNonRoburWithCirc", 124);
  public final static etIOModType iomtNonRoburWithCircAndErrorSep = new etIOModType("iomtNonRoburWithCircAndErrorSep", 125);
  public final static etIOModType iomtNonRoburWithCircAndError = new etIOModType("iomtNonRoburWithCircAndError", 126);
  public final static etIOModType iomtVoid = new etIOModType("iomtVoid", 127);

  public final int swigValue() {
    return swigValue;
  }

  public String toString() {
    return swigName;
  }

  public static etIOModType swigToEnum(int swigValue) {
    if (swigValue < swigValues.length && swigValue >= 0 && swigValues[swigValue].swigValue == swigValue)
      return swigValues[swigValue];
    for (int i = 0; i < swigValues.length; i++)
      if (swigValues[i].swigValue == swigValue)
        return swigValues[i];
    throw new IllegalArgumentException("No enum " + etIOModType.class + " with value " + swigValue);
  }

  private etIOModType(String swigName) {
    this.swigName = swigName;
    this.swigValue = swigNext++;
  }

  private etIOModType(String swigName, int swigValue) {
    this.swigName = swigName;
    this.swigValue = swigValue;
    swigNext = swigValue+1;
  }

  private etIOModType(String swigName, etIOModType swigEnum) {
    this.swigName = swigName;
    this.swigValue = swigEnum.swigValue;
    swigNext = this.swigValue+1;
  }

  private static etIOModType[] swigValues = { iomtACSSepValveNoFeedback, iomtACSSepValveWithFeedback, iomtCHValveNoFeedback, iomtCHValveWithFeedback, iomtChilProbes, iomtHeatProbes, iomtSepProbes, iomtHeatHighEffAddInProbe, iomtChilCirc, iomtHeatCirc, iomtSepCirc, iomtSecondaryPlantChilCirc, iomtSecondaryPlantHeatCirc, MAX_IO_MOD_TYPE, iomtNonRoburSimpleSep, iomtNonRoburSimple, iomtNonRoburWithErrorSep, iomtNonRoburWithError, iomtNonRoburWithCircSep, iomtNonRoburWithCirc, iomtNonRoburWithCircAndErrorSep, iomtNonRoburWithCircAndError, iomtVoid };
  private static int swigNext = 0;
  private final int swigValue;
  private final String swigName;
}

