/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 3.0.12
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package it.robur.test.physicalDCC.t32api;

public final class etTemperatureIntegrType {
  public final static etTemperatureIntegrType titTempStandard = new etTemperatureIntegrType("titTempStandard");
  public final static etTemperatureIntegrType titTempAdvanced = new etTemperatureIntegrType("titTempAdvanced");

  public final int swigValue() {
    return swigValue;
  }

  public String toString() {
    return swigName;
  }

  public static etTemperatureIntegrType swigToEnum(int swigValue) {
    if (swigValue < swigValues.length && swigValue >= 0 && swigValues[swigValue].swigValue == swigValue)
      return swigValues[swigValue];
    for (int i = 0; i < swigValues.length; i++)
      if (swigValues[i].swigValue == swigValue)
        return swigValues[i];
    throw new IllegalArgumentException("No enum " + etTemperatureIntegrType.class + " with value " + swigValue);
  }

  private etTemperatureIntegrType(String swigName) {
    this.swigName = swigName;
    this.swigValue = swigNext++;
  }

  private etTemperatureIntegrType(String swigName, int swigValue) {
    this.swigName = swigName;
    this.swigValue = swigValue;
    swigNext = swigValue+1;
  }

  private etTemperatureIntegrType(String swigName, etTemperatureIntegrType swigEnum) {
    this.swigName = swigName;
    this.swigValue = swigEnum.swigValue;
    swigNext = this.swigValue+1;
  }

  private static etTemperatureIntegrType[] swigValues = { titTempStandard, titTempAdvanced };
  private static int swigNext = 0;
  private final int swigValue;
  private final String swigName;
}

