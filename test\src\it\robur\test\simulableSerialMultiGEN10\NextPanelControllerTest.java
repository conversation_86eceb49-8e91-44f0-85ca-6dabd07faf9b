package it.robur.test.simulableSerialMultiGEN10;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertThat;
import static org.junit.Assert.assertTrue;
import static uk.co.datumedge.hamcrest.json.SameJSONAs.sameJSONObjectAs;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.text.ParseException;
import java.util.Date;
import java.util.Locale;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.junit.After;
import org.junit.AfterClass;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Ignore;
import org.junit.Test;

import it.robur.gen10.GEN10.AnalogIndex;

import it.robur.gen10.GEN10.SummerVentilationSpeed;
import it.robur.gen10.GEN10.HeatingDiscretePowerLevel;
import it.robur.jserialtool.SerialPurejavacomm;
import it.robur.loggers.AllByTagJSONLogPublisher;
import it.robur.loggers.JSONPublisherDisableableDecorator;
import it.robur.loggers.JSONPublisherMulti;
import it.robur.loggers.JSONUtil;
import it.robur.loggers.LastByTagJSONLogPublisher;
import it.robur.loggers.LogConsts;
import it.robur.loggers.NextPanelJSONLogCons;
import it.robur.loggers.SimpleJSONLogPublisher;
import it.robur.loggers.Timer;
import it.robur.modbus.ModbusInstrument;
import it.robur.nextpanel.JSONNextPanel;
import it.robur.nextpanel.NextPanel;
import it.robur.nextpanel.NextPanelController;
import it.robur.nextpanel.StatefulNextZone;
import it.robur.nextpanel.NextPanel.Event.Category;
import it.robur.nextpanel.StatefulNextZone.ZoneConfig.HeatingTimer.Level;
import it.robur.nextpanel.StatefulNextZone.ZoneConfig.HeatingModeEnum;
import it.robur.nextpanel.StatefulNextZone.ZoneConfig.ServiceModeEnum;
import it.robur.test.multiplatform.util.JSONTestUtil;
import it.robur.test.multiplatform.util.TestUtil;
import it.robur.util.DebugLog;

public class NextPanelControllerTest
{
	
	// BEWARE!! TODO: remove each dontcare when adding it, and the remove the method altogether
	public static JSONObject nullifyNextPanelDontCares(JSONObject json) throws JSONException
	{	
		if( json.has("at") )
			json.put("at", JSONTestUtil.zeroTimestamp);
		
		if( json.has("param_ids") )
			json.put("param_ids", new JSONArray()); // i.e. zero-length
		
		
		JSONArray keys = json.names();		
		String key;
		if( keys != null )
			for (int i = 0; i < keys.length(); ++i) 
			{
			   key = keys.getString(i);
			   
			   if ( json.get(key) instanceof JSONObject )
				   nullifyNextPanelDontCares(json.getJSONObject(key));
			   else if ( json.get(key) instanceof JSONArray )
				   nullifyNextPanelDontCares(json.getJSONArray(key));		   
			}
		
		return json;
	}

	public static JSONArray nullifyNextPanelDontCares(JSONArray json) throws JSONException
	{
		for (int i = 0; i < json.length(); ++i) 
		{		   
		   if ( json.get(i) instanceof JSONObject )
			   nullifyNextPanelDontCares(json.getJSONObject(i));
		   else if ( json.get(i) instanceof JSONArray )
			   nullifyNextPanelDontCares(json.getJSONArray(i));		   
		}
		
		return json;		
	}
	
	
	

	public static final int MODBUS_COMMAND_DESTRATIFICATION_ADDR  = 798;
	public static final int MODBUS_COMMAND_DIFFERENTIAL_ADDR      = 799;
	public static final int MODBUS_COMMAND_OPERATION_REQUEST_ADDR = 800;
	public static final int MODBUS_COMMAND_AMBIENT_SETPOINT_ADDR  = 802;
	public static final int MODBUS_COMMAND_DISCRETE_POWER_LEVEL_ADDR = 803;
	public static final int MODBUS_COMMAND_AMBIENT_TEMP_ADDR = 804;
	
	public final static String path = "test/res/json/NextPanel";
	
	public final static String JSONIdentificationFilename = "identification";
	public final static String JSONIdentificationModule1NotCompatibleFilename = "identification_m1_nc";
	public final static String JSONIdentificationModule1OfflineFilename = "identification_m1_offline";
	public final static String JSONIdentificationAllModuleOfflineFilename = "identification_all_offline";
	public final static String JSONIdentificationModule4OfflineFilename = "identification_m4_offline";
	
	public final static String JSONSampleFilename = "sample";
	public final static String JSONSampleModule1NotCompatibleFilename = "sample_m1_nc";
	public final static String JSONSampleModule1OfflineFilename = "sample_m1_offline";
	public final static String JSONSampleAllModuleOfflineFilename = "sample_all_offline";
	public final static String JSONSampleModule4OfflineFilename = "sample_m4_offline";
	public final static String JSONSampleTemperatureProbeFilename = "sample_tprobe";
	public final static String JSONSampleFaultyTemperatureProbeFilename = "sample_faulty_tprobe";
	
	public final static String JSONConfigurationFilename = "configuration";	// one zone
	public final static String JSONAllZonesConfigurationFilename = "configuration_all_zones";
	public final static String JSONConfigurationProbeEnabledFilename = "configuration_probe_enabled";	// one zone
	
	public final static String JSONSplitAtSplitTimeFilename = "split";
	
	public final static String tProbeFilename = path + "/ntc_temperature_value";
	
	public final static int DEFAULT_AMBIENT_TEMP_VALUE = 200;
	
	private static DebugLog debugLog;
	
	private static NextPanel nextPanel;
	private static NextPanelController nextPanelController;
	
	private static LastByTagJSONLogPublisher jPublisher;
	
	@BeforeClass
	public static void setupTest() throws IOException
	{
		
	}
	
	@AfterClass
	public static void teardownTest() throws IOException
	{
		
	}
	
	@Before
	public void setup() throws IOException
	{
		debugLog = null;		
 
		nextPanel = null;
		nextPanelController = null;
		
	}
	
	@After
	public void teardown() throws IOException
	{
		if( nextPanel != null )
		{
			nextPanel.close();
			nextPanel = null;
		}
		
	}
	
	public void setupNextPanel() throws IOException
	{
		debugLog = new DebugLog(TestUtil.tmpPath, "npctest", true);
		
		SerialPurejavacomm serial = new SerialPurejavacomm(Settings.getPort());				
		jPublisher = new LastByTagJSONLogPublisher();
		
		nextPanel = new NextPanel("1.0", serial, jPublisher, new JSONPublisherDisableableDecorator(null), tProbeFilename, debugLog);		
	}
	
	public void setupNextPanelController() throws IOException
	{
		setupNextPanel();
		
		nextPanelController = new NextPanelController(nextPanel, debugLog);
		    	
	}
	
	@Test
	public void ProvisionalTest() throws IOException
	{
    	setupNextPanelController();
    	
    	nextPanelController.init(path, JSONIdentificationFilename, JSONConfigurationFilename);

    	nextPanel.getNextHeater(1).modbus.getModbusInstrument().writeRegister(808, 0x5A5A, 6);

    	nextPanel.getNextHeater(1).modbus.getModbusInstrument().writeRegister(808, 0xA5A5, 6);
		
	}
	
	private  void presetUnmuteAndRoburOEMCode() throws IOException
	{
    	// preset OEM Code compatibility OK
    	for(int i=0; i<nextPanel.heaters.length; ++i)
    	{
    		nextPanel.heaters[i].modbus.getModbusInstrument().writeRegister(808, 0xA5A5, 6);	// unmute
    		nextPanel.heaters[i].modbus.getModbusInstrument().writeRegister(0, 0);	  			// OEM Robur
    	}
	}
	
	@Test
	public void TestTickControllerOneModuleOffline() throws IOException
	{
    	setupNextPanelController();
    	
    	nextPanelController.init(path, JSONIdentificationFilename, JSONConfigurationFilename);

    	// include heater not bound with any zone
    	nextPanel.getNextHeater(4).stateful.config.userExcluded = false;	// set different from loaded configuration.json
    	presetUnmuteAndRoburOEMCode();
    	
    	nextPanel.setHostConfig.putIfEmpty(JSONUtil.loadJSON(path, "echoconfig").getJSONObject("config"));	// needed for tick.op wait for confserver
    	// System.out.println("serial da echoconfig: " + nextPanel.setHostConfig.getSerial()); // DEBUG ONLY

        // preset unit test default ambient temp
    	for(int i=0; i<nextPanel.heaters.length; ++i)
    	{
    		nextPanel.heaters[i].modbus.getModbusInstrument().writeRegister(500, 2);
    		nextPanel.heaters[i].modbus.getModbusInstrument().writeRegister(504, DEFAULT_AMBIENT_TEMP_VALUE);	
    	}

    	nextPanelController.tick.op();
    	
    	assertThat(nextPanel.heaters.length, equalTo(3));	// according to reference identification

    	// only for debug
    	//System.out.println(jPublisher.getLast("np_identification"));
    	//System.out.println(jPublisher.getLast("np_sample"));

    	for(int i=0; i<nextPanel.heaters.length; ++i)
    	{
	    	// check online & compatibility
	    	assertFalse(nextPanel.heaters[i].stateful.offline);
	    	assertTrue(nextPanel.heaters[i].stateful.compatible);
    	}
    	
        assertThat(
        		nullifyNextPanelDontCares((jPublisher.getLast("np_identification"))),
                sameJSONObjectAs(nullifyNextPanelDontCares(JSONTestUtil.loadJSONSubDir("NextPanel", JSONIdentificationFilename))));   	

        assertThat(
        		nullifyNextPanelDontCares((jPublisher.getLast("np_sample"))),
                sameJSONObjectAs(nullifyNextPanelDontCares(JSONTestUtil.loadJSONSubDir("NextPanel", JSONSampleFilename))));   	
    	
    	
    	// now simulate module 1 offline by writing to 40809 value 0x5A5A
    	nextPanel.getNextHeater(1).modbus.getModbusInstrument().writeRegister(808, 0x5A5A, 6);
    	
    	nextPanelController.tick.op();
    	//nextPanelController.tick.op();	// PROVVI

    	assertTrue(nextPanel.getNextHeater(1).stateful.offline);
    	assertFalse(nextPanel.getNextHeater(2).stateful.offline);
    	assertFalse(nextPanel.getNextHeater(4).stateful.offline);

    	// only for debug
    	//System.out.println(jPublisher.getLast("np_sample"));
    	assertThat(
        		nullifyNextPanelDontCares((jPublisher.getLast("np_identification"))),
                sameJSONObjectAs(nullifyNextPanelDontCares(JSONTestUtil.loadJSONSubDir("NextPanel", JSONIdentificationModule1OfflineFilename))));   	
    	
        assertThat(
        		nullifyNextPanelDontCares((jPublisher.getLast("np_sample"))),
                sameJSONObjectAs(nullifyNextPanelDontCares(JSONTestUtil.loadJSONSubDir("NextPanel", JSONSampleModule1OfflineFilename))));   	

    	// recover module 1 online by writing to 40809 value 0xA5A5
    	nextPanel.getNextHeater(1).modbus.getModbusInstrument().writeRegister(808, 0xA5A5, 6);

    	nextPanelController.tick.op();

    	assertFalse(nextPanel.getNextHeater(1).stateful.offline);
    	assertFalse(nextPanel.getNextHeater(2).stateful.offline);
    	assertFalse(nextPanel.getNextHeater(4).stateful.offline);

        assertThat(
        		nullifyNextPanelDontCares((jPublisher.getLast("np_identification"))),
                sameJSONObjectAs(nullifyNextPanelDontCares(JSONTestUtil.loadJSONSubDir("NextPanel", JSONIdentificationFilename))));   	

        assertThat(
        		nullifyNextPanelDontCares((jPublisher.getLast("np_sample"))),
                sameJSONObjectAs(nullifyNextPanelDontCares(JSONTestUtil.loadJSONSubDir("NextPanel", JSONSampleFilename))));   	
	}


	@Test
	public void TestTickControllerLastModuleOffline() throws IOException
	{
    	setupNextPanelController();
    	
    	nextPanelController.init(path, JSONIdentificationFilename, JSONConfigurationFilename);

    	// include heater not bound with any zone
    	nextPanel.getNextHeater(4).stateful.config.userExcluded = false;	// set different from loaded configuration.json
    	presetUnmuteAndRoburOEMCode();

    	nextPanel.setHostConfig.putIfEmpty(JSONUtil.loadJSON(path, "echoconfig").getJSONObject("config"));	// needed for tick.op wait for confserver

        // preset unit test default ambient temp
    	for(int i=0; i<nextPanel.heaters.length; ++i)
    	{
    		nextPanel.heaters[i].modbus.getModbusInstrument().writeRegister(500, 2);
    		nextPanel.heaters[i].modbus.getModbusInstrument().writeRegister(504, DEFAULT_AMBIENT_TEMP_VALUE);	
    	}
    	
    	nextPanelController.tick.op();
    	
    	assertThat(nextPanel.heaters.length, equalTo(3));	// according to reference identification

    	// only for debug
    	//System.out.println(jPublisher.getLast("np_identification"));
    	//System.out.println(jPublisher.getLast("np_sample"));

    	for(int i=0; i<nextPanel.heaters.length; ++i)
    	{
	    	// check online & compatibility
	    	assertFalse(nextPanel.heaters[i].stateful.offline);
	    	assertTrue(nextPanel.heaters[i].stateful.compatible);
    	}
    	
        assertThat(
        		nullifyNextPanelDontCares((jPublisher.getLast("np_identification"))),
                sameJSONObjectAs(nullifyNextPanelDontCares(JSONTestUtil.loadJSONSubDir("NextPanel", JSONIdentificationFilename))));   	

        assertThat(
        		nullifyNextPanelDontCares((jPublisher.getLast("np_sample"))),
                sameJSONObjectAs(nullifyNextPanelDontCares(JSONTestUtil.loadJSONSubDir("NextPanel", JSONSampleFilename))));   	
    	
    	
    	// now simulate third module (4) offline
    	nextPanel.getNextHeater(4).modbus.getModbusInstrument().writeRegister(808, 0x5A5A, 6);
    	
    	nextPanelController.tick.op();

    	assertFalse(nextPanel.getNextHeater(1).stateful.offline);
    	assertFalse(nextPanel.getNextHeater(2).stateful.offline);
    	assertTrue(nextPanel.getNextHeater(4).stateful.offline);

    	// only for debug
    	//System.out.println(jPublisher.getLast("np_sample"));
    	assertThat(
        		nullifyNextPanelDontCares((jPublisher.getLast("np_identification"))),
                sameJSONObjectAs(nullifyNextPanelDontCares(JSONTestUtil.loadJSONSubDir("NextPanel", JSONIdentificationModule4OfflineFilename))));   	
    	
        assertThat(
        		nullifyNextPanelDontCares((jPublisher.getLast("np_sample"))),
                sameJSONObjectAs(nullifyNextPanelDontCares(JSONTestUtil.loadJSONSubDir("NextPanel", JSONSampleModule4OfflineFilename))));   	

    	// recover module 4 online 
    	nextPanel.getNextHeater(4).modbus.getModbusInstrument().writeRegister(808, 0xA5A5, 6);

    	nextPanelController.tick.op();

    	assertFalse(nextPanel.getNextHeater(1).stateful.offline);
    	assertFalse(nextPanel.getNextHeater(2).stateful.offline);
    	assertFalse(nextPanel.getNextHeater(4).stateful.offline);

        assertThat(
        		nullifyNextPanelDontCares((jPublisher.getLast("np_identification"))),
                sameJSONObjectAs(nullifyNextPanelDontCares(JSONTestUtil.loadJSONSubDir("NextPanel", JSONIdentificationFilename))));   	

        assertThat(
        		nullifyNextPanelDontCares((jPublisher.getLast("np_sample"))),
                sameJSONObjectAs(nullifyNextPanelDontCares(JSONTestUtil.loadJSONSubDir("NextPanel", JSONSampleFilename))));   	
	}

	
	
	@Test
	public void TestTickControllerAllModuleOffline() throws IOException
	{
    	setupNextPanelController();
    	
    	nextPanelController.init(path, JSONIdentificationFilename, JSONConfigurationFilename);

    	// include heater not bound with any zone
    	nextPanel.getNextHeater(4).stateful.config.userExcluded = false;	// set different from loaded configuration.json
    	presetUnmuteAndRoburOEMCode();

    	nextPanel.setHostConfig.putIfEmpty(JSONUtil.loadJSON(path, "echoconfig").getJSONObject("config"));	// needed for tick.op wait for confserver

        // preset unit test default ambient temp
    	for(int i=0; i<nextPanel.heaters.length; ++i)
    	{
    		nextPanel.heaters[i].modbus.getModbusInstrument().writeRegister(500, 2);
    		nextPanel.heaters[i].modbus.getModbusInstrument().writeRegister(504, DEFAULT_AMBIENT_TEMP_VALUE);	
    	}
    	
    	nextPanelController.tick.op();
    	
    	assertThat(nextPanel.heaters.length, equalTo(3));	// according to reference identification

    	for(int i=0; i<nextPanel.heaters.length; ++i)
    	{
	    	// check online & compatibility
	    	assertFalse(nextPanel.heaters[i].stateful.offline);
	    	assertTrue(nextPanel.heaters[i].stateful.compatible);
    	}
    	
        assertThat(
        		nullifyNextPanelDontCares((jPublisher.getLast("np_identification"))),
                sameJSONObjectAs(nullifyNextPanelDontCares(JSONTestUtil.loadJSONSubDir("NextPanel", JSONIdentificationFilename))));   	

        assertThat(
        		nullifyNextPanelDontCares((jPublisher.getLast("np_sample"))),
                sameJSONObjectAs(nullifyNextPanelDontCares(JSONTestUtil.loadJSONSubDir("NextPanel", JSONSampleFilename))));   	
    	
    	
    	// now set all modules offline
    	for(int i=0; i<nextPanel.heaters.length; ++i)
    	{
    		nextPanel.heaters[i].modbus.getModbusInstrument().writeRegister(808, 0x5A5A, 6);
    	}
    	
    	nextPanelController.tick.op();

    	assertTrue(nextPanel.getNextHeater(1).stateful.offline);
    	assertTrue(nextPanel.getNextHeater(2).stateful.offline);
    	assertTrue(nextPanel.getNextHeater(4).stateful.offline);

    	// only for debug
    	//System.out.println(jPublisher.getLast("np_sample"));
    	
    	assertThat(
        		nullifyNextPanelDontCares((jPublisher.getLast("np_identification"))),
                sameJSONObjectAs(nullifyNextPanelDontCares(JSONTestUtil.loadJSONSubDir("NextPanel", JSONIdentificationAllModuleOfflineFilename))));   	
    	
        assertThat(
        		nullifyNextPanelDontCares((jPublisher.getLast("np_sample"))),
                sameJSONObjectAs(nullifyNextPanelDontCares(JSONTestUtil.loadJSONSubDir("NextPanel", JSONSampleAllModuleOfflineFilename))));   	

    	// recover all modules online 
    	try{Thread.sleep(500);}
    	catch(InterruptedException e){System.out.println(e);} 
    	
    	for(int i=0; i<nextPanel.heaters.length; ++i)
    	{
    		nextPanel.heaters[i].modbus.getModbusInstrument().writeRegister(808, 0xA5A5, 6);
    	}

    	nextPanelController.tick.op();

    	assertFalse(nextPanel.getNextHeater(1).stateful.offline);
    	assertFalse(nextPanel.getNextHeater(2).stateful.offline);
    	assertFalse(nextPanel.getNextHeater(4).stateful.offline);

        assertThat(
        		nullifyNextPanelDontCares((jPublisher.getLast("np_identification"))),
                sameJSONObjectAs(nullifyNextPanelDontCares(JSONTestUtil.loadJSONSubDir("NextPanel", JSONIdentificationFilename))));   	

        assertThat(
        		nullifyNextPanelDontCares((jPublisher.getLast("np_sample"))),
                sameJSONObjectAs(nullifyNextPanelDontCares(JSONTestUtil.loadJSONSubDir("NextPanel", JSONSampleFilename))));   	
	}
	

	
	@Test
	public void TestTickControllerAllModuleInitiallyOffline() throws IOException
	{
    	setupNextPanelController();
    	
    	nextPanelController.init(path, JSONIdentificationFilename, JSONConfigurationFilename);

    	// include heater 4 not bound with any zone
    	nextPanel.getNextHeater(4).stateful.config.userExcluded = false;	// set different from loaded configuration.json
    	presetUnmuteAndRoburOEMCode();

    	nextPanel.setHostConfig.putIfEmpty(JSONUtil.loadJSON(path, "echoconfig").getJSONObject("config"));	// needed for tick.op wait for confserver

    	// set all module modbus mute to cause offline 
    	for(int i=0; i<nextPanel.heaters.length; ++i)
    	{
    		nextPanel.heaters[i].modbus.getModbusInstrument().writeRegister(808, 0x5A5A, 6);
    	}

    	nextPanelController.tick.op();
    	
    	assertThat(nextPanel.heaters.length, equalTo(3));	// according to reference identification

    	assertTrue(nextPanel.getNextHeater(1).stateful.offline);
    	assertTrue(nextPanel.getNextHeater(2).stateful.offline);
    	assertTrue(nextPanel.getNextHeater(4).stateful.offline);
    	// module's compatible flag don't care

    	// only for debug
    	//System.out.println(jPublisher.getLast("np_identification"));
    	//System.out.println(nullifyNextPanelDontCares((jPublisher.getLast("np_identification"))));
    	
        // NB serial should non be in identification
    	assertThat(
        		nullifyNextPanelDontCares((jPublisher.getLast("np_identification"))),
                sameJSONObjectAs(nullifyNextPanelDontCares(JSONTestUtil.loadJSONSubDir("NextPanel", JSONIdentificationAllModuleOfflineFilename))));   	
    	
        assertThat(
        		nullifyNextPanelDontCares((jPublisher.getLast("np_sample"))),
                sameJSONObjectAs(nullifyNextPanelDontCares(JSONTestUtil.loadJSONSubDir("NextPanel", JSONSampleAllModuleOfflineFilename))));   	
        
    	// unmute all module to recover online
    	try{Thread.sleep(500);}
    	catch(InterruptedException e){System.out.println(e);} 
    	
    	for(int i=0; i<nextPanel.heaters.length; ++i)
    	{
    		nextPanel.heaters[i].modbus.getModbusInstrument().writeRegister(808, 0xA5A5, 6);
    	}
    	
    	nextPanelController.tick.op();

    	for(int i=0; i<nextPanel.heaters.length; ++i)
    	{
	    	// check online & compatibility
	    	assertFalse(nextPanel.heaters[i].stateful.offline);
	    	assertTrue(nextPanel.heaters[i].stateful.compatible);
    	}
    	
        assertThat(
        		nullifyNextPanelDontCares((jPublisher.getLast("np_identification"))),
                sameJSONObjectAs(nullifyNextPanelDontCares(JSONTestUtil.loadJSONSubDir("NextPanel", JSONIdentificationFilename))));   	

	}	
	
	@Test
	public void TestTickControllerOneModuleInitiallyOffline() throws IOException
	{
    	setupNextPanelController();
    	
    	nextPanelController.init(path, JSONIdentificationFilename, JSONConfigurationFilename);

    	// include heater 4 not bound with any zone
    	nextPanel.getNextHeater(4).stateful.config.userExcluded = false;	// set different from loaded configuration.json
    	presetUnmuteAndRoburOEMCode();

    	nextPanel.setHostConfig.putIfEmpty(JSONUtil.loadJSON(path, "echoconfig").getJSONObject("config"));	// needed for tick.op wait for confserver

        // preset unit test default ambient temp
    	for(int i=0; i<nextPanel.heaters.length; ++i)
    	{
    		nextPanel.heaters[i].modbus.getModbusInstrument().writeRegister(500, 2);
    		nextPanel.heaters[i].modbus.getModbusInstrument().writeRegister(504, DEFAULT_AMBIENT_TEMP_VALUE);	
    	}
    	
    	// set module modbus mute to cause module 1 offline 
    	nextPanel.getNextHeater(1).modbus.getModbusInstrument().writeRegister(808, 0x5A5A, 6);

    	nextPanelController.tick.op();
    	
    	assertThat(nextPanel.heaters.length, equalTo(3));	// according to reference identification

    	assertTrue(nextPanel.getNextHeater(1).stateful.offline);
    	// module 1 compatible flag don't care
    	assertFalse(nextPanel.getNextHeater(2).stateful.offline);
    	assertTrue(nextPanel.getNextHeater(2).stateful.compatible);
    	assertFalse(nextPanel.getNextHeater(4).stateful.offline);
    	assertTrue(nextPanel.getNextHeater(4).stateful.compatible);

    	// only for debug
    	//System.out.println(jPublisher.getLast("np_identification"));
    	//System.out.println(nullifyNextPanelDontCares(JSONTestUtil.nullifyDontCares(jPublisher.getLast("np_identification"))));
    	
        // NB "serial" should non be in identification for offline module
    	assertThat(
        		nullifyNextPanelDontCares((jPublisher.getLast("np_identification"))),
                sameJSONObjectAs(nullifyNextPanelDontCares(JSONTestUtil.loadJSONSubDir("NextPanel", JSONIdentificationModule1OfflineFilename))));   	
    	
        assertThat(
        		nullifyNextPanelDontCares((jPublisher.getLast("np_sample"))),
                sameJSONObjectAs(nullifyNextPanelDontCares(JSONTestUtil.loadJSONSubDir("NextPanel", JSONSampleModule1OfflineFilename))));   	
        
    	// unmute module 1 to recover online
    	nextPanel.getNextHeater(1).modbus.getModbusInstrument().writeRegister(808, 0xA5A5, 6);
    	
    	nextPanelController.tick.op();

    	for(int i=0; i<nextPanel.heaters.length; ++i)
    	{
	    	// check online & compatibility
	    	assertFalse(nextPanel.heaters[i].stateful.offline);
	    	assertTrue(nextPanel.heaters[i].stateful.compatible);
    	}

    	// PROVVI
    	//System.out.println(jPublisher.getLast("np_sample"));
    	
        assertThat(
        		nullifyNextPanelDontCares((jPublisher.getLast("np_identification"))),
                sameJSONObjectAs(nullifyNextPanelDontCares(JSONTestUtil.loadJSONSubDir("NextPanel", JSONIdentificationFilename))));   	

        assertThat(
        		nullifyNextPanelDontCares((jPublisher.getLast("np_sample"))),
                sameJSONObjectAs(nullifyNextPanelDontCares(JSONTestUtil.loadJSONSubDir("NextPanel", JSONSampleFilename))));   	
    	
    	// Now test module 1 offline => online but module 1 not compatible 
    	nextPanel.getNextHeater(1).modbus.getModbusInstrument().writeRegister(808, 0x5A5A, 6);	// mute
    	nextPanelController.tick.op();
    	assertTrue(nextPanel.getNextHeater(1).stateful.offline);

    	nextPanel.getNextHeater(1).modbus.getModbusInstrument().writeRegister(808, 0xA5A5, 6);	// unmute
    	nextPanel.getNextHeater(1).modbus.getModbusInstrument().writeRegister(0, 5);	// set OEM Flowair (i.e not compatible) 
    	nextPanelController.tick.op();
    	assertFalse(nextPanel.getNextHeater(1).stateful.offline);
    	assertFalse(nextPanel.getNextHeater(1).stateful.compatible);
    	
    	// only for debug
    	//System.out.println(jPublisher.getLast("np_identification"));
    	//System.out.println(jPublisher.getLast("np_sample"));
    	
        // NB serial should not be present in identification
        assertThat(
        		nullifyNextPanelDontCares((jPublisher.getLast("np_identification"))),
                sameJSONObjectAs(nullifyNextPanelDontCares(JSONTestUtil.loadJSONSubDir("NextPanel", JSONIdentificationModule1NotCompatibleFilename))));   	

        assertThat(
        		nullifyNextPanelDontCares((jPublisher.getLast("np_sample"))),
                sameJSONObjectAs(nullifyNextPanelDontCares(JSONTestUtil.loadJSONSubDir("NextPanel", JSONSampleModule1NotCompatibleFilename))));   	

        // recover compatibility
    	nextPanel.getNextHeater(1).modbus.getModbusInstrument().writeRegister(0, 0);

		// ADDED TEST TO CHECK DIFFERENCE BETWEEN FIRST READ WITH MODULE OFFLINE AND A READ WITH SAME MODULE OFFLINE BUT PREVIOUSLY ONLINE.
		// USED TO CHECK "SERIAL" PRESENCE IN IDENTIFICATION. 

		// set module modbus mute to cause offline 
    	nextPanel.getNextHeater(1).modbus.getModbusInstrument().writeRegister(808, 0x5A5A, 6);

    	nextPanelController.tick.op();

    	// unmute module 1 to recover online
    	nextPanel.getNextHeater(1).modbus.getModbusInstrument().writeRegister(808, 0xA5A5, 6);
    	
    	nextPanelController.tick.op();
    	// Now module 1 is online and compatible
    	
    	// only for debug
    	//System.out.println(jPublisher.getLast("np_identification"));

        assertThat(
        		nullifyNextPanelDontCares((jPublisher.getLast("np_identification"))),
                sameJSONObjectAs(nullifyNextPanelDontCares(JSONTestUtil.loadJSONSubDir("NextPanel", JSONIdentificationFilename))));   	
    	
        assertThat(
        		nullifyNextPanelDontCares((jPublisher.getLast("np_sample"))),
                sameJSONObjectAs(nullifyNextPanelDontCares(JSONTestUtil.loadJSONSubDir("NextPanel", JSONSampleFilename))));   	
	}

	
	@Test
	public void TestTickControllerOneModuleInitiallyOfflineMultipleTick() throws IOException
	{
    	setupNextPanelController();
    	
    	nextPanelController.init(path, JSONIdentificationFilename, JSONConfigurationFilename);

    	// include heater 4 not bound with any zone
    	nextPanel.getNextHeater(4).stateful.config.userExcluded = false;	// set different from loaded configuration.json
    	presetUnmuteAndRoburOEMCode();

    	nextPanel.setHostConfig.putIfEmpty(JSONUtil.loadJSON(path, "echoconfig").getJSONObject("config"));	// needed for tick.op wait for confserver
    	
    	// set module modbus mute to cause module 1 offline 
    	nextPanel.getNextHeater(1).modbus.getModbusInstrument().writeRegister(808, 0x5A5A, 6);

    	nextPanelController.tick.op();
    	
    	assertThat(nextPanel.heaters.length, equalTo(3));	// according to reference identification

    	assertTrue(nextPanel.getNextHeater(1).stateful.offline);
    	// module 1 compatible flag don't care
    	assertFalse(nextPanel.getNextHeater(2).stateful.offline);
    	assertTrue(nextPanel.getNextHeater(2).stateful.compatible);
    	assertFalse(nextPanel.getNextHeater(4).stateful.offline);
    	assertTrue(nextPanel.getNextHeater(4).stateful.compatible);

        // NB "serial" should non be in identification
    	assertThat(
        		nullifyNextPanelDontCares((jPublisher.getLast("np_identification"))),
                sameJSONObjectAs(nullifyNextPanelDontCares(JSONTestUtil.loadJSONSubDir("NextPanel", JSONIdentificationModule1OfflineFilename))));   	
    	
        // TODO add sample check
        
    	// do another tick keeping module 1 offline: again nothing should change
    	nextPanelController.tick.op();

    	assertTrue(nextPanel.getNextHeater(1).stateful.offline);
    	// module 1 compatible flag don't care
    	assertFalse(nextPanel.getNextHeater(2).stateful.offline);
    	assertTrue(nextPanel.getNextHeater(2).stateful.compatible);
    	assertFalse(nextPanel.getNextHeater(4).stateful.offline);
    	assertTrue(nextPanel.getNextHeater(4).stateful.compatible);

    	assertThat(
        		nullifyNextPanelDontCares((jPublisher.getLast("np_identification"))),
                sameJSONObjectAs(nullifyNextPanelDontCares(JSONTestUtil.loadJSONSubDir("NextPanel", JSONIdentificationModule1OfflineFilename))));   	
    	
    	// unmute module 1 to recover online
    	nextPanel.getNextHeater(1).modbus.getModbusInstrument().writeRegister(808, 0xA5A5, 6);
    	
    	nextPanelController.tick.op();

    	for(int i=0; i<nextPanel.heaters.length; ++i)
    	{
	    	// check online & compatibility
	    	assertFalse(nextPanel.heaters[i].stateful.offline);
	    	assertTrue(nextPanel.heaters[i].stateful.compatible);
    	}
    	
        assertThat(
        		nullifyNextPanelDontCares((jPublisher.getLast("np_identification"))),
                sameJSONObjectAs(nullifyNextPanelDontCares(JSONTestUtil.loadJSONSubDir("NextPanel", JSONIdentificationFilename))));   	

	}
	
	
	
    @Test
    public void TestTickControllerOnlineNotCompatible() throws IOException
    {
    	int count;
    	
    	setupNextPanelController();
    	
    	nextPanelController.init(path, JSONIdentificationFilename, JSONConfigurationFilename);

    	presetUnmuteAndRoburOEMCode();

    	nextPanel.setHostConfig.putIfEmpty(JSONUtil.loadJSON(path, "echoconfig").getJSONObject("config"));	// needed for tick.op wait for confserver
    	
    	// set all modules not compatible: OEM compatibility code major
    	for(int i=0; i<nextPanel.heaters.length; ++i)
    	{
    		nextPanel.heaters[i].modbus.getModbusInstrument().writeRegister(1, 3);	// compatible value is 1
    	}
    	
    	for(count=0; count < 4; count++)
    	{
	    	nextPanelController.tick.op();
	    	// check online & compatibility
	    	for(int j=0; j<nextPanel.heaters.length; ++j)
	    	{
		    	assertFalse(nextPanel.heaters[j].stateful.offline);
		    	assertFalse(nextPanel.heaters[j].stateful.compatible);
	    	}
    	}
    	// check analogs: should be not read
    	for(int i=0; i<nextPanel.heaters.length; ++i)
    	{
    		assertThat(nextPanel.heaters[i].stateful.analogs[AnalogIndex.SETPOINT_TEMP.ordinal()],
    				equalTo(null));
    	}
    	
    	// now set compatible and check if ok
    	for(int i=0; i<nextPanel.heaters.length; ++i)
    	{
    		nextPanel.heaters[i].modbus.getModbusInstrument().writeRegister(1, 1);
    		
        	// simulate offline - needed to re-check compatibility - NB simulation needed because setting modSim offline presently is not supported.
        	nextPanel.heaters[i].stateful.offline = true;
    	}
    	// preset setpoint value
    	for(int i=0; i<nextPanel.heaters.length; ++i)
    	{
    		nextPanel.heaters[i].modbus.getModbusInstrument().writeRegister(500, 9);
    		nextPanel.heaters[i].modbus.getModbusInstrument().writeRegister(504, ModbusInstrument.NO_VALUE_SIGNED);	
    	}
    	nextPanelController.tick.op();
    	// check online & compatibility
    	for(int i=0; i<nextPanel.heaters.length; ++i)
    	{
	    	assertFalse(nextPanel.heaters[i].stateful.offline);
	    	assertTrue(nextPanel.heaters[i].stateful.compatible);

	    	assertThat(nextPanel.heaters[i].stateful.analogs[AnalogIndex.SETPOINT_TEMP.ordinal()],
					equalTo(LogConsts.NO_VALUE_STRING));
    	}
    	
    	// now set not compatible appliance type (and force offline to update) ========== 
    	for(int i=0; i<nextPanel.heaters.length; ++i)
    	{
			nextPanel.heaters[i].modbus.getModbusInstrument().writeRegister(110, 45);
	    	nextPanel.heaters[i].stateful.offline = true;
    	}
    	nextPanelController.tick.op();
    	// check online & compatibility
    	for(int i=0; i<nextPanel.heaters.length; ++i)
    	{
	    	assertFalse(nextPanel.heaters[i].stateful.offline);
	    	assertFalse(nextPanel.heaters[i].stateful.compatible);

	    	// TODO check if analogs should keep previous value or not
	    	//assertThat(nextPanel.heaters[i].stateful.analogs[AnalogIndex.SETPOINT_TEMP.ordinal()],
	    	//		equalTo(LogConsts.NO_VALUE_STRING));
    	}
    	// restore compatible appliance type  
    	for(int i=0; i<nextPanel.heaters.length; ++i)
    	{
			nextPanel.heaters[i].modbus.getModbusInstrument().writeRegister(110, 0);
	    	nextPanel.heaters[i].stateful.offline = true;
    	}
    	nextPanelController.tick.op();
    	// check online & compatibility
    	for(int i=0; i<nextPanel.heaters.length; ++i)
    	{
	    	assertFalse(nextPanel.heaters[i].stateful.offline);
	    	assertTrue(nextPanel.heaters[i].stateful.compatible);
    	}
    	// ===========================
    	
    	
    	
    	// TODO test real offline using 40809 register when supported by GEN10 simulator
    }    	
    	
    
    
    @Test
    public void TestTickController() throws IOException
    {
    	setupNextPanelController();
    	
    	nextPanelController.init(path, JSONIdentificationFilename, JSONConfigurationFilename);
    	
    	presetUnmuteAndRoburOEMCode();

    	nextPanel.setHostConfig.putIfEmpty(JSONUtil.loadJSON(path, "echoconfig").getJSONObject("config"));	// needed for tick.op wait for confserver
    	
    	// preset ambient temperature to check mean temperature computation
    	for(int i=0; i<nextPanel.heaters.length; ++i)
    	{
    		nextPanel.heaters[i].modbus.getModbusInstrument().writeRegister(500, 2);
    		nextPanel.heaters[i].modbus.getModbusInstrument().writeRegister(504, ModbusInstrument.NO_VALUE_SIGNED);	
    	}
    	// preset setpoint value
    	for(int i=0; i<nextPanel.heaters.length; ++i)
    	{
    		nextPanel.heaters[i].modbus.getModbusInstrument().writeRegister(500, 9);
    		nextPanel.heaters[i].modbus.getModbusInstrument().writeRegister(504, ModbusInstrument.NO_VALUE_SIGNED);	
    	}
    	// include heater not bound with any zone
    	nextPanel.getNextHeater(4).stateful.config.userExcluded = false;	// set different from loaded configuration.json

    	nextPanelController.tick.op();
    	
    	assertThat(nextPanel.heaters.length, equalTo(3));	// according to reference identification

    	JSONArray statistics = jPublisher.getLast("np_split").getJSONArray("statistics_samples").getJSONObject(0).getJSONArray("heater_modules");
    	for(int i=0; i<nextPanel.heaters.length; ++i)
    	{
	    	// check online & compatibility
	    	assertFalse(nextPanel.heaters[i].stateful.offline);
	    	assertTrue(nextPanel.heaters[i].stateful.compatible);
	    	
	    	// check analogs
	    	// current setpoint shall be ABSENT_VALUE (i.e. not provided) when module is turned off. 
	    	// here we check NO_VALUE because of limitation of preset modbus slave simulator ****
	    	assertThat(nextPanel.heaters[i].stateful.analogs[AnalogIndex.SETPOINT_TEMP.ordinal()],
	    					equalTo(LogConsts.NO_VALUE_STRING));

	    	// check statistics (json format)
	    	assertThat(statistics.getJSONObject(i).getJSONObject("counters").getString("power_on_time"), equalTo("7200"));
	    	assertThat(statistics.getJSONObject(i).getJSONObject("counters").getString("flame_on_time"), equalTo("3600"));
	    	assertThat(statistics.getJSONObject(i).getJSONObject("counters").getString("ignitions"), equalTo("10"));
	    	assertThat(statistics.getJSONObject(i).getJSONObject("counters").getString("missed_ignitions"), equalTo("2"));
    	}    	
    	
    	// check mean ambient temperature (first zone)
    	assertThat(nextPanel.stateful.getZone(1).meanAmbientTemp, equalTo(ModbusInstrument.NO_VALUE_SIGNED));
    	
    	
    	// check commands
    	// NB this test should read from modbus to get command registers
    	//assertThat(nextPanel.stateful.getZone(1).config.differential, equalTo(20));	// default value
    	
    	// check commands on first zone's modules
    	
    	
    	// heaters modbusID 4 do not belongs to any zone, so module's default commands should apply.
    	assertThat(nextPanel.getNextHeater(4).modbus.getModbusInstrument().readRegisters(MODBUS_COMMAND_DIFFERENTIAL_ADDR, 1)[0], equalTo(20));
	

    	// NEW TICK
    	// preset differential in zone 0 - 
    	// NB: only first 2 modules (of 3) belong to zone 0.
    	nextPanel.stateful.getZone(1).config.onSwitch = false;
    	nextPanel.stateful.getZone(1).config.destratification = true;
    	int myDifferential = 40;
    	nextPanel.stateful.getZone(1).config.differential = myDifferential;

    	// NB : due to ModSim limitation, all heater modules share same registers values, so only last written value counts.
    	// So I need to exclude last module. This test is compatible with GEN10 simulator, soon to be introduced. 
    	nextPanel.heaters[2].stateful.config.userExcluded = true;

    	// preset ambient temperature to check mean temperature computation
    	for(int i=0; i<nextPanel.heaters.length; ++i)
    	{
    		nextPanel.heaters[i].modbus.getModbusInstrument().writeRegister(500, 2);
    		nextPanel.heaters[i].modbus.getModbusInstrument().writeRegister(504, 195);	// i.e. 19,5°C
    	}
    	// TODO add test with different values when GEN10 simulator will be introduced.
    	
    	nextPanelController.tick.op();

    	assertThat(nextPanel.stateful.getZone(1).config.differential, equalTo(myDifferential));
    	// check mean ambient temperature (first zone)
    	assertThat(nextPanel.stateful.getZone(1).meanAmbientTemp, equalTo(195));
    	
    	
    	// check command registers only for heaters belonging to zone 0 (first zone)
    	for(int i=0; i<2; ++i)
    	{
    		assertThat(nextPanel.heaters[i].modbus.getModbusInstrument().readRegisters(MODBUS_COMMAND_DESTRATIFICATION_ADDR, 1)[0], equalTo(1));	// i.e. ON
    		assertThat(nextPanel.heaters[i].modbus.getModbusInstrument().readRegisters(MODBUS_COMMAND_DIFFERENTIAL_ADDR, 1)[0], equalTo(myDifferential));
    		assertThat(nextPanel.heaters[i].modbus.getModbusInstrument().readRegisters(MODBUS_COMMAND_OPERATION_REQUEST_ADDR, 1)[0], equalTo(0));	// i.e. OFF
    		// other commands don't care when off
    	}
    	
    	
    	// initially used to generate reference JSON:
//    	JSONTestUtil.saveJSON("identification", jPublisher.getLast("np_identification"));
    	    	
        assertThat(
        		nullifyNextPanelDontCares((jPublisher.getLast("np_identification"))),
                sameJSONObjectAs(nullifyNextPanelDontCares(JSONTestUtil.loadJSONSubDir("NextPanel", JSONIdentificationFilename))));   	

        // recover default ambient temp
    	for(int i=0; i<nextPanel.heaters.length; ++i)
    	{
    		nextPanel.heaters[i].modbus.getModbusInstrument().writeRegister(500, 2);
    		nextPanel.heaters[i].modbus.getModbusInstrument().writeRegister(504, DEFAULT_AMBIENT_TEMP_VALUE);	
    	}
        
        
    }
    
    @Test
    public void TestTickControllerCommands() throws IOException, ParseException
    {
    	setupNextPanelController();
    	nextPanelController.init(path, JSONIdentificationFilename, JSONConfigurationFilename);

    	// NB : due to ModSim limitation, all heater modules share same registers values, so only last written value counts.
    	// So I need to exclude last module. This test is compatible with GEN10 simulator, soon to be introduced. 
    	nextPanel.getNextHeater(4).stateful.config.userExcluded = true;
    	presetUnmuteAndRoburOEMCode();

    	nextPanel.setHostConfig.putIfEmpty(JSONUtil.loadJSON(path, "echoconfig").getJSONObject("config"));	// needed for tick.op wait for confserver
    	
    	// preset zone commands for first zone, set by GUI
    	nextPanel.stateful.getZone(1).config.onSwitch = true;
    	nextPanel.stateful.getZone(1).config.heatingMode = HeatingModeEnum.ON_OFF;
    	nextPanel.stateful.getZone(1).config.heatingDiscretePowerLevel = HeatingDiscretePowerLevel.LEVEL_2;
    	nextPanel.stateful.getZone(1).config.serviceMode = ServiceModeEnum.HEATING;
    	nextPanel.stateful.getZone(1).config.defaultSetpoint = 230; 	// i.e. 23,0°C
    	nextPanel.stateful.getZone(1).config.heatingTimerEnable = false;
    	nextPanel.stateful.getZone(1).config.destratification = false;
    	nextPanel.stateful.getZone(1).config.differential = 30;
    	
    	// preset ambient temperature in all heaters to check mean temperature computation
    	for(int i=0; i<nextPanel.heaters.length; ++i)
    	{
    		nextPanel.heaters[i].modbus.getModbusInstrument().writeRegister(500, 2);
    		nextPanel.heaters[i].modbus.getModbusInstrument().writeRegister(504, 180);	// i.e. 18°C
    	}
    	
    	nextPanelController.tick.op();

    	// loop on first zone heaters
    	for(int modId : nextPanel.stateful.getZone(1).config.heatersIds)
    	{
			assertThat(nextPanel.getNextHeater(modId).modbus.getModbusInstrument().readRegisters(MODBUS_COMMAND_OPERATION_REQUEST_ADDR, 1)[0], equalTo(2));	// i.e. ON discrete power level
			assertThat(nextPanel.getNextHeater(modId).modbus.getModbusInstrument().readRegisters(MODBUS_COMMAND_AMBIENT_SETPOINT_ADDR, 1)[0], equalTo(230));
			assertThat(nextPanel.getNextHeater(modId).modbus.getModbusInstrument().readRegisters(MODBUS_COMMAND_DISCRETE_POWER_LEVEL_ADDR, 1)[0], equalTo(2));
			assertThat(nextPanel.getNextHeater(modId).modbus.getModbusInstrument().readRegisters(MODBUS_COMMAND_AMBIENT_TEMP_ADDR, 1)[0], equalTo(180));
			
			assertThat(nextPanel.getNextHeater(modId).modbus.getModbusInstrument().readRegisters(MODBUS_COMMAND_DESTRATIFICATION_ADDR, 1)[0], equalTo(0));
			assertThat(nextPanel.getNextHeater(modId).modbus.getModbusInstrument().readRegisters(MODBUS_COMMAND_DIFFERENTIAL_ADDR, 1)[0], equalTo(30));
    	}
    	
    	// now change heating mode and setpoint
    	nextPanel.stateful.getZone(1).config.heatingMode = HeatingModeEnum.MODULATION;
    	nextPanel.stateful.getZone(1).config.defaultSetpoint = 250; 	// i.e. 25,0°C

    	nextPanelController.tick.op();

    	// loop on first zone heaters
    	for(int modId : nextPanel.stateful.getZone(1).config.heatersIds)
    	{
			assertThat(nextPanel.getNextHeater(modId).modbus.getModbusInstrument().readRegisters(MODBUS_COMMAND_OPERATION_REQUEST_ADDR, 1)[0], equalTo(3));	// i.e. ON modulation
			assertThat(nextPanel.getNextHeater(modId).modbus.getModbusInstrument().readRegisters(MODBUS_COMMAND_AMBIENT_SETPOINT_ADDR, 1)[0], equalTo(250));
			//assertThat(nextPanel.getNextHeater(modId).modbus.getModbusInstrument().readRegisters(MODBUS_COMMAND_DISCRETE_POWER_LEVEL_ADDR, 1)[0], equalTo(2)); don't care
			assertThat(nextPanel.getNextHeater(modId).modbus.getModbusInstrument().readRegisters(MODBUS_COMMAND_AMBIENT_TEMP_ADDR, 1)[0], equalTo(180));
			
			assertThat(nextPanel.getNextHeater(modId).modbus.getModbusInstrument().readRegisters(MODBUS_COMMAND_DESTRATIFICATION_ADDR, 1)[0], equalTo(0));
			assertThat(nextPanel.getNextHeater(modId).modbus.getModbusInstrument().readRegisters(MODBUS_COMMAND_DIFFERENTIAL_ADDR, 1)[0], equalTo(30));
    		
    	}
    	
        // recover default ambient temp
    	for(int i=0; i<nextPanel.heaters.length; ++i)
    	{
    		nextPanel.heaters[i].modbus.getModbusInstrument().writeRegister(500, 2);
    		nextPanel.heaters[i].modbus.getModbusInstrument().writeRegister(504, DEFAULT_AMBIENT_TEMP_VALUE);	
    	}
    	
    }
    
	
    @Test
    public void TestTickControllerLoop() throws IOException, ParseException
    {
    	setupNextPanelController();
    	
    	final int nTicks = 24;
    	final int period = 5; // NOTE: standard period is 40 sec.
    	
    	nextPanelController.init(path, JSONIdentificationFilename, JSONConfigurationFilename);

    	presetUnmuteAndRoburOEMCode();

    	nextPanel.setHostConfig.putIfEmpty(JSONUtil.loadJSON(path, "echoconfig").getJSONObject("config"));	// needed for tick.op wait for confserver
    	
    	// preset setpoint value
    	for(int i=0; i<nextPanel.heaters.length; ++i)
    	{
    		nextPanel.heaters[i].modbus.getModbusInstrument().writeRegister(500, 9);
    		nextPanel.heaters[i].modbus.getModbusInstrument().writeRegister(504, ModbusInstrument.NO_VALUE_SIGNED);	
    	}
    	
    	Thread stopThread = new Thread(new Runnable() 
		{    		
            public void run() 
            {
            	try 
            	{
    				Thread.sleep(nTicks * period * 1000);
    				
    				nextPanelController.stop();
    				
    			} 
            	catch (InterruptedException e) 
            	{
    				e.printStackTrace();
    			}
            }
		});
    	stopThread.start();
    	
    	
    	nextPanelController.startControlLoop(period, Timer.timeStringToDate("00:30"));
            	
    	    	
    	// check analogs
    	// current setpoint shall be NO_VALUE when module is turned off. 
    	assertThat(nextPanel.heaters[0].stateful.analogs[AnalogIndex.SETPOINT_TEMP.ordinal()],
    					equalTo(LogConsts.NO_VALUE_STRING));
    	   	
    	
    	// initially used to generate reference JSON:
//    	JSONTestUtil.saveJSON("identification_loop", jPublisher.getLast("np_identification"));
    	
        assertThat(
        		nullifyNextPanelDontCares((jPublisher.getLast("np_identification"))),
                sameJSONObjectAs(nullifyNextPanelDontCares(JSONTestUtil.loadJSONSubDir("NextPanel", JSONIdentificationFilename))));   	
    	    	
    }

    @Test
    public void TestSplit() throws IOException, ParseException
    {
    	
    	debugLog = new DebugLog(TestUtil.tmpPath, "npctest", true);
		
		SerialPurejavacomm serial = new SerialPurejavacomm(Settings.getPort());		
		
		AllByTagJSONLogPublisher jPublisherAlways    = new AllByTagJSONLogPublisher();
		AllByTagJSONLogPublisher jPublisherOnlySplit = new AllByTagJSONLogPublisher(); 
		JSONPublisherDisableableDecorator disableableJPublisherOnlySplit = new JSONPublisherDisableableDecorator(jPublisherOnlySplit);
		
		JSONPublisherMulti.Builder jsonSamplePublisherBuilder  = new JSONPublisherMulti.Builder();
		jsonSamplePublisherBuilder.addPublisher(jPublisherAlways);
		jsonSamplePublisherBuilder.addPublisher(disableableJPublisherOnlySplit);
		JSONPublisherMulti jsonSamplePublishers = jsonSamplePublisherBuilder.buildIfNeeded();
		
		String tProbeFilename = path + "/ntc_temperature_value";
		nextPanel = new NextPanel("1.0", serial, jsonSamplePublishers, disableableJPublisherOnlySplit, tProbeFilename, debugLog);				    				
		nextPanelController = new NextPanelController(nextPanel, debugLog);
		
		nextPanelController.init(path, JSONIdentificationFilename, JSONConfigurationFilename);

    	nextPanel.setHostConfig.putIfEmpty(JSONUtil.loadJSON(path, "echoconfig").getJSONObject("config"));	// needed for tick.op wait for confserver

        // preset unit test default ambient temp
    	for(int i=0; i<nextPanel.heaters.length; ++i)
    	{
    		nextPanel.heaters[i].modbus.getModbusInstrument().writeRegister(500, 2);
    		nextPanel.heaters[i].modbus.getModbusInstrument().writeRegister(504, DEFAULT_AMBIENT_TEMP_VALUE);	
    	}
    	
		// preset statistics registers
		nextPanel.heaters[0].modbus.getModbusInstrument().writeRegister(1200, 0);
		nextPanel.heaters[0].modbus.getModbusInstrument().writeRegister(1201, 1000);
		nextPanel.heaters[0].modbus.getModbusInstrument().writeRegister(1202, 0);
		nextPanel.heaters[0].modbus.getModbusInstrument().writeRegister(1203, 481);
		nextPanel.heaters[0].modbus.getModbusInstrument().writeRegister(1204, 0);
		nextPanel.heaters[0].modbus.getModbusInstrument().writeRegister(1205, 3);
		nextPanel.heaters[0].modbus.getModbusInstrument().writeRegister(1206, 0);
		
		nextPanel.heaters[1].modbus.getModbusInstrument().writeRegister(1200, 0);
		nextPanel.heaters[1].modbus.getModbusInstrument().writeRegister(1201, 2000);
		nextPanel.heaters[1].modbus.getModbusInstrument().writeRegister(1202, 0);
		nextPanel.heaters[1].modbus.getModbusInstrument().writeRegister(1203, 30);
		nextPanel.heaters[1].modbus.getModbusInstrument().writeRegister(1204, 0);
		nextPanel.heaters[1].modbus.getModbusInstrument().writeRegister(1205, 5);
		nextPanel.heaters[1].modbus.getModbusInstrument().writeRegister(1206, 1);
		
		nextPanel.heaters[2].modbus.getModbusInstrument().writeRegister(1200, 0);
		nextPanel.heaters[2].modbus.getModbusInstrument().writeRegister(1201, 4000);
		nextPanel.heaters[2].modbus.getModbusInstrument().writeRegister(1202, 0);
		nextPanel.heaters[2].modbus.getModbusInstrument().writeRegister(1203, 40);
		nextPanel.heaters[2].modbus.getModbusInstrument().writeRegister(1204, 0);
		nextPanel.heaters[2].modbus.getModbusInstrument().writeRegister(1205, 4);
		nextPanel.heaters[2].modbus.getModbusInstrument().writeRegister(1206, 0);

		// preset Parameter P40 (modbus address), P41, P82 in all heaters, to bypass Faker 1.4 bug
    	/*
		for(int i=0; i<nextPanel.heaters.length; ++i)
    	{
    		nextPanel.heaters[i].modbus.getModbusInstrument().writeRegister(204, 40);
    		nextPanel.heaters[i].modbus.getModbusInstrument().writeRegister(212, nextPanel.heaters[i].getId());	

    		nextPanel.heaters[i].modbus.getModbusInstrument().writeRegister(204, 41);
    		nextPanel.heaters[i].modbus.getModbusInstrument().writeRegister(212, 60);	

    		nextPanel.heaters[i].modbus.getModbusInstrument().writeRegister(204, 82);
    		nextPanel.heaters[i].modbus.getModbusInstrument().writeRegister(212, 8);	
    	}
		*/
		
    	final int period = 40; // NOTE: standard period is 40 sec.
    	final int waitMinutes = 2;	// was 3
    	    	
    	Thread stopThread = new Thread(new Runnable() 
		{    		
            public void run() 
            {
            	try 
            	{
    				Thread.sleep( ((waitMinutes * 60) + (period) + 1) * 1000);
    				
    				nextPanelController.stop();
    				
    			} 
            	catch (InterruptedException e) 
            	{
    				e.printStackTrace();
    			}
            }
		});
    	stopThread.start();
    	
    	
    	nextPanelController.startControlLoop(period, new Date(System.currentTimeMillis() + (waitMinutes * 60 * 1000)));
    	
    	assertTrue(jPublisherAlways.getCount("np_split") > 1 );
    	assertEquals(jPublisherAlways.getCount("np_split"), jPublisherAlways.getCount("np_sample"));
    	
    	assertEquals(jPublisherOnlySplit.getCount("np_split"), 1);
    	assertEquals(jPublisherOnlySplit.getCount("np_sample"), jPublisherAlways.getCount("np_sample") );
    	   	
    	
    	// initially used to generate reference JSON:
    	//JSONTestUtil.saveJSON("tmpAlwaysSplit", jPublisherAlways.get("np_split", 0));
    	//JSONTestUtil.saveJSON("tmpOnlySplit",   jPublisherOnlySplit.get("np_split", 0));
    	
    	JSONObject fullSplit = JSONTestUtil.loadJSONWithNullDontCaresSubDir("NextPanel", JSONSplitAtSplitTimeFilename);
    	JSONObject noParamsSplit = JSONTestUtil.loadJSONWithNullDontCaresSubDir("NextPanel", JSONSplitAtSplitTimeFilename);
    	noParamsSplit.remove("params_samples");
    	
        assertThat(
        		nullifyNextPanelDontCares(JSONTestUtil.nullifyDontCares(jPublisherOnlySplit.get("np_split", 0))),
                sameJSONObjectAs(nullifyNextPanelDontCares(fullSplit)));
        
        assertThat(
        		nullifyNextPanelDontCares(JSONTestUtil.nullifyDontCares(jPublisherAlways.get("np_split", 0))),
        		sameJSONObjectAs(nullifyNextPanelDontCares(noParamsSplit)));
        
        assertThat(
        		nullifyNextPanelDontCares(JSONTestUtil.nullifyDontCares(jPublisherAlways.get("np_split", jPublisherAlways.getCount("np_split") - 1))),
        		sameJSONObjectAs(nullifyNextPanelDontCares(fullSplit)));

        
		// at the end recover default statistics registers values
		nextPanel.heaters[0].modbus.getModbusInstrument().writeRegister(1200, 0);
		nextPanel.heaters[0].modbus.getModbusInstrument().writeRegister(1201, 7200);
		nextPanel.heaters[0].modbus.getModbusInstrument().writeRegister(1202, 0);
		nextPanel.heaters[0].modbus.getModbusInstrument().writeRegister(1203, 3600);
		nextPanel.heaters[0].modbus.getModbusInstrument().writeRegister(1204, 0);
		nextPanel.heaters[0].modbus.getModbusInstrument().writeRegister(1205, 10);
		nextPanel.heaters[0].modbus.getModbusInstrument().writeRegister(1206, 2);
		
		nextPanel.heaters[1].modbus.getModbusInstrument().writeRegister(1200, 0);
		nextPanel.heaters[1].modbus.getModbusInstrument().writeRegister(1201, 7200);
		nextPanel.heaters[1].modbus.getModbusInstrument().writeRegister(1202, 0);
		nextPanel.heaters[1].modbus.getModbusInstrument().writeRegister(1203, 3600);
		nextPanel.heaters[1].modbus.getModbusInstrument().writeRegister(1204, 0);
		nextPanel.heaters[1].modbus.getModbusInstrument().writeRegister(1205, 10);
		nextPanel.heaters[1].modbus.getModbusInstrument().writeRegister(1206, 2);
		
		nextPanel.heaters[2].modbus.getModbusInstrument().writeRegister(1200, 0);
		nextPanel.heaters[2].modbus.getModbusInstrument().writeRegister(1201, 7200);
		nextPanel.heaters[2].modbus.getModbusInstrument().writeRegister(1202, 0);
		nextPanel.heaters[2].modbus.getModbusInstrument().writeRegister(1203, 3600);
		nextPanel.heaters[2].modbus.getModbusInstrument().writeRegister(1204, 0);
		nextPanel.heaters[2].modbus.getModbusInstrument().writeRegister(1205, 10);
		nextPanel.heaters[2].modbus.getModbusInstrument().writeRegister(1206, 2);
    }

    
    @SuppressWarnings("deprecation")
	@Test
    public void TestHeatingTimer()
    {
    	int i;
    	
    	// NB controller timer needs NextPanelConfig.Zone.HeatingTimer
    	StatefulNextZone.ZoneConfig.HeatingTimer configTimer = new StatefulNextZone.ZoneConfig.HeatingTimer();
    	StatefulNextZone.HeatingTimer timer = new StatefulNextZone.HeatingTimer(configTimer);
    	
    	// set time slots (default OFF)
		configTimer.setTimeSlot(StatefulNextZone.ZoneConfig.HeatingTimer.DayOfTheWeek.sunday, 0, StatefulNextZone.ZoneConfig.HeatingTimer.Level.COMFORT);	// first index

		for(i=10; i<20; ++i)
    		configTimer.setTimeSlot(StatefulNextZone.ZoneConfig.HeatingTimer.DayOfTheWeek.sunday, i, StatefulNextZone.ZoneConfig.HeatingTimer.Level.ANTIFREEZE);
    	
    	for(i=30; i<35; ++i)
    		configTimer.setTimeSlot(StatefulNextZone.ZoneConfig.HeatingTimer.DayOfTheWeek.sunday, i, StatefulNextZone.ZoneConfig.HeatingTimer.Level.REDUCED);

		configTimer.setTimeSlot(StatefulNextZone.ZoneConfig.HeatingTimer.DayOfTheWeek.sunday, 47, StatefulNextZone.ZoneConfig.HeatingTimer.Level.REDUCED);	// last index
    	
    	// test without timeslot extension
		Date date1 = new Date(2023-1900, 1-1, 29, 2, 0, 0);	// yy-1900 mm-1 dd hh mm ss - i.e. hhIndex 4
		assertThat(timer.getPresentLevel(date1), equalTo(StatefulNextZone.ZoneConfig.HeatingTimer.Level.OFF));
		Date date2 = new Date(2023-1900, 1-1, 29, 6, 0, 0);	// i.e. hhIndex 12
		assertThat(timer.getPresentLevel(date2), equalTo(StatefulNextZone.ZoneConfig.HeatingTimer.Level.ANTIFREEZE));
		Date date3 = new Date(2023-1900, 1-1, 29, 17, 0, 0);// i.e. hhIndex 34	
		assertThat(timer.getPresentLevel(date3), equalTo(StatefulNextZone.ZoneConfig.HeatingTimer.Level.REDUCED));
		Date date4 = new Date(2023-1900, 1-1, 29, 20, 0, 0);// i.e. hhIndex 40	
		assertThat(timer.getPresentLevel(date4), equalTo(StatefulNextZone.ZoneConfig.HeatingTimer.Level.OFF));
		
		// test extend timeslot
		timer.setExtendTimeSlot(5, date1);	// extend 5 step the active timeslot @date1, i.e. 5 from index 9 (end of present timeslot), so up to index 14
		// check timer extended in date2, i.e. index 12: should keep extension
		assertThat(timer.getPresentLevel(date2), equalTo(StatefulNextZone.ZoneConfig.HeatingTimer.Level.OFF));	// should be OFF instead of ANTIFREEZE
		assertThat(timer.extendLength, equalTo(5));
		assertThat(timer.extendIndex, equalTo(10));
		// check timer extend in date2_5, i.e. 16: should expire
		Date date2_5 = new Date(2023-1900, 1-1, 29, 8, 0, 0);	
		assertThat(timer.getPresentLevel(date2_5), equalTo(StatefulNextZone.ZoneConfig.HeatingTimer.Level.ANTIFREEZE));
		// test extension expired
		assertThat(timer.extendLength, equalTo(0));
		// implicit test that extension expired
		assertThat(timer.getPresentLevel(date3), equalTo(StatefulNextZone.ZoneConfig.HeatingTimer.Level.REDUCED));
		assertThat(timer.getPresentLevel(date4), equalTo(StatefulNextZone.ZoneConfig.HeatingTimer.Level.OFF));
		
		// test set extended time to postpone ANTIFREEZE time from index 10 to index 12
		timer.setExtendTimeSlot(2, date1);	// extend 2 step the active timeslot @date1, i.e. 2 from index 9 (end of present timeslot), so up to index 11
		// check @index 10 is still OFF
		Date date2_1 = new Date(2023-1900, 1-1, 29, 5, 0, 0);	// index 10
		assertThat(timer.getPresentLevel(date2_1), equalTo(StatefulNextZone.ZoneConfig.HeatingTimer.Level.OFF));	// should be OFF instead of ANTIFREEZE
		assertThat(timer.extendLength, equalTo(2));
		assertThat(timer.extendIndex, equalTo(10));
		// check @index 11 is still OFF
		Date date2_2 = new Date(2023-1900, 1-1, 29, 5, 30, 0);	// index 11
		assertThat(timer.getPresentLevel(date2_2), equalTo(StatefulNextZone.ZoneConfig.HeatingTimer.Level.OFF));	// should be OFF instead of ANTIFREEZE
		assertThat(timer.extendLength, equalTo(2));
		assertThat(timer.extendIndex, equalTo(10));
		// check @index 12 change to ANTIFREEZE
		Date date2_3 = new Date(2023-1900, 1-1, 29, 6, 00, 0);	// index 12
		assertThat(timer.getPresentLevel(date2_3), equalTo(StatefulNextZone.ZoneConfig.HeatingTimer.Level.ANTIFREEZE));	
		assertThat(timer.extendLength, equalTo(0));	// should expire
		assertThat(timer.extendIndex, equalTo(0));	// "
		
		// test extend last index
		timer.setExtendTimeSlot(1, date4);	// extend 1 step the active timeslot @date4, i.e. 1 from index 46 (end of present timeslot), so index 47
		Date date4_1 = new Date(2023-1900, 1-1, 29, 23, 30, 0);	// index 47
		assertThat(timer.getPresentLevel(date4_1), equalTo(StatefulNextZone.ZoneConfig.HeatingTimer.Level.OFF));	// should be OFF instead of REDUCED
		assertThat(timer.extendLength, equalTo(1));
		assertThat(timer.extendIndex, equalTo(47));
		// test day transition
		Date date0 = new Date(2023-1900, 1-1, 29, 0, 0, 0);	// index 0
		assertThat(timer.getPresentLevel(date0), equalTo(StatefulNextZone.ZoneConfig.HeatingTimer.Level.COMFORT));
		assertThat(timer.extendLength, equalTo(0));
		assertThat(timer.extendIndex, equalTo(0));
		
		// test out of range extension
		timer.setExtendTimeSlot(2, date4);	// extend 2 step the active timeslot @date4, i.e. 1 from index 46 (end of present timeslot), so index 48 (not valid)
		assertThat(timer.getPresentLevel(date4_1), equalTo(StatefulNextZone.ZoneConfig.HeatingTimer.Level.REDUCED));	
		assertThat(timer.extendLength, equalTo(0));
		assertThat(timer.extendIndex, equalTo(0));
		timer.setExtendTimeSlot(10, date4);	// extend 10 step the active timeslot @date4, i.e. 1 from index 46 (end of present timeslot), so index 56 (not valid)
		assertThat(timer.getPresentLevel(date4_1), equalTo(StatefulNextZone.ZoneConfig.HeatingTimer.Level.REDUCED));	
		assertThat(timer.extendLength, equalTo(0));
		assertThat(timer.extendIndex, equalTo(0));
		
		// ---
    }
    
    // TEST EVENT MANAGER
	@Test
	public void TestTickControllerEventManager() throws IOException
	{
    	setupNextPanelController();
    	
    	nextPanelController.init(path, JSONIdentificationFilename, JSONConfigurationFilename);

    	// include heater not bound with any zone
    	nextPanel.getNextHeater(4).stateful.config.userExcluded = false;	// set different from loaded configuration.json
    	presetUnmuteAndRoburOEMCode();

    	nextPanel.setHostConfig.putIfEmpty(JSONUtil.loadJSON(path, "echoconfig").getJSONObject("config"));	// needed for tick.op wait for confserver
    	
    	nextPanelController.tick.op();
    	
    	assertThat(nextPanel.heaters.length, equalTo(3));	// according to reference identification

    	// only for debug
    	//System.out.println(jPublisher.getLast("np_identification"));
    	//System.out.println(jPublisher.getLast("np_sample"));
    	
    	for(int i=0; i<nextPanel.heaters.length; ++i)
    	{
	    	// check online & compatibility
	    	assertFalse(nextPanel.heaters[i].stateful.offline);
	    	assertTrue(nextPanel.heaters[i].stateful.compatible);
    	}

    	// CHECK EVENT MANAGER: should be empty
    	// TODO for each heater

    	for(int i=0; i<nextPanel.heaters.length; ++i)
    	{
    		int mbaddr = nextPanel.heaters[i].stateful.modbusAddress;
	    	assertFalse(nextPanel.eventManager.containsModuleWarningEvents(mbaddr));
	    	assertFalse(nextPanel.eventManager.containsModuleErrorEvents(mbaddr));
    	}    	
    	
    	// now simulate module 1 offline by writing to 40809 value 0x5A5A
    	nextPanel.getNextHeater(1).modbus.getModbusInstrument().writeRegister(808, 0x5A5A, 6);
    	
    	nextPanelController.tick.op();
    	//nextPanelController.tick.op();	// PROVVI

    	assertTrue(nextPanel.getNextHeater(1).stateful.offline);
    	assertFalse(nextPanel.getNextHeater(2).stateful.offline);
    	assertFalse(nextPanel.getNextHeater(4).stateful.offline);

    	assertFalse(nextPanel.eventManager.containsModuleWarningEvents(1));
    	assertTrue(nextPanel.eventManager.containsModuleErrorEvents(1));	// E1001
    	assertFalse(nextPanel.eventManager.containsModuleWarningEvents(2));
    	assertFalse(nextPanel.eventManager.containsModuleErrorEvents(2));
    	assertFalse(nextPanel.eventManager.containsModuleWarningEvents(4));
    	assertFalse(nextPanel.eventManager.containsModuleErrorEvents(4));
    	
    	// check E1001 presence
    	// DEBUG PURPOSE
    	// System.out.println(nextPanel.eventManager.printEventList());
    	
    	assertTrue(nextPanel.eventManager.containsEvent( new NextPanel.Event(NextPanel.Event.Category.ERROR, 1001, 1)));
    	
    	// recover module 1 online by writing to 40809 value 0xA5A5
    	nextPanel.getNextHeater(1).modbus.getModbusInstrument().writeRegister(808, 0xA5A5, 6);

    	nextPanelController.tick.op();

    	assertFalse(nextPanel.getNextHeater(1).stateful.offline);
    	assertFalse(nextPanel.getNextHeater(2).stateful.offline);
    	assertFalse(nextPanel.getNextHeater(4).stateful.offline);

    	assertFalse(nextPanel.eventManager.containsEvent( new NextPanel.Event(NextPanel.Event.Category.ERROR, 1001, 1)));

    	// now set all modules offline
    	
    	
    	
	}

	public void deleteTemperatureProbeFile()
	{
		File probeFile = new File(tProbeFilename);
		probeFile.delete();
	}
	
	public void writeTemperatureProbe(String value)
	{
		deleteTemperatureProbeFile();
		File probeFile = new File(tProbeFilename);
		
		try
		{
			FileWriter fw = new FileWriter(probeFile, false);
			fw.write(value);
			fw.close();
			
		} catch(IOException e)
		{
			e.printStackTrace();
		}
	}
	
	
    @Test
    public void TestTickControllerTemperatureProbe() throws IOException
    {
    	setupNextPanelController();
    	
    	nextPanelController.init(path, JSONIdentificationFilename, JSONConfigurationProbeEnabledFilename);
    	// NB all other unit test uses JSONConfigurationFilename with temperature probe disabled, so value should be ABSENT, i.e. "." in sample.json.
    	// this unit test enables temperature probe to check probe management.
    	
    	presetUnmuteAndRoburOEMCode();

    	nextPanel.setHostConfig.putIfEmpty(JSONUtil.loadJSON(path, "echoconfig").getJSONObject("config"));	// needed for tick.op wait for confserver
    	
    	// preset ambient temperature to check mean temperature computation
    	for(int i=0; i<nextPanel.heaters.length; ++i)
    	{
    		nextPanel.heaters[i].modbus.getModbusInstrument().writeRegister(500, 2);
    		nextPanel.heaters[i].modbus.getModbusInstrument().writeRegister(504, ModbusInstrument.NO_VALUE_SIGNED);	
    	}
    	// preset setpoint value
    	for(int i=0; i<nextPanel.heaters.length; ++i)
    	{
    		nextPanel.heaters[i].modbus.getModbusInstrument().writeRegister(500, 9);
    		nextPanel.heaters[i].modbus.getModbusInstrument().writeRegister(504, ModbusInstrument.NO_VALUE_SIGNED);	
    	}
    	// include heater not bound with any zone
    	nextPanel.getNextHeater(4).stateful.config.userExcluded = false;	// set different from loaded configuration.json

    	// set NextPanel temperature probe value in file read by controller
    	writeTemperatureProbe("238");
    	
    	nextPanelController.tick.op();
    	
    	assertThat(nextPanel.heaters.length, equalTo(3));	// according to reference identification

    	for(int i=0; i<nextPanel.heaters.length; ++i)
    	{
	    	// check online & compatibility
	    	assertFalse(nextPanel.heaters[i].stateful.offline);
	    	assertTrue(nextPanel.heaters[i].stateful.compatible);
	    	
	    	// check analogs
	    	// current setpoint shall be ABSENT_VALUE (i.e. not provided) when module is turned off. 
	    	// here we check NO_VALUE because of limitation of preset modbus slave simulator ****
	    	assertThat(nextPanel.heaters[i].stateful.analogs[AnalogIndex.SETPOINT_TEMP.ordinal()],
	    					equalTo(LogConsts.NO_VALUE_STRING));
    	}    	
    	// check read nextpanel temperature probe
    	assertThat(nextPanel.stateful.ambientNPProbeTemp, equalTo(238));
    	// check mean ambient temperature (first zone) - shall use only NextPanel temperature probe
    	assertThat(nextPanel.stateful.getZone(1).meanAmbientTemp, equalTo(238));
    	// check JSON sample
        assertThat(
        		nullifyNextPanelDontCares((jPublisher.getLast("np_sample"))),
                sameJSONObjectAs(nullifyNextPanelDontCares(JSONTestUtil.loadJSONSubDir("NextPanel", JSONSampleTemperatureProbeFilename))));   	

    	
    	// test another correct value
    	writeTemperatureProbe("195");
    	nextPanelController.tick.op();
    	// check read nextpanel temperature probe
    	assertThat(nextPanel.stateful.ambientNPProbeTemp, equalTo(195));
    	// check mean ambient temperature (first zone) - shall use only NextPanel temperature probe
    	assertThat(nextPanel.stateful.getZone(1).meanAmbientTemp, equalTo(195));
    	
    	// test with offset
    	nextPanel.stateful.config.temperatureProbeOffset = 30;
    	nextPanelController.tick.op();
    	// check read nextpanel temperature probe
    	assertThat(nextPanel.stateful.ambientNPProbeTemp, equalTo(225));
    	// check mean ambient temperature (first zone) - shall use only NextPanel temperature probe
    	assertThat(nextPanel.stateful.getZone(1).meanAmbientTemp, equalTo(225));
    	
    	// test excluded value 32767 NO_VALUE , i.e. fault probe, with offset != 0
    	writeTemperatureProbe("32767");
    	nextPanelController.tick.op();
    	// check read nextpanel temperature probe
    	assertThat(nextPanel.stateful.ambientNPProbeTemp, equalTo(32767));	// i.e. NO_VALUE_SIGNED
    	// check mean ambient temperature (first zone) - shall use only NextPanel temperature probe
    	assertThat(nextPanel.stateful.getZone(1).meanAmbientTemp, equalTo(32767));
    	// check JSON sample
        assertThat(
        		nullifyNextPanelDontCares((jPublisher.getLast("np_sample"))),
                sameJSONObjectAs(nullifyNextPanelDontCares(JSONTestUtil.loadJSONSubDir("NextPanel", JSONSampleFaultyTemperatureProbeFilename))));   	
    	
    	
    	// test simile ma con offset a zero
    	nextPanel.stateful.config.temperatureProbeOffset = 0;
    	nextPanelController.tick.op();
    	assertThat(nextPanel.stateful.ambientNPProbeTemp, equalTo(32767));
    	// check mean ambient temperature (first zone) - shall use only NextPanel temperature probe
    	assertThat(nextPanel.stateful.getZone(1).meanAmbientTemp, equalTo(32767));
    	// check JSON sample
        assertThat(
        		nullifyNextPanelDontCares((jPublisher.getLast("np_sample"))),
                sameJSONObjectAs(nullifyNextPanelDontCares(JSONTestUtil.loadJSONSubDir("NextPanel", JSONSampleFaultyTemperatureProbeFilename))));   	
    	
        
    	// test missing panel probe file (ABSENT) with heaters temperature probes OK
        deleteTemperatureProbeFile();
    	// preset ambient temperature 20.0
    	for(int i=0; i<nextPanel.heaters.length; ++i)
    	{
    		nextPanel.heaters[i].modbus.getModbusInstrument().writeRegister(500, 2);
    		nextPanel.heaters[i].modbus.getModbusInstrument().writeRegister(504, 200);	
    	}
    	nextPanelController.tick.op();
    	// check read nextpanel temperature probe
    	assertThat(nextPanel.stateful.ambientNPProbeTemp, equalTo(32766));	// i.e. ABSENT_VALUE_SIGNED
    	// check mean ambient temperature (first zone) - shall use only NextPanel temperature probe
    	assertThat(nextPanel.stateful.getZone(1).meanAmbientTemp, equalTo(200));	
    	assertThat(nextPanel.stateful.getZone(2).meanAmbientTemp, equalTo(32766)); // i.e. ABSENT 	
    	// check JSON sample: panel temperature probe should be ABSENT, i.e. "."
        assertThat(
        		nullifyNextPanelDontCares((jPublisher.getLast("np_sample"))),
                sameJSONObjectAs(nullifyNextPanelDontCares(JSONTestUtil.loadJSONSubDir("NextPanel", JSONSampleFilename))));   	
        
        
    	// test empty probe file with heaters temperature probes OK (similar to missing file)
    	writeTemperatureProbe("");
    	// preset ambient temperature 20.0
    	for(int i=0; i<nextPanel.heaters.length; ++i)
    	{
    		nextPanel.heaters[i].modbus.getModbusInstrument().writeRegister(500, 2);
    		nextPanel.heaters[i].modbus.getModbusInstrument().writeRegister(504, 200);	
    	}
    	nextPanelController.tick.op();
    	// check read nextpanel temperature probe
    	assertThat(nextPanel.stateful.ambientNPProbeTemp, equalTo(32766));	// i.e. ABSENT_VALUE_SIGNED
    	// check mean ambient temperature (first zone) - shall use only NextPanel temperature probe
    	assertThat(nextPanel.stateful.getZone(1).meanAmbientTemp, equalTo(200));	
    	assertThat(nextPanel.stateful.getZone(2).meanAmbientTemp, equalTo(32766)); // i.e. ABSENT 	
    	// check JSON sample: panel temperature probe should be ABSENT, i.e. "."
        assertThat(
        		nullifyNextPanelDontCares((jPublisher.getLast("np_sample"))),
                sameJSONObjectAs(nullifyNextPanelDontCares(JSONTestUtil.loadJSONSubDir("NextPanel", JSONSampleFilename))));   	
        
        
        // test wrong numeric value in probe file with faulty heaters temperature probes
    	writeTemperatureProbe("ABCZ");
    	// preset ambient temperature 20.0
    	for(int i=0; i<nextPanel.heaters.length; ++i)
    	{
    		nextPanel.heaters[i].modbus.getModbusInstrument().writeRegister(500, 2);
    		nextPanel.heaters[i].modbus.getModbusInstrument().writeRegister(504, ModbusInstrument.NO_VALUE_SIGNED);	
    	}
    	nextPanelController.tick.op();
    	// check read nextpanel temperature probe
    	assertThat(nextPanel.stateful.ambientNPProbeTemp, equalTo(32767));	// i.e. NO_VALUE_SIGNED (fault)
    	// check mean ambient temperature (first zone) - shall use only NextPanel temperature probe
    	assertThat(nextPanel.stateful.getZone(1).meanAmbientTemp, equalTo(32767));	
    	assertThat(nextPanel.stateful.getZone(2).meanAmbientTemp, equalTo(32767)); // i.e. NO_VALUE 	
    	// check JSON sample: panel temperature probe should be NO_VALUE, i.e. "-"
        assertThat(
        		nullifyNextPanelDontCares((jPublisher.getLast("np_sample"))),
                sameJSONObjectAs(nullifyNextPanelDontCares(JSONTestUtil.loadJSONSubDir("NextPanel", JSONSampleFaultyTemperatureProbeFilename))));   	
        
    	

    	
	
    }
    
    
}
