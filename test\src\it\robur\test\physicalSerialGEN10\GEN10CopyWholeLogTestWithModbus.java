package it.robur.test.physicalSerialGEN10;

import static org.junit.Assert.assertTrue;

import java.io.File;
import java.io.FilenameFilter;
import java.io.IOException;
import java.util.Arrays;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;

import it.robur.jserialtool.SerialPurejavacomm;
import it.robur.loggers.GEN10JSONLogCons;
import it.robur.loggers.GEN10LogCopy;
import it.robur.loggers.GEN10Logger.SingleSampleLogProducer;
import it.robur.modbus.ModbusGEN10;
import it.robur.test.multiplatform.util.JSONTestUtil;
import it.robur.test.multiplatform.util.TestUtil;

public class GEN10CopyWholeLogTestWithModbus 
{
	static final String parentPath = "test/res/wholelog".replace("/", File.separator);

	// WAS: for DDC:
//	private static JSONDDC jDdc;
//	private static DDCJSONLogCons jConsumer;
//	private static SimpleJSONLogPublisher jPublisher;
//	
//	private static MockDDCMetricsXMLLogFile xmlMetrics;
//	private static MockDDCEventsXMLLogFile xmlEvents;
//	private static DDCXMLLogCons xConsumer;
	
	private static ModbusGEN10 mGen10;
	private static SerialPurejavacomm serial;
	
//	private static boolean T32ApiLoaded = false; 
//	
//	private String metricsLogFilename;
//	private String eventLogFilename;
		
//	@AfterClass
//	public static void teardownTest() throws IOException
//	{		
//		if( T32ApiLoaded )
//			cT32Api.Exit();
//	}
	
	@Before
	public void setup() throws IOException
	{
		// Modbus
		mGen10 = null;
				
//    	// JSON
//		jGen10       = null;
//		jConsumer  = null;
//		jPublisher = null;
//		
//		// XML
//		xmlMetrics       = null;
//		xConsumer = null;
		
	}
	
	@After
	public void teardown() throws IOException
	{
		// Modbus
		if( mGen10 != null)
		{
			mGen10.close();			
			mGen10 = null;
		}
		
	}

	
	public void setupModbus() throws IOException
	{
		serial = new SerialPurejavacomm(Settings.getPort());
		mGen10 = new ModbusGEN10(serial, 1);		
		    	
	}
	
//	public void setupJSON() throws IOException
//	{
//		jGen10 = new JSONGEN10();
//		jPublisher = new SimpleJSONLogPublisher();
//		jConsumer = new GEN10JSONLogCons( jPublisher, false, true, null);
//		
//	}
//	
//	public void setupXML() throws IOException
//	{
//    	xmlMetrics = new MockGEN10MetricsXMLLogFile();
//    	xmlEvents = new MockGEN10EventsXMLLogFile();
//		xConsumer = new GEN10XMLLogCons(xmlMetrics, xmlEvents);
//		    	
//	}

    
    @Test
    public void testCopyWholeSingleLogFromModbusToJSON() throws IOException
    {   	
    	setupModbus();
    	
        mGen10.init();
    	
    	final JSONTestUtil.TestFilesJSONLogPublisher jPublisher = new JSONTestUtil.TestFilesJSONLogPublisher(TestUtil.tmpPath);
    	final GEN10JSONLogCons jConsumer = new GEN10JSONLogCons( jPublisher, false, null);
		
		mGen10.getIdentificationInfo();
		
		jConsumer.setAndConnect(Integer.toString(mGen10.getApplianceSerial()));
    	
    	GEN10LogCopy.copyWholeLog(mGen10, new SingleSampleLogProducer(mGen10), jConsumer);
    	
        mGen10.close();
        mGen10 = null;
    	    	
    	String NextG4Path = "NextG_sn21_2021.10.07/".replace("/", File.separator);    	

    	assertTrue(
    			JSONTestUtil.nearlySameAs(
    					JSONTestUtil.loadJSONWithNullDontCares(NextG4Path + "nextg_identif"),
    					JSONTestUtil.loadJSONWithNullDontCaresPrjDir(TestUtil.tmpPath, "nextg_identif")));    	

    	assertTrue(
    			JSONTestUtil.nearlySameAs(
    					JSONTestUtil.loadJSONWithNullDontCares(NextG4Path + "nextg_sample"),
    					JSONTestUtil.loadJSONWithNullDontCaresPrjDir(TestUtil.tmpPath, "nextg_sample")));

    	assertTrue(
    			JSONTestUtil.nearlySameAs(
    					JSONTestUtil.loadJSONWithNullDontCares(NextG4Path + "nextg_split"),
    					JSONTestUtil.loadJSONWithNullDontCaresPrjDir(TestUtil.tmpPath, "nextg_split")));

    	// TODO: how?
//    	assertTrue(
//    			JSONTestUtil.nearlySameAs(
//    					JSONTestUtil.loadJSON(NextG4Path + "nextg_events"),
//    					JSONTestUtil.loadJSON(outPath, getLastJSON(outPath, "events"))));   	
	
    }
    
    static protected String getLastJSON(final String path, final String prefix)
    {
    	File dir = new File(path);
    	
    	FilenameFilter filter = new FilenameFilter() 
    	{        		
			public boolean accept(File dir, String name) 
			{    				
				String lowercaseName = name.toLowerCase();
				
				if ( lowercaseName.toLowerCase().startsWith(prefix)
						&& lowercaseName.toLowerCase().endsWith(".json") )				
					return true;
					
				return false;
			}
		};
		
		File[] files = dir.listFiles(filter);
		
		if (files == null || files.length == 0)
			return null;
		else
		{
			Arrays.sort(files);
			return files[0].getName().replace(".json", "");
		}
		
    }

    
}
