package it.robur.jstcertif;

import java.awt.Cursor;
import java.awt.Toolkit;
import java.awt.datatransfer.StringSelection;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.WindowAdapter;
import java.awt.event.WindowEvent;
import java.io.File;
import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.net.InetAddress;
import java.util.Properties;
import java.util.Set;
import java.util.SortedMap;
import java.util.TreeMap;
import java.util.concurrent.ExecutionException;

import javax.swing.ImageIcon;
import javax.swing.JOptionPane;
import javax.swing.SwingUtilities;
import javax.swing.SwingWorker;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import it.robur.flashers.GEN10SerialFlasher;
import it.robur.flashers.GHP10SerialFlasher;
import it.robur.flashers.TerminalSerial;
import it.robur.interfaces.OpJSON;
import it.robur.interfaces.OpLong;
import it.robur.interfaces.OpStr;
import it.robur.interfaces.OpStrStr;
import it.robur.interfaces.OpStrStrMulti;
import it.robur.jserialtool.ApplicationPreference;
import it.robur.jserialtool.CCILogUIConsPC;
import it.robur.net.ConfServer;
import it.robur.jserialtool.DDCLogUIConsPC;
import it.robur.jserialtool.ErrorDialog;
import it.robur.jserialtool.GEN10LogUIConsPC;
import it.robur.jserialtool.GHP10LogUIConsPC;
import it.robur.jserialtool.LogConvResolverPC;
import it.robur.jserialtool.MainView;
import it.robur.jserialtool.Manifest;
import it.robur.jserialtool.ModbusView;
import it.robur.jserialtool.MqttClientPCBuilder;
import it.robur.jserialtool.SerialPurejavacomm;
import it.robur.jserialtool.SettingsModel;
import it.robur.jserialtool.SharerPC;
import it.robur.jserialtool.USRBridgePC;
import it.robur.loggers.CCIJSONLogCons;
import it.robur.loggers.CCILogger;
import it.robur.loggers.DDCJSONLogCons;
import it.robur.loggers.DDCJSONReceive;
import it.robur.loggers.DDCLogger;
import it.robur.loggers.DumpJSONLogPublisher;
import it.robur.loggers.GEN10JSONLogCons;
import it.robur.loggers.GEN10Logger;
import it.robur.loggers.GHP10JSONLogCons;
import it.robur.loggers.GHP10JSONReceive;
import it.robur.loggers.GHP10Logger;
import it.robur.loggers.JSONPublisherMulti;
import it.robur.loggers.JSONPublisherResponseDecorator;
import it.robur.loggers.JSONUtil;
import it.robur.loggers.LogFilesUtil;
import it.robur.loggers.LogPoller;
import it.robur.loggers.LogPollerSleep;
import it.robur.loggers.LogPollerSync;
import it.robur.loggers.NextPanelJSONLogCons;
import it.robur.modbus.ModbusCCI;
import it.robur.modbus.ModbusDDC;
import it.robur.modbus.ModbusGEN10;
import it.robur.modbus.ModbusGHP10;
import it.robur.modbus.ModbusInstrument;
import it.robur.modbus.ModbusSerial;
import it.robur.net.Connected;
import it.robur.net.ConnectedMulti;
import it.robur.net.MQTTClientLogPublisherReceiver;
import it.robur.net.SerialOverTCP;
import it.robur.rberry.FullJSONtoGEN10LogConsumer;
import it.robur.rberry.JSONParser;
import it.robur.rberry.RberryComm;
import it.robur.rberry.SocketRberryComm;
import it.robur.strings.Language;
import it.robur.util.DebugLog;

/**
 * Listener for all the user action of the button of the MainView and ModbusView
 */
public class MainController extends WindowAdapter implements ActionListener {
	//Action command
	private final static String START = "start_id";
	private final static String STOP = "stop_id";
	private final static String WRITE = "write_id";
	private final static String READ = "read_id";
	private final static String RESET = "reset_id";	
	private final static String DOWNLOAD = "dawnload_id";	
	private final static String SETTINGS = "option_id";
	private final static String DIAGNOSTIC = "diagnostic_id";	
	private final static String MODBUS = "Modbus_id";
	private final static String FLASH = "Flash_id";
	private final static String EMERGENCYFLASH = "EmergencyFlash_id";
	private final static String CONNECT = "connect_id";
	private final static String DISCONNECT = "disconnect_id";
	//private final static String JTAG = "jtag_id";
	private final static String SEND = "send_id";
	private final static String REBOOT = "reboot_id";
	private final static String ABOUT = "about_id";
	//SwingWorker task
	private static BackgroundTask backgroundTask = null;
	//View reference
	private MainView view = null;	
	//Models reference
	private GHP10LogUIConsPC GHP10Consumer = null;
	private DDCLogUIConsPC DDCConsumer = null;
	private CCILogUIConsPC CCIConsumer = null;
	private GEN10LogUIConsPC GEN10Consumer = null;
	private SettingsModel settingsModel = null;
	//
	private ModbusGHP10 mbGhp10 = null;
	private ModbusDDC mbDDC = null;
	private ModbusCCI mbCCI = null;
	private ModbusGEN10 mbGen10 = null;
	private LogPollerSync poller = null;
	//
	RberryComm comm = null;
	private boolean exitService = true;
	
	/*
	 * This inner class manage the shutdown for the command line polling. The instance of this class must be installed 
	 * thought Runtime.getRuntime().addShutdownHook(Thread name). In this way when system signal: SIGHUP (only for unix), 
	 * SIGINT (^C) and SIGTERM([x]) are generated for this process, the following thread is executed:
	 * 
	 * This thread say at the poller to stop, wait the end of the main thread an than halt the JVM.
	 * 
	 */
	class ShutDown extends Thread {
		LogPollerSync poller;
		BackgroundTask mainThread;		
		
		/*
		 * aPoller: the poller to stop
		 * aThread: the thread that start the poller
		 */
		ShutDown(BackgroundTask aThread, LogPollerSync aPoller) {
			poller = aPoller;
			mainThread = aThread;
		}
		
		public void run() {
			
			view.showGUIBusy(Language.getMessages("msg_logging"));
			
			//stop the poller
			new Thread(new Runnable() 
			{    		
				public void run() 
	            {
					if( comm != null )
					{
						comm.stopListening();
						exitService = true;		
					}
					
					if( poller != null )
						poller.setExit();
	            }
			}).start();
	        
	        /* 
	         * now wait the for the main thread to stop, because if this thread ends, the JVM halt
	         * independently that the other thread are terminated or not.
	         */		        
	        while(!mainThread.isDone()) {
	        	try {
					Thread.sleep(500);
				} catch (InterruptedException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
	        }				

	    }	
	}
	
	MainController(MainView aView, GHP10LogUIConsPC aGHP10Consumer, DDCLogUIConsPC aDDCConsumer, CCILogUIConsPC aCCIConsumer, GEN10LogUIConsPC aGEN10Consumer, SettingsModel aSettingsModel) {
		view = aView;			
		
		view.addWindowListener(this);
		
		view.getStartButtonRef().addActionListener(this);
		view.getStopButtonRef().addActionListener(this);
		view.getDownloadButtonRef().addActionListener(this);
		view.getSettingsButtonRef().addActionListener(this);
		view.getShareDiagnosticButtonRef().addActionListener(this);
		view.getAboutButtonRef().addActionListener(this);
		view.getModbusButtonRef().addActionListener(this);
		view.getModbusViewRef().getWriteButtonRef().addActionListener(this);
		view.getModbusViewRef().getReadButtonRef().addActionListener(this);
		view.getModbusViewRef().getResetButtonRef().addActionListener(this);	
		view.getFwFileViewRef().getFlashItemRef().addActionListener(this);
		view.getFwFileViewRef().getEmergencyFlashItemRef().addActionListener(this);
		view.getConnectButtonRef().addActionListener(this);
		view.getDisconnectButtonRef().addActionListener(this);
		//view.getJtagButtonRef().addActionListener(this);
		view.getConfigurationViewRef().getSendButtonRef().addActionListener(this);
		view.getStatusViewRef().getRebootButtonRef().addActionListener(this);
		
		view.getStartButtonRef().setActionCommand(MainController.START);
		view.getStopButtonRef().setActionCommand(MainController.STOP);
		view.getDownloadButtonRef().setActionCommand(MainController.DOWNLOAD);
		view.getSettingsButtonRef().setActionCommand(MainController.SETTINGS);
		view.getShareDiagnosticButtonRef().setActionCommand(MainController.DIAGNOSTIC);
		view.getAboutButtonRef().setActionCommand(MainController.ABOUT);
		view.getModbusButtonRef().setActionCommand(MainController.MODBUS);
		view.getModbusViewRef().getWriteButtonRef().setActionCommand(MainController.WRITE);
		view.getModbusViewRef().getReadButtonRef().setActionCommand(MainController.READ);
		view.getModbusViewRef().getResetButtonRef().setActionCommand(MainController.RESET);
		view.getFwFileViewRef().getFlashItemRef().setActionCommand(MainController.FLASH);
		view.getFwFileViewRef().getEmergencyFlashItemRef().setActionCommand(MainController.EMERGENCYFLASH);
		view.getConnectButtonRef().setActionCommand(MainController.CONNECT);
		view.getDisconnectButtonRef().setActionCommand(MainController.DISCONNECT);
		//view.getJtagButtonRef().setActionCommand(MainController.JTAG);
		view.getConfigurationViewRef().getSendButtonRef().setActionCommand(MainController.SEND);
		view.getStatusViewRef().getRebootButtonRef().setActionCommand(MainController.REBOOT);
		
		GHP10Consumer = aGHP10Consumer;
		DDCConsumer = aDDCConsumer;
		CCIConsumer = aCCIConsumer;
		GEN10Consumer = aGEN10Consumer;
		settingsModel = aSettingsModel;
	}	
	
	/**
	 * force the auto start of the logger (by forcing the listener)
	 */
	public void forceAutoStart(boolean force) {
		if (force)
			this.actionPerformed(new ActionEvent(view.getStartButtonRef(), ActionEvent.ACTION_PERFORMED, MainController.START));			
	}
	
	@Override
	public void actionPerformed(ActionEvent aEvent) {
		String id = aEvent.getActionCommand();
				
		if (id == START) {
			backgroundTask = new BackgroundTask();
			backgroundTask.execute();			
		} else if (id == STOP) {
			//DO NOT USE the standard cancel() method!
			backgroundTask.stop();
		} else if (id == WRITE) {
			writeTask();          
		} else if (id == READ) {
			readTask();          
		} else if (id == RESET) {
			resetTask();          
		} else if (id == DOWNLOAD) {
			new DownloadTask().execute();       
		} else if (id == SETTINGS) {
			view.getSettingsViewRef().show();
		} else if (id == DIAGNOSTIC) {
			shareDiagnostic();
		} 
		else if (id == MODBUS) {
			view.getModbusViewRef().show();
		} else if (id == FLASH) {
			if (JOptionPane.showConfirmDialog(view, Language.getMessages("ttl_confirm_flash"), Language.getMessages("msg_info"), JOptionPane.YES_NO_OPTION) == JOptionPane.YES_OPTION)
				new FlashTask().execute();
		} else if (id == EMERGENCYFLASH) {
			if (JOptionPane.showConfirmDialog(view, Language.getMessages("ttl_confirm_flash"), Language.getMessages("msg_info"), JOptionPane.YES_NO_OPTION) == JOptionPane.YES_OPTION)
				new EmergencyFlashTask().execute();
		} else if (id == CONNECT) {			
			connectTask();			
		} else if (id == DISCONNECT) {			
			disconnect();				
		} 
//		else if (id == JTAG) {
//			if(ApplicationPreference.isWindows()) {
//				try {
//					if(Files.exists(Paths.get(".\\Robur Flashing Tool"))) {
//						if(Files.exists(Paths.get(".\\Robur Flashing Tool\\jflashingtool.exe"))) {
//							Process p = Runtime.getRuntime().exec("cmd /C \".\\Robur Flashing Tool\\jflashingtool.exe\"");
//						}
//						else
//							JOptionPane.showMessageDialog(view,"File jflashingtool.exe not found in Sub folder .\\Robur Flashing Tool", Language.getMessages("msg_error"), JOptionPane.ERROR_MESSAGE);
//					}
//					else
//						JOptionPane.showMessageDialog(view,"Sub folder .\\Robur Flashing Tool not found", Language.getMessages("msg_error"), JOptionPane.ERROR_MESSAGE);
//				} catch (IOException e) {
//					JOptionPane.showMessageDialog(view,"Error launching JTAG Programming", Language.getMessages("msg_error"), JOptionPane.ERROR_MESSAGE);
//				}	
//			} else
//				JOptionPane.showMessageDialog(view,"JTAG Programming, available only on WINDOWS", Language.getMessages("msg_error"), JOptionPane.ERROR_MESSAGE);
//		} 
		else if (id == SEND) {			
			sendJSONTask();
		} else if (id == REBOOT) {			
			reboot();
		} else if (id == ABOUT) {			
			JOptionPane.showMessageDialog(view, "JSerialTool " + Manifest.versionName + " version code " + Manifest.versionCode + "\n" +
												System.getProperty("java.runtime.name") + " " + System.getProperty("java.runtime.version") + " " + System.getProperty("sun.arch.data.model") + " bit\n" +
												System.getProperty("os.name") + " " + System.getProperty("os.arch"),					
												Language.getMessages("app_name"), JOptionPane.INFORMATION_MESSAGE, new ImageIcon(ApplicationPreference.ICON_PATH + "logo.gif"));
		} else {
			JOptionPane.showMessageDialog(view,"Pressed unknow button id: " + id, Language.getMessages("msg_error"), JOptionPane.ERROR_MESSAGE);
		}

	}
	@Override
	// On window closing, exit only if the logger is stopped
	public void windowClosing(WindowEvent e) {
		if(backgroundTask != null) {
			if(backgroundTask.isDone()) {
				e.getWindow().dispose();
			} else {
				JOptionPane.showMessageDialog(view,Language.getMessages("msg_exit"), Language.getMessages("msg_warning"), JOptionPane.WARNING_MESSAGE);
			}
		} else if (exitService == false){
			JOptionPane.showMessageDialog(view,Language.getMessages("msg_exit_connection"), Language.getMessages("msg_warning"), JOptionPane.WARNING_MESSAGE);			
		}
			else
			e.getWindow().dispose();
	}
	
	
	private void logError(SettingsModel aModel, Throwable excp) {
		
		String detailMessage = excp.toString() + "\n";
		for (StackTraceElement e : excp.getStackTrace())
			detailMessage += ("\t" + e.getClassName() + ":" + e.getLineNumber() + "\n");
		
		DebugLog log = null; 
		try
		{
			String path = aModel.getLogPath();
			String tempPath = path + File.separator + ".." + File.separator + ".itroburtemp";
		    new File(tempPath).mkdirs();
		   
		    log = new DebugLog(tempPath, "fatal", false, true, 7);
		   
		   if(detailMessage != null)
		      log.println("Fatal, Detail", detailMessage);

		   Properties systemProperties = System.getProperties();
		   SortedMap sortedSystemProperties = new TreeMap(systemProperties);
		   Set<String> keys = sortedSystemProperties.keySet();
		   String property = "";
		   for (String key : keys) {
			   property += "[" + key + "] = " + systemProperties.get(key) + "\n";
			   
		   }
		   if(property != null)
			   log.println("System Property", property);		   

		}
		catch(Exception e)
		{
		   // just ignore
		}     
		finally
		{
		   if (log != null)
		      log.close();
		}
		
	}
	
	private void showErrorDialogWithExtraInfo(Throwable excp) {
		logError(settingsModel, excp);
		ErrorDialog.showErrorDialog(view, settingsModel, excp);	
	}
	
	private void showErrorDialog(Throwable excp) {
		ErrorDialog.showErrorDialog(view, settingsModel, excp);	
	}
	
	
	class CloudConnectedStatus implements Connected 
	{
		@Override
		public void connected(boolean connected) 
		{
			if (connected)
				view.setCloudConnected();
			else
				view.setCloudNOTConnected();
		}

	}
	
	// TODO: Marco, move wherever you want 
	//This code is for the 
	OpStrStr notifyCannotWriteXMLLogGUI = new OpStrStr()
	{
		public void op(String message, String details)
				throws IOException
		{
			// TODO...
			// ignore params (or write into debuglog)
			// use string Id: rberry_usb_memory_suggestion
			// color on Android: #FFFFB500 on white
			//view.setBrokenUsbKey();
			view.updateWarning(Language.getMessages("rberry_usb_memory_suggestion"));
		}							
	};
		
	/*
	 * Background task that start the logging
	 * Do not use the standard cancel() method to stop doInBackground 
	 * but the new defined stop()
	 */
	class BackgroundTask extends SwingWorker<Boolean, Void>  {
		private String logPath;
		private boolean tcpOption;
		private boolean comOption;
		private boolean fixedIPUsed;
		private String strPort;
		private String ipAddres;
		private boolean ipIsValid;
		private int modbusAddres;
		private int pollingPeriod;
		private String savingTime;
		private String language;
		private boolean cloudIsSelected;
		private boolean confserverIsSelected;
		private boolean massMemoryFaultTolerance;
		private boolean  testModeIsSelected;
		private boolean  allQuantitiesIsSelected;
		private boolean isK18;
		private boolean isDDC;
		private boolean isCCI;
		private boolean isNextG;
		private ShutDown shutdown;
		
		protected volatile DebugLog debugLog = null;

		BackgroundTask() {
			logPath = settingsModel.getLogPath();
			tcpOption = settingsModel.isTcpOptionSelected();
			comOption = settingsModel.isComOptionSelected();
			fixedIPUsed = settingsModel.isFixedIPSelected();
			strPort = settingsModel.getComPort();
			ipAddres = settingsModel.getIPAddres();
			modbusAddres = settingsModel.getModbusAddres();
			pollingPeriod = settingsModel.getPollingPeriod();
			savingTime = settingsModel.getSavingTime();
			language = settingsModel.getLanguageCode();
			cloudIsSelected = settingsModel.isCloudSelected();
			confserverIsSelected = settingsModel.isConfserverSelected();
			massMemoryFaultTolerance = settingsModel.isMassMemoryFaultTolerance();
			testModeIsSelected = settingsModel.isTestModeSelected();
			allQuantitiesIsSelected = settingsModel.isAllQuantitiesSelected();
			isK18   = settingsModel.isBoardK18();
			isDDC   = settingsModel.isBoardDDC();
			isCCI   = settingsModel.isBoardCCI();
			isNextG = settingsModel.isBoardNextG();
		}
		
		@Override
		protected Boolean doInBackground() throws Exception {
			ModbusSerial serial = null;
			
			// Isn't EDT, so update GUI using invokeLater().
			SwingUtilities.invokeLater(new Runnable () {
				@Override
				public void run(){
					view.showGUIBusy(Language.getMessages("msg_logging"));
				}
			});
			
			// Paths
			String path = logPath;
			String tempPath  = path + File.separator + ".." + File.separator + ".itroburtemp";
			/* 
			 * If the path is not the standard one, check it's existence
			 * The path can be wrong (not existent device, corrupted, ...)
			 */
			if (!path.equals(ApplicationPreference.LOGPATH_DEFAULT)) {
				File path_file = new File(path);
				File check_path = new File(path_file.getParent());
				if(check_path.isDirectory()) {
					//Ensure output DIR exist
					new File(path).mkdirs();
					new File(tempPath).mkdirs();					
				}				
				else {
					if(!confserverIsSelected) {
						// Isn't EDT, so update GUI using invokeLater().
						SwingUtilities.invokeLater(new Runnable () {
							@Override
							public void run(){
								JOptionPane.showMessageDialog(view, "Logpath: " + logPath + " not existent", Language.getMessages("msg_error"), JOptionPane.ERROR_MESSAGE);
							}
						});
						return false;						
					}						
				} 	
				
			} else {
				//Ensure standard output DIR exist
				new File(path).mkdirs();
				new File(tempPath).mkdirs();
			}
						
			try {
				debugLog = new DebugLog(tempPath, "applog", true);
				
				//set the serial port
				if(tcpOption && fixedIPUsed) {
					// TCP with given address	
					serial = new SerialOverTCP(InetAddress.getByName(ipAddres), new USRBridgePC(debugLog));
				} else if (tcpOption && !fixedIPUsed) {
					// TCP with auto discovery
					serial = new SerialOverTCP(new USRBridgePC(debugLog));
				} else if (comOption) {	
					//COM port
					if (strPort.equals(null)) {
						// Isn't EDT, so update GUI using invokeLater().
						SwingUtilities.invokeLater(new Runnable () {
							@Override
							public void run(){
								JOptionPane.showMessageDialog(view, Language.getMessages("msg_com_invalid"), Language.getMessages("msg_error"), JOptionPane.ERROR_MESSAGE);
							}
						});
						return false;
					}
					serial = new SerialPurejavacomm(strPort);
				} else {
					// Isn't EDT, so update GUI using invokeLater().
					SwingUtilities.invokeLater(new Runnable () {
						@Override
						public void run(){
							JOptionPane.showMessageDialog(view, "No type of connection Selected", Language.getMessages("msg_error"), JOptionPane.ERROR_MESSAGE);
						}
					});
					return false;					
				}
				
				// Start the GHP10 Logger
				ConfServer confServer = null; // TODO volatile etc. per confServer.error() 
				DDCJSONLogCons DDCJSONConsumer = null;
				GHP10JSONLogCons GHP10JSONConsumer = null;
				CCIJSONLogCons CCIJSONConsumer = null;
				GEN10JSONLogCons GEN10JSONConsumer = null;
				try
				{
					if (confserverIsSelected) {
						confServer = new ConfServer(tempPath);
						confServer.connect();
						confServer.start();
					}
					
					int retainDays;
					if(cloudIsSelected)
						retainDays = 30;
					else
						retainDays = 0;
					
					
					
					OpStrStr notifyCannotWriteXMLLog;
					if(confserverIsSelected)
						notifyCannotWriteXMLLog = new OpStrStrMulti(notifyCannotWriteXMLLogGUI, confServer.notifyCannotWriteXMLLog);
					else
						notifyCannotWriteXMLLog = null;
					
					if(isK18) {
						
						JSONPublisherMulti.Builder jsonSamplePublisherBuilder  = new JSONPublisherMulti.Builder();
						JSONPublisherMulti.Builder jsonCloudResponsePublisherBuilder = new JSONPublisherMulti.Builder();

						MQTTClientLogPublisherReceiver mqttPublisherReceiver = null;

						if( cloudIsSelected ) {
							mqttPublisherReceiver = new MQTTClientLogPublisherReceiver(
											"K18", 
											new MqttClientPCBuilder(tempPath),
											new ConnectedMulti( new CloudConnectedStatus(), confServer), 
											pollingPeriod * 2, 
											debugLog);
							
							jsonSamplePublisherBuilder.addPublisher(mqttPublisherReceiver.samplePublisher);
							jsonCloudResponsePublisherBuilder.addPublisher(mqttPublisherReceiver.responsePublisher);
						}

						if( testModeIsSelected ) {
							jsonSamplePublisherBuilder.addPublisher(new DumpJSONLogPublisher(tempPath));
							jsonCloudResponsePublisherBuilder.addPublisher(new DumpJSONLogPublisher(tempPath));
						}
						
						if( confserverIsSelected )
							jsonSamplePublisherBuilder.addPublisher(confServer);

						JSONPublisherMulti jsonSamplePublishers = jsonSamplePublisherBuilder.buildIfNeeded();
						if( jsonSamplePublishers != null )
							GHP10JSONConsumer = new GHP10JSONLogCons(jsonSamplePublishers, confserverIsSelected, debugLog);
						
						poller = new LogPollerSleep(pollingPeriod, LogPoller.timeStringToDate(savingTime));
						poller.setDebugLog(debugLog);	
						GHP10Logger logger = new GHP10Logger();
						
						shutdown = new ShutDown(backgroundTask, poller);
						Runtime.getRuntime().addShutdownHook(shutdown);
						
						mbGhp10 = logger.initLog(serial, modbusAddres,
												language,
												path, tempPath, 
												GHP10Consumer, GHP10JSONConsumer, null, retainDays, notifyCannotWriteXMLLog,
												debugLog); // returns ghp10
						
						JSONPublisherResponseDecorator jsonCloudResponsePublisher = JSONPublisherResponseDecorator.buildIfNeeded(
																						jsonCloudResponsePublisherBuilder.buildIfNeeded(),
																						false);
						if( mqttPublisherReceiver != null && jsonCloudResponsePublisher != null)
							mqttPublisherReceiver.setMessageReceiver(
											new GHP10JSONReceive(
													mbGhp10,
													jsonCloudResponsePublisher,
													new GHP10JSONLogCons(jsonCloudResponsePublisher, confserverIsSelected, null)));
						
						if( confServer != null )
						{							
							JSONPublisherResponseDecorator jsonConfServerResponsePublisher = new JSONPublisherResponseDecorator(
																															confServer,
																															true);
							
							if( jsonSamplePublishers != null )
								JSONUtil.publishHostConfig(
										jsonSamplePublishers,
										confServer.requestConfServerAction(JSONUtil.getConfig, JSONUtil.getConfigActionResponse),
										"k18",
										String.valueOf(mbGhp10.getApplianceSerial()));
							
							confServer.startListeningOnAnotherThread(
												new GHP10JSONReceive(
															mbGhp10,
															jsonConfServerResponsePublisher,
															new GHP10JSONLogCons(jsonConfServerResponsePublisher, confserverIsSelected, null)));
						}
								
						/// Isn't EDT, so update GUI using invokeLater().
						SwingUtilities.invokeAndWait(new Runnable () {
							@Override
							public void run(){
								view.createTabPanelForLoggingTable();
								view.showGUI_not_Busy(Language.getMessages("msg_logging"));
								view.enableLoggingUserAction(true);
							}
						});						
						
						logger.log(poller);
					} else if(isDDC) {						
																													
						JSONPublisherMulti.Builder jsonSamplePublisherBuilder  = new JSONPublisherMulti.Builder();
						JSONPublisherMulti.Builder jsonCloudResponsePublisherBuilder = new JSONPublisherMulti.Builder();

						MQTTClientLogPublisherReceiver mqttPublisherReceiver = null;
						if( cloudIsSelected ) {
							mqttPublisherReceiver = new MQTTClientLogPublisherReceiver(
											"DDC", 
											new MqttClientPCBuilder(tempPath), 
											new ConnectedMulti( new CloudConnectedStatus(), confServer), 
											pollingPeriod * 2, 
											debugLog);
							
							jsonSamplePublisherBuilder.addPublisher(mqttPublisherReceiver.samplePublisher);
							jsonCloudResponsePublisherBuilder.addPublisher(mqttPublisherReceiver.responsePublisher);
						}

						if( testModeIsSelected ) {
							jsonSamplePublisherBuilder.addPublisher(new DumpJSONLogPublisher(tempPath));
							jsonCloudResponsePublisherBuilder.addPublisher(new DumpJSONLogPublisher(tempPath));
						}
												
						if( confserverIsSelected )
							jsonSamplePublisherBuilder.addPublisher(confServer);
					
						JSONPublisherMulti jsonSamplePublishers = jsonSamplePublisherBuilder.buildIfNeeded();
						if( jsonSamplePublishers != null )
							DDCJSONConsumer = new DDCJSONLogCons( jsonSamplePublishers, !allQuantitiesIsSelected, confserverIsSelected, debugLog);
						
						poller = new LogPollerSleep(pollingPeriod, LogPoller.timeStringToDate(savingTime));
						poller.setDebugLog(debugLog);
						DDCLogger logger = new DDCLogger();
						
						shutdown = new ShutDown(backgroundTask, poller);
						Runtime.getRuntime().addShutdownHook(shutdown);
						
						mbDDC = logger.initLog(serial, modbusAddres,
													language,
													path, tempPath, 
													DDCConsumer, DDCJSONConsumer, 
													null, 
													! confserverIsSelected, 
													retainDays, 
													notifyCannotWriteXMLLog,
													debugLog); // returns ddc

						JSONPublisherResponseDecorator jsonCloudResponsePublisher = JSONPublisherResponseDecorator.buildIfNeeded(
																		jsonCloudResponsePublisherBuilder.buildIfNeeded(),
																		false);
						if( mqttPublisherReceiver != null && jsonCloudResponsePublisher != null)
							mqttPublisherReceiver.setMessageReceiver(
												new DDCJSONReceive(
														mbDDC,
														jsonCloudResponsePublisher,
														new DDCJSONLogCons(jsonCloudResponsePublisher, !allQuantitiesIsSelected, confserverIsSelected, null)));
						
						if( confServer != null )
						{							
							JSONPublisherResponseDecorator jsonConfServerResponsePublisher = new JSONPublisherResponseDecorator(
																										confServer,
																										true);
							
							if( jsonSamplePublishers != null )
								JSONUtil.publishHostConfig(
										jsonSamplePublishers,
										confServer.requestConfServerAction(JSONUtil.getConfig, JSONUtil.getConfigActionResponse),
										"ddc",
										String.valueOf(mbDDC.getSerial()));
							
							confServer.startListeningOnAnotherThread(
													new DDCJSONReceive(
															mbDDC,
															jsonConfServerResponsePublisher,
															new DDCJSONLogCons(jsonConfServerResponsePublisher, !allQuantitiesIsSelected, confserverIsSelected, null)));
						}

						
						/// Isn't EDT, so update GUI using invokeLater().
						SwingUtilities.invokeAndWait(new Runnable () {
							@Override
							public void run(){
								view.createTabPanelForLoggingTable();
								view.showGUI_not_Busy(Language.getMessages("msg_logging"));
								view.enableLoggingUserAction(true);
							}
						});	
						
						logger.log(poller);
						
					} else if(isCCI) {						
						
						JSONPublisherMulti.Builder jsonSamplePublisherBuilder  = new JSONPublisherMulti.Builder();

						// TODO Cloud not available yet
//						if( cloudIsSelected ) 
//							jsonPublisherBuilder.addPublisher(
//									new MQTTClientLogPublisher(
//											"CCI", 
//											new MqttClientPCBuilder(tempPath), 
//											new ConnectedMulti( new CloudConnectedStatus(), confServer), 
//											pollingPeriod * 2, 
//											debugLog));

						if( testModeIsSelected )
							jsonSamplePublisherBuilder.addPublisher(new DumpJSONLogPublisher(tempPath));
						
						if( confserverIsSelected )
							jsonSamplePublisherBuilder.addPublisher(confServer);

						JSONPublisherMulti jsonSamplePublishers = jsonSamplePublisherBuilder.buildIfNeeded();
						if( jsonSamplePublishers != null )
							CCIJSONConsumer = new CCIJSONLogCons( jsonSamplePublishers, !allQuantitiesIsSelected, confserverIsSelected, debugLog);
						
						poller = new LogPollerSleep(pollingPeriod, LogPoller.timeStringToDate(savingTime));
						poller.setDebugLog(debugLog);
						CCILogger logger = new CCILogger();
						
						shutdown = new ShutDown(backgroundTask, poller);
						Runtime.getRuntime().addShutdownHook(shutdown);
						
						mbCCI = logger.initLog(serial, modbusAddres,
												language,
												path, tempPath, 
												CCIConsumer, CCIJSONConsumer, null, retainDays, notifyCannotWriteXMLLog,
												debugLog); // returns cci
						
						/// Isn't EDT, so update GUI using invokeLater().
						SwingUtilities.invokeAndWait(new Runnable () {
							@Override
							public void run(){
								view.createTabPanelForLoggingTable();
								view.showGUI_not_Busy(Language.getMessages("msg_logging"));
								view.enableLoggingUserAction(true);
							}
						});	
						
						logger.log(poller);		
					}
					if(isNextG) 
					{
																
			        	long minMillisToWaitBeforeError = 0;
			        	long startTime = System.currentTimeMillis();
			        	
			        	//Open communication
			        	comm = new SocketRberryComm("192.168.12.31"); // ("192.168.12.31");
			        	minMillisToWaitBeforeError =  2 * 60 * 1000;		// allow minutes to the Raspeberry to reboot        	
			        	exitService = false;
			        	
		            	while(!exitService ) 
		            	{
		            		
		            		//Try to connect to Roburberry
		            		try {
		    					comm.connect();
		    				}
		    				catch(IOException excp) {
		    					if ( (System.currentTimeMillis() - startTime) >  minMillisToWaitBeforeError)
		    						throw excp;
		    					try
		    					{
		    						//comm.stopListening();
		    						Thread.sleep(5000);
		    					}
		    					catch(InterruptedException ecxcp)
		    					{}
		
		    					continue;
		    				}
		            		
							shutdown = new ShutDown(backgroundTask, poller);
							Runtime.getRuntime().addShutdownHook(shutdown);
							
							final JSONParser jsonToLogConsumer = new FullJSONtoGEN10LogConsumer(GEN10Consumer);

				        		            				                    		                    
		                    //Start listening
		                    comm.startListening(
		        					new OpJSON()
		        					{
		        						@Override
		        						public void op(JSONObject value) throws IOException
		        						{
		        							debugLog.syncPrintln("RECEIVED (CONFIG) from NextPanel", value.toString(4));
		        						}
		    						
		        					},
		        					new OpJSON()
		        					{
		        						@Override
		        						public void op(JSONObject value) throws IOException
		        						{
		        							try
		        							{
		        								debugLog.syncPrintln("RECEIVED (SAMPLE) from NextPanel", value.toString(4));
		        								
		        								
		        								// adapt NextPanel to single GEN10 
		        								if( value.has("identification") )
		        								{
		        									value = value
		        										.getJSONObject("identification")
		        										.getJSONArray("heater_modules")
		        										.getJSONObject(0);
		        								}
		        								else if( value.has("samples") )
		        								{
		        									value = value
		        										.getJSONArray("samples")
		        										.getJSONObject(0)
		        										.getJSONArray("heater_modules")
		        										.getJSONObject(0);
		        									
		        									value = NextPanelJSONLogCons.unAdaptGEN10Sample(value);
		        									
		        								}
		        								else
		        									return;

		        								debugLog.syncPrintln("ADAPTED GEN10 sample", value.toString(4));
		        								
		        								if( jsonToLogConsumer != null )
		        									jsonToLogConsumer.parseJSON(value);
		        								
		        								
		        								if( value.has("identification") )
		        								{		        								
			        								/// Isn't EDT, so update GUI using invokeLater().
			        								try 
			        								{
														SwingUtilities.invokeAndWait(new Runnable () 
														{
															@Override
															public void run(){
																view.createTabPanelForLoggingTable();
																view.showGUI_not_Busy(Language.getMessages("msg_logging"));
																view.enableLoggingUserAction(true);
															}
														});
													} 
			        								catch (InvocationTargetException excp) 
			        								{
			        									throw new IOException(excp);
													} 
			        								catch (InterruptedException excp) 
			        								{
			        									throw new IOException(excp);
													}
		        								}

		        							}
		        							catch(JSONException excp)
		        							{
		        								throw new IOException("JSONException: " + excp.toString());
		        							}
		        						}
		        					}
		        				);
		                    	                    
		            		
		    				startTime = System.currentTimeMillis();	            		
		   
		            	}

					}								
				}
				catch(final Throwable excp)
				{
					logError(settingsModel, excp);				
					
					// Isn't EDT, so update GUI using invokeLater().
					SwingUtilities.invokeLater(new Runnable () {
						@Override
						public void run(){
							view.showGUIBusy(Language.getMessages("msg_logging"));
							showErrorDialog(excp);
						}
					});
					
					//This try/catch suppers the second error message (not useful)
					try {
						if( confServer != null)	
							confServer.error(excp);
					}
					catch(Exception e) {						
					}					
										
					return false;
					
				}
				finally
				{
					if (GHP10JSONConsumer != null)
						GHP10JSONConsumer.close();
					if (DDCJSONConsumer != null)
						DDCJSONConsumer.close();
					if (CCIJSONConsumer != null)
						CCIJSONConsumer.close();
					if (confServer != null)
						confServer.disconnect();
					
					if(comm != null)
						comm.disconnect();
				}
			}
			finally
			{
				if(debugLog != null)
					debugLog.close();
			}
			
			return true;
		}
		
		@Override
		//executed by EDT, when doInBackground ends
		protected void done() {
			boolean task_ended_correctly;		
			try {
				shutdown = null;
				view.showGUI_not_Busy("");
				view.enableLoggingUserAction(false);
				view.getLogFileViewRef().updateLogFiles();
				view.clearCloudConnected();
				
				//Show logging end message only if task ends correctly
				task_ended_correctly = get();				
				if(task_ended_correctly) {
					JOptionPane.showMessageDialog(view, Language.getMessages("msg_terminated"), Language.getMessages("msg_info"), JOptionPane.INFORMATION_MESSAGE);				
				}								
			} catch (InterruptedException e) {
				showErrorDialogWithExtraInfo(e);
			} catch (ExecutionException e) {
				showErrorDialogWithExtraInfo(e);
			}		
		}
		/**
		 * Do not use the standard cancel() method to stop doInBackground, 
		 * but use this one that stop the poller. So doInBackground can end
		 * normally.
		 * Executed by EDT
		 */
		public void stop() {
			if(backgroundTask.isDone())
				return;
			
			view.showGUIBusy(Language.getMessages("msg_logging"));
			new Thread(new Runnable() 
			{    		
				public void run() 
	            {
					if( comm != null )
					{
						comm.stopListening();
						exitService = true;		
					}
					
					if( poller != null )
						poller.setExit();
	            }
			}).start();
		}
	
	}
	
	/*
	 * Background task that download the logger
	 */
	class DownloadTask extends SwingWorker<Boolean, Void>  {	
		private String logPath;
		private boolean tcpOption;
		private boolean comOption;
		private boolean fixedIPUsed;
		private String strPort;
		private String ipAddres;
		private String language;
		private int modbusAddres;
		private boolean isK18;
		private boolean isDDC;
		private boolean isCCI;
		private boolean isNextG;

		DownloadTask() {
			logPath = settingsModel.getLogPath();
			tcpOption = settingsModel.isTcpOptionSelected();
			comOption = settingsModel.isComOptionSelected();
			fixedIPUsed = settingsModel.isFixedIPSelected();
			strPort = settingsModel.getComPort();
			ipAddres = settingsModel.getIPAddres();
			language = settingsModel.getLanguageCode();
			modbusAddres = settingsModel.getModbusAddres();
			isK18 = settingsModel.isBoardK18();
			isDDC = settingsModel.isBoardDDC();
			isCCI = settingsModel.isBoardCCI();
			isNextG = settingsModel.isBoardNextG();
		}
		
		@Override
		protected Boolean doInBackground() throws Exception {

			// Isn't EDT, so update GUI using invokeLater().
			SwingUtilities.invokeLater(new Runnable () {
				@Override
				public void run(){
					view.showGUIBusy(Language.getMessages("ttl_downloading") + " ... " + Language.getMessages("msg_downloading"));
					view.disableAllLoggingAction();
				}
			});

    		ModbusSerial serial = null;        		
 		
    		// Paths
			String path = logPath;
			String tempPath  = path + File.separator + ".." + File.separator + ".itroburtemp";
			/* 
			 * If the path is not the standard one, check it's existence
			 * The path can be wrong (not existent device, corrupted, ...)
			 */
			if (!path.equals(ApplicationPreference.LOGPATH_DEFAULT)) {
				File path_file = new File(path);
				File check_path = new File(path_file.getParent());
				if(check_path.isDirectory()) {
					//Ensure output DIR exist
					new File(path).mkdirs();
					new File(tempPath).mkdirs();					
				}				
				else {
						// Isn't EDT, so update GUI using invokeLater().
						SwingUtilities.invokeLater(new Runnable () {
							@Override
							public void run(){
								JOptionPane.showMessageDialog(view, "Logpath: " + logPath + " not existent", Language.getMessages("msg_error"), JOptionPane.ERROR_MESSAGE);
							}
						});
						return false;			
				} 	
				
			} else {
				//Ensure standard output DIR exist
				new File(path).mkdirs();
				new File(tempPath).mkdirs();
			}
    		
    		DebugLog debugLog = null;
    		try
    		{
    			debugLog = new DebugLog(tempPath, "applog", true);
    			
    			//set the serial port
				if(tcpOption && fixedIPUsed) {
					// TCP with given address
					serial = new SerialOverTCP(InetAddress.getByName(ipAddres), new USRBridgePC(debugLog));
				
				} else if (tcpOption && !fixedIPUsed) {
					// TCP with auto discovery
					serial = new SerialOverTCP(new USRBridgePC(debugLog));
				} else if (comOption) {	
					//COM port
					if (strPort.equals(null)) {
						// Isn't EDT, so update GUI using invokeLater().
						SwingUtilities.invokeLater(new Runnable () {
							@Override
							public void run(){
								JOptionPane.showMessageDialog(view, Language.getMessages("msg_com_invalid"), Language.getMessages("msg_error"), JOptionPane.ERROR_MESSAGE);
							}
						});
						return false;
					}
					serial = new SerialPurejavacomm(strPort);
				} else {
					// Isn't EDT, so update GUI using invokeLater().
					SwingUtilities.invokeLater(new Runnable () {
						@Override
						public void run(){
							JOptionPane.showMessageDialog(view, "No type of connection Selected", Language.getMessages("msg_error"), JOptionPane.ERROR_MESSAGE);
						}
					});
					return false;					
				}
    			
    			// Download logger
				try
				{
					if(isK18)
						new GHP10Logger().logSingleSample(serial, modbusAddres,
														language,
														path, tempPath, debugLog);
					else if(isDDC)
						new DDCLogger().logSingleSample(serial, modbusAddres,
														language,
														path, tempPath, debugLog);
					else if(isCCI) 
						new CCILogger().logSingleSample(serial, modbusAddres,
														language,
														path, tempPath, debugLog);
					else if(isNextG)
						new GEN10Logger().logSingleSample(serial, modbusAddres,
														language,
														path, tempPath, debugLog);

				}
				catch(IOException excp)
				{
					final IOException e = excp;
					// Isn't EDT, so update GUI using invokeLater().
					SwingUtilities.invokeLater(new Runnable () {
						@Override
						public void run(){
							showErrorDialogWithExtraInfo(e);
						}
					});
					return false;
				}

    		}
			catch(IOException excp)
			{
				final IOException e = excp;
				// Isn't EDT, so update GUI using invokeLater().
				SwingUtilities.invokeLater(new Runnable () {
					@Override
					public void run(){
						showErrorDialogWithExtraInfo(e);
					}
				});
				return false;
			}
			finally
			{
				if(debugLog != null)
					debugLog.close();
			}  	
			
			return true;
		}
		
		@Override
		//executed by EDT, when doInBackground ends
		protected void done() {
			boolean task_ended_correctly;		
			try {
				view.showGUI_not_Busy("");
				view.enableLoggingUserAction(false);
				view.getLogFileViewRef().updateLogFiles();
				
				//Show logging end message only if task ends correctly
				task_ended_correctly = get();				
				if(task_ended_correctly) {
					JOptionPane.showMessageDialog(view, Language.getMessages("ttl_donwloaded"), Language.getMessages("msg_info"), JOptionPane.INFORMATION_MESSAGE);				
				}
							
			} catch (InterruptedException e) {
				showErrorDialogWithExtraInfo(e);
			} catch (ExecutionException e) {
				showErrorDialogWithExtraInfo(e);
			}		
		}
	}
	
	/*
	 * Background task that flash the firmware
	 */
	class EmergencyFlashTask extends SwingWorker<Boolean, Void>  {
		private String logPath;
		private String tempPath;
		private String flashPath;
		private String flashProgramName;		
		private String flashParamName;
		private boolean tcpOption;
		private boolean comOption;
		private boolean fixedIPUsed;
		private String strPort;
		private String ipAddres;
		private int modbusAddres;
		private boolean isK18;
		private boolean isNextG;
		private String flashMessage;
		private int commSpeed = 57600;
		
		
		EmergencyFlashTask() {
			logPath = settingsModel.getLogPath();
			tempPath  = logPath + File.separator + ".." + File.separator + ".itroburtemp";
    		// Ensure output dirs exist
    		new File(logPath).mkdirs();
    		new File(tempPath).mkdirs();
			flashPath = settingsModel.getFlashPath();
			flashProgramName = view.getFwFileViewRef().getSelectedFile();		
			flashParamName = null;
			tcpOption = settingsModel.isTcpOptionSelected();
			comOption = settingsModel.isComOptionSelected();
			fixedIPUsed = settingsModel.isFixedIPSelected();
			strPort = settingsModel.getComPort();
			ipAddres = settingsModel.getIPAddres();
			modbusAddres = settingsModel.getModbusAddres();
			isK18 = settingsModel.isBoardK18();
			isNextG = settingsModel.isBoardNextG();
			if (isK18)
				flashMessage = "ttl_k18_flashing";
			if (isNextG)
				flashMessage = "ttl_nextg_flashing";
		}
		
		@Override
		protected Boolean doInBackground() throws Exception {
			
			// Isn't EDT, so update GUI using invokeLater().
			SwingUtilities.invokeLater(new Runnable () {
				@Override
				public void run(){
					view.showCursorBusy(Language.getMessages(flashMessage) + "  ... " + Language.getMessages("msg_downloading"));
					view.disableAllLoggingAction();
				}
			});
			
			ModbusSerial serial = null;
			TerminalSerial flashSerial = null;
			DebugLog debugLog = null;
			
			try {
				
				debugLog = new DebugLog(tempPath, "applog", true);
    			
    			//set the serial port
				if(tcpOption && fixedIPUsed) {
					// TCP with given address
					serial = new SerialOverTCP(InetAddress.getByName(ipAddres), new USRBridgePC(debugLog));
					flashSerial = new SerialOverTCP(InetAddress.getByName(ipAddres), new USRBridgePC(debugLog));
				
				} else if (tcpOption && !fixedIPUsed) {
					// TCP with auto discovery
					serial = new SerialOverTCP(new USRBridgePC(debugLog));
					flashSerial = new SerialOverTCP(new USRBridgePC(debugLog));
				} else if (comOption) {	
					//COM port
					if (strPort.equals(null)) {
						// Isn't EDT, so update GUI using invokeLater().
						SwingUtilities.invokeLater(new Runnable () {
							@Override
							public void run(){
								JOptionPane.showMessageDialog(view, Language.getMessages("msg_com_invalid"), Language.getMessages("msg_error"), JOptionPane.ERROR_MESSAGE);
							}
						});
						return false;
					}
					serial = new SerialPurejavacomm(strPort);
					flashSerial = new SerialPurejavacomm(strPort);
				} else {
					// Isn't EDT, so update GUI using invokeLater().
					SwingUtilities.invokeLater(new Runnable () {
						@Override
						public void run(){
							JOptionPane.showMessageDialog(view, "No type of connection Selected", Language.getMessages("msg_error"), JOptionPane.ERROR_MESSAGE);
						}
					});
					return false;					
				}
				
				
				// ensure files exists
				if(!(new File(flashPath).isDirectory()) || !(new File(flashPath+ File.separator + flashProgramName).isFile()))
				{
					// Isn't EDT, so update GUI using invokeLater().
					SwingUtilities.invokeLater(new Runnable () {
						@Override
						public void run(){
							//TODO sistemare la stringa della finestra
							JOptionPane.showMessageDialog(view, "Missing file: " +flashPath+ File.separator +flashParamName, Language.getMessages("msg_error"), JOptionPane.ERROR_MESSAGE);
						}
					});
					return false;
				}
				
				
				//set the callback
				OpLong notifyFileSize = new OpLong()
				{
					@Override
					public void op(long value)
					{
						final int fValue = (int) value;
						// Isn't EDT, so update GUI using invokeLater().
						SwingUtilities.invokeLater(new Runnable () {
							@Override
							public void run(){
								view.closeProgressBar();
								view.showProgressBar(fValue);
							}
						});
					}
				};

				OpLong notifySentChunk = new OpLong()
				{
					
					@Override
					public void op(long value)
					{
						final Integer fValue = (int) value;
						// Isn't EDT, so update GUI using invokeLater().
						SwingUtilities.invokeLater(new Runnable () {
							@Override
							public void run(){
								view.setProgressInBar(fValue);
							}
						});
					}
				};
				
				OpStr printVerbose = new OpStr() 
				{							
					@Override
					public void op(String value) 
					{
						System.err.println(value);						
					}
				};
				
				
				if (isK18)
				{	
					//Start the flashing
					ModbusGHP10 ghp10 = new ModbusGHP10(serial, modbusAddres);
				
					new GHP10SerialFlasher(notifyFileSize, notifySentChunk, printVerbose, debugLog)
							.startFlashing(ghp10, flashSerial, flashPath, flashParamName, flashProgramName);				
				}
				
				if (isNextG)
				{	
					//Start the flashing
					ModbusGEN10 gen10 = new ModbusGEN10(serial, modbusAddres);
				
					new GEN10SerialFlasher(notifyFileSize, notifySentChunk, printVerbose, debugLog)
							.startFlashing(gen10, flashSerial, flashPath, flashParamName, flashProgramName, true, commSpeed);				
				}				
			}
			catch(IOException excp)
			{
				final IOException e = excp;
				// Isn't EDT, so update GUI using invokeLater().
				SwingUtilities.invokeLater(new Runnable () {
					@Override
					public void run(){
						showErrorDialogWithExtraInfo(e);
					}
				});
				return false;
			}
			finally
			{
				if(debugLog != null)
					debugLog.close();
			}			
			
			return true;
		}
		
		@Override
		//executed by EDT, when doInBackground ends
		protected void done() {
			boolean task_ended_correctly;		
			try {
				view.showCursor_not_Busy("");
				view.closeProgressBar();
				view.enableLoggingUserAction(false);
				
				//Show logging end message only if task ends correctly
				task_ended_correctly = get();				
				if(task_ended_correctly) {
					JOptionPane.showMessageDialog(view, Language.getMessages("ttl_flashed"), Language.getMessages("msg_info"), JOptionPane.INFORMATION_MESSAGE);				
				}
							
			} catch (InterruptedException e) {
				showErrorDialogWithExtraInfo(e);
			} catch (ExecutionException e) {
				showErrorDialogWithExtraInfo(e);
			}		
		}
		
	}
	
	/*
	 * Background task that flash the firmware
	 */
	class FlashTask extends SwingWorker<Boolean, Void>  {
		private String logPath;
		private String tempPath;
		private String flashPath;
		private String flashProgramName;		
		private String flashParamName;
		private boolean tcpOption;
		private boolean comOption;
		private boolean fixedIPUsed;
		private String strPort;
		private String ipAddres;
		private String language;
		private int modbusAddres;
		private boolean isK18;
		private boolean isNextG;
		private String flashMessage;
		private int commSpeed = 115200;
		
		
		FlashTask() {
			logPath = settingsModel.getLogPath();
			tempPath  = logPath + File.separator + ".." + File.separator + ".itroburtemp";
			// Ensure output dirs exist
    		new File(logPath).mkdirs();
    		new File(tempPath).mkdirs();
			flashPath = settingsModel.getFlashPath();
			flashProgramName = view.getFwFileViewRef().getSelectedFile();		
			flashParamName = null;
			tcpOption = settingsModel.isTcpOptionSelected();
			comOption = settingsModel.isComOptionSelected();
			fixedIPUsed = settingsModel.isFixedIPSelected();
			strPort = settingsModel.getComPort();
			ipAddres = settingsModel.getIPAddres();
			language = settingsModel.getLanguageCode();
			modbusAddres = settingsModel.getModbusAddres();
			isK18 = settingsModel.isBoardK18();
			isNextG = settingsModel.isBoardNextG();
			if (isK18)
				flashMessage = "ttl_k18_flashing";
			if (isNextG)
				flashMessage = "ttl_nextg_flashing";			
		}
		
		@Override
		protected Boolean doInBackground() throws Exception {
			
			// Isn't EDT, so update GUI using invokeLater().
			SwingUtilities.invokeLater(new Runnable () {
				@Override
				public void run(){
					view.showGUIBusy(Language.getMessages("ttl_downloading") + " ... " + Language.getMessages("msg_downloading"));
					view.disableAllLoggingAction();
				}
			});
			
			ModbusSerial serial = null;
			TerminalSerial flashSerial = null;
			DebugLog debugLog = null;
			
			try {
				
				debugLog = new DebugLog(tempPath, "applog", true);
    			
    			//set the serial port
				if(tcpOption && fixedIPUsed) {
					// TCP with given address
					serial = new SerialOverTCP(InetAddress.getByName(ipAddres), new USRBridgePC(debugLog));
					flashSerial = new SerialOverTCP(InetAddress.getByName(ipAddres), new USRBridgePC(debugLog));
				
				} else if (tcpOption && !fixedIPUsed) {
					// TCP with auto discovery
					serial = new SerialOverTCP(new USRBridgePC(debugLog));
					flashSerial = new SerialOverTCP(new USRBridgePC(debugLog));
				} else if (comOption) {	
					//COM port
					if (strPort.equals(null)) {
						// Isn't EDT, so update GUI using invokeLater().
						SwingUtilities.invokeLater(new Runnable () {
							@Override
							public void run(){
								JOptionPane.showMessageDialog(view, Language.getMessages("msg_com_invalid"), Language.getMessages("msg_error"), JOptionPane.ERROR_MESSAGE);
							}
						});
						return false;
					}
					serial = new SerialPurejavacomm(strPort);
					flashSerial = new SerialPurejavacomm(strPort);
				} else {
					// Isn't EDT, so update GUI using invokeLater().
					SwingUtilities.invokeLater(new Runnable () {
						@Override
						public void run(){
							JOptionPane.showMessageDialog(view, "No type of connection Selected", Language.getMessages("msg_error"), JOptionPane.ERROR_MESSAGE);
						}
					});
					return false;					
				}
				
				
				// ensure files exists
				if(!(new File(flashPath).isDirectory()) || !(new File(flashPath+ File.separator + flashProgramName).isFile()))
				{
					// Isn't EDT, so update GUI using invokeLater().
					SwingUtilities.invokeLater(new Runnable () {
						@Override
						public void run(){
							//TODO sistemare la stringa della finestra
							JOptionPane.showMessageDialog(view, "Missing file: " +flashPath+ File.separator +flashParamName, Language.getMessages("msg_error"), JOptionPane.ERROR_MESSAGE);
						}
					});
					return false;
				}
				
				// Download logger
				try
				{
					if (isK18)
						new GHP10Logger().logSingleSample(serial, modbusAddres,	language, logPath, tempPath, debugLog);
					
					if (isNextG)
						new GEN10Logger().logSingleSample(serial, modbusAddres,	language, logPath, tempPath, debugLog);
				}
				catch(IOException excp)
				{
					final IOException e = excp;
					// Isn't EDT, so update GUI using invokeLater().
					SwingUtilities.invokeLater(new Runnable () {
						@Override
						public void run(){
							showErrorDialogWithExtraInfo(e);
						}
					});
					return false;
				}
				
				
				// Isn't EDT, so update GUI using invokeLater().
				SwingUtilities.invokeLater(new Runnable () {
					@Override
					public void run(){
						view.showCursorBusy(Language.getMessages(flashMessage) + "  ... " + Language.getMessages("msg_downloading"));
					}
				});				
				
				//set the callback
				OpLong notifyFileSize = new OpLong()
				{
					@Override
					public void op(long value)
					{
						final int fValue = (int) value;
						// Isn't EDT, so update GUI using invokeLater().
						SwingUtilities.invokeLater(new Runnable () {
							@Override
							public void run(){
								view.closeProgressBar();
								view.showProgressBar(fValue);
							}
						});
					}
				};

				OpLong notifySentChunk = new OpLong()
				{
					@Override
					public void op(long value)
					{
						final int fValue = (int) value;
						// Isn't EDT, so update GUI using invokeLater().
						SwingUtilities.invokeLater(new Runnable () {
							@Override
							public void run(){
								view.setProgressInBar(fValue);
							}
						});
					}
				};
				
				OpStr printVerbose = new OpStr() 
				{							
					@Override
					public void op(String value) 
					{
						System.err.println(value);	
					}
				};
				
				if (isK18)
				{	
					//Start the flashing
					ModbusGHP10 ghp10 = new ModbusGHP10(serial, modbusAddres);
				
					new GHP10SerialFlasher(notifyFileSize, notifySentChunk, printVerbose, debugLog)
							.startFlashing(ghp10, flashSerial, flashPath, flashParamName, flashProgramName);				
				}
				
				if (isNextG)
				{	
					//Start the flashing
					ModbusGEN10 gen10 = new ModbusGEN10(serial, modbusAddres);
				
					new GEN10SerialFlasher(notifyFileSize, notifySentChunk, printVerbose, debugLog)
							.startFlashing(gen10, flashSerial, flashPath, flashParamName, flashProgramName, false, commSpeed);				
				}

			}
			catch(IOException excp)
			{
				final IOException e = excp;
				// Isn't EDT, so update GUI using invokeLater().
				SwingUtilities.invokeLater(new Runnable () {
					@Override
					public void run(){
						showErrorDialogWithExtraInfo(e);
					}
				});
				return false;
			}
			finally
			{
				if(debugLog != null)
					debugLog.close();
			}			
			
			return true;
		}
		
		@Override
		//executed by EDT, when doInBackground ends
		protected void done() {
			boolean task_ended_correctly;		
			try {
				view.showCursor_not_Busy("");
				view.closeProgressBar();
				view.enableLoggingUserAction(false);
				
				//Show logging end message only if task ends correctly
				task_ended_correctly = get();				
				if(task_ended_correctly) {
					JOptionPane.showMessageDialog(view, Language.getMessages("ttl_flashed"), Language.getMessages("msg_info"), JOptionPane.INFORMATION_MESSAGE);				
				}
							
			} catch (InterruptedException e) {
				showErrorDialogWithExtraInfo(e);
			} catch (ExecutionException e) {
				showErrorDialogWithExtraInfo(e);
			}		
		}
		
	}
	

		
	private void writeTask() {
		
		if(backgroundTask.isDone())
			return;
		
		try {
			final int addres = Integer.valueOf(view.getModbusViewRef().getModbusRegisterAddres());
			final int value = Integer.valueOf(view.getModbusViewRef().getModbusRegisterValue());
			final boolean isK18 = settingsModel.isBoardK18();
			final boolean isDDC = settingsModel.isBoardDDC();
			final boolean isCCI = settingsModel.isBoardCCI();
			final boolean isNextG = settingsModel.isBoardNextG();
			final int registerType = view.getModbusViewRef().getSelectedRegisterType();
			
            new Thread("Modbus Command Write Thread") {                
				public void run() {
                	try
	            	{
                		// Isn't EDT, so update GUI using invokeLater().
                		SwingUtilities.invokeLater(new Runnable () {
                			@Override
                			public void run(){
                				view.setCursor(Cursor.getPredefinedCursor(Cursor.WAIT_CURSOR));
                			}
                		});
                		
                		if (isK18) {
                			mbGhp10.getModbusInstrument().syncWriteRegister(addres - 1, value);
                		} else if(isDDC) {
                			switch (registerType)
    	            		{                			
                				case RegisterComboBox.COIL:
    	            				mbDDC.getModbusInstrument().syncWriteBit(addres - 1, value);
    	            			break;
    	            			
    	            			case RegisterComboBox.HOLDING:
    	            				mbDDC.getModbusInstrument().syncWriteRegister(addres - 1, value);
    	            			break;
    	            		}
                		} else if(isCCI) {
                			switch (registerType)
    	            		{                			
                				case RegisterComboBox.COIL:
    	            				mbCCI.syncWriteBit(addres - 1, value);
    	            			break;
    	            			
    	            			case RegisterComboBox.HOLDING:
    	            				mbCCI.syncWriteRegister(addres - 1, value);
    	            			break;
    	            		}
                		} else if (isNextG) {
                			mbGen10.getModbusInstrument().syncWriteRegister(addres - 1, value);
                		}

	            		
                		// Isn't EDT, so update GUI using invokeLater().
	                    SwingUtilities.invokeLater(new Runnable() {
							public void run() {
	                        	view.setCursor(Cursor.getDefaultCursor());   
	                        }
	                    });
						
	            	}
	            	catch(IOException excp)
	            	{
	            		final IOException e = excp;
	            		
	            		// Isn't EDT, so update GUI using invokeLater().
						SwingUtilities.invokeLater(new Runnable () {
							@Override
							public void run(){
								view.setCursor(Cursor.getDefaultCursor());
								showErrorDialogWithExtraInfo(e);
							}
						});							            		
	            	}
   
                	
                }
            }.start();
		} catch (NumberFormatException ex) {
			JOptionPane.showMessageDialog(view,Language.getMessages("msg_decimal"), Language.getMessages("msg_warning"), JOptionPane.WARNING_MESSAGE);  	
		}		
	}
	
	private void readTask() {
		
		if(backgroundTask.isDone())
			return;
		
		try {
			final int addres = Integer.valueOf(view.getModbusViewRef().getModbusRegisterAddres());
			final boolean isK18 = settingsModel.isBoardK18();
			final boolean isDDC = settingsModel.isBoardDDC();
			final boolean isCCI = settingsModel.isBoardCCI();
			final boolean isNextG = settingsModel.isBoardNextG();
			final int registerType = view.getModbusViewRef().getSelectedRegisterType();

            new Thread("Modbus Command Read Thread") {                
				public void run() {
                	try
	            	{
                		// Isn't EDT, so update GUI using invokeLater().
                		SwingUtilities.invokeLater(new Runnable () {
                			@Override
                			public void run(){
                				view.setCursor(Cursor.getPredefinedCursor(Cursor.WAIT_CURSOR));
                			}
                		});
                		
                		int value;
                		if(isK18) {
                			
                			value = mbGhp10.getModbusInstrument().syncReadRegister(addres - 1);      
                			
                			if(view.getModbusViewRef().isSignedSlected())
                				value = ModbusInstrument.to16bitSigned(value);  
                			
                			// Isn't EDT, so report the result using invokeLater().
                			final int aValue = value;
    	                    SwingUtilities.invokeLater(new Runnable() {
								public void run() {
    	                        	view.getModbusViewRef().setModbusRegisterValue(Integer.toString(aValue));
    	                            view.setCursor(Cursor.getDefaultCursor());               			
    	                        }
    	                    });
                		} else if (isDDC) {
                			switch (registerType)
    	            		{
    							case RegisterComboBox.COIL:	
    								//System.out.println("coil ");
    								value = mbDDC.getModbusInstrument().syncReadBit(addres - 1, ModbusInstrument.COIL_STATUS_READ_FUNCTION);
        						break;
        						
    							case RegisterComboBox.DISCRETE:
    								//System.out.println("discrete ");
    								value = mbDDC.getModbusInstrument().syncReadBit(addres - 1, ModbusInstrument.DISC_INPUT_READ_FUNCTION);
    							break;
    							
    							case RegisterComboBox.HOLDING:
    								//System.out.println("holding ");
    								value = mbDDC.getModbusInstrument().syncReadRegister(addres - 1, ModbusInstrument.HOLDING_REGS_READ_FUNCTION);
        						break;
        						
    							case RegisterComboBox.INPUT:
    								//System.out.println("input ");
    								value = mbDDC.getModbusInstrument().syncReadRegister(addres - 1, ModbusInstrument.INPUT_REGS_READ_FUNCTION);
    							break;
    							
    							default:
    								value = 0;							
    	            		}
                			if(view.getModbusViewRef().isSignedSlected())
                				value = ModbusInstrument.to16bitSigned(value);  
                			
                			// Isn't EDT, so update GUI using invokeLater().
                			final int aValue = value;
    	                    SwingUtilities.invokeLater(new Runnable() {
								public void run() {
    	                        	view.getModbusViewRef().setModbusRegisterValue(Integer.toString(aValue));
    	                            view.setCursor(Cursor.getDefaultCursor());               			
    	                        }
    	                    });
                		} else if (isCCI) {
                			switch (registerType)
    	            		{
    							case RegisterComboBox.COIL:	
    								//System.out.println("coil ");
    								value = mbCCI.syncReadBit(addres - 1, ModbusInstrument.COIL_STATUS_READ_FUNCTION);
        						break;
        						
    							case RegisterComboBox.DISCRETE:
    								//System.out.println("discrete ");
    								value = mbCCI.syncReadBit(addres - 1, ModbusInstrument.DISC_INPUT_READ_FUNCTION);
    							break;
    							
    							case RegisterComboBox.HOLDING:
    								//System.out.println("holding ");
    								value = mbCCI.syncReadRegister(addres - 1, ModbusInstrument.HOLDING_REGS_READ_FUNCTION);
        						break;
        						
    							case RegisterComboBox.INPUT:
    								//System.out.println("input ");
    								value = mbCCI.syncReadRegister(addres - 1, ModbusInstrument.INPUT_REGS_READ_FUNCTION);
    							break;
    							
    							default:
    								value = 0;							
    	            		}
                			if(view.getModbusViewRef().isSignedSlected())
                				value = ModbusInstrument.to16bitSigned(value);  
                			
                			// Isn't EDT, so update GUI using invokeLater().
                			final int aValue = value;
    	                    SwingUtilities.invokeLater(new Runnable() {
								public void run() {
    	                        	view.getModbusViewRef().setModbusRegisterValue(Integer.toString(aValue));
    	                            view.setCursor(Cursor.getDefaultCursor());               			
    	                        }
    	                    });
                		} else if(isNextG) {
                    			
                			value = mbGen10.getModbusInstrument().syncReadRegister(addres - 1);      
                			
                			if(view.getModbusViewRef().isSignedSlected())
                				value = ModbusInstrument.to16bitSigned(value);  
                			
                			// Isn't EDT, so report the result using invokeLater().
                			final int aValue = value;
    	                    SwingUtilities.invokeLater(new Runnable() {
								public void run() {
    	                        	view.getModbusViewRef().setModbusRegisterValue(Integer.toString(aValue));
    	                            view.setCursor(Cursor.getDefaultCursor());               			
    	                        }
    	                    });
                		}

                		
                		// Isn't EDT, so update GUI using invokeLater().
	                    SwingUtilities.invokeLater(new Runnable() {
							public void run() {
	                            view.setCursor(Cursor.getDefaultCursor());               			
	                        }
	                    });
	            	}
	            	catch(IOException excp)
	            	{
	            		final IOException e = excp;	            		
	            		// Isn't EDT, so update GUI using invokeLater().
						SwingUtilities.invokeLater(new Runnable () {
							@Override
							public void run(){
								view.setCursor(Cursor.getDefaultCursor());
								showErrorDialogWithExtraInfo(e);
							}
						});							            		
	            	}
                }
            }.start();
		} catch (NumberFormatException ex) {
			JOptionPane.showMessageDialog(view,Language.getMessages("msg_decimal"), Language.getMessages("msg_warning"), JOptionPane.WARNING_MESSAGE);  	
		}		
	}
	
	private void resetTask() {
		
		if(backgroundTask.isDone())
			return;
		
		try {

			final String modId = view.getModbusViewRef().getSelectedModId();
			final boolean isK18 = settingsModel.isBoardK18();
			final boolean isDDC = settingsModel.isBoardDDC();
			final boolean isCCI = settingsModel.isBoardCCI();
			final boolean isNextG = settingsModel.isBoardNextG();
			
            new Thread("Reset Error Thread") {                
				public void run() {
                	try
	            	{
                		// Isn't EDT, so update GUI using invokeLater().
                		SwingUtilities.invokeLater(new Runnable () {
                			@Override
                			public void run(){
                				view.setCursor(Cursor.getPredefinedCursor(Cursor.WAIT_CURSOR));
                			}
                		});
                		
                		if (isK18) {
                			final int result = mbGhp10.resetErrors(null);
                			
                			if (result == 0) {
                				// Isn't EDT, so update GUI using invokeLater().
        	                    SwingUtilities.invokeLater(new Runnable() {
									public void run() {
        	                        	view.setCursor(Cursor.getDefaultCursor());   
        	                        }
        	                    });	                    			
                    		} else {
                    			// Isn't EDT, so update GUI using invokeLater().
        						SwingUtilities.invokeLater(new Runnable () {
        							@Override
        							public void run(){
        								view.setCursor(Cursor.getDefaultCursor());
        	                			JOptionPane.showMessageDialog(view, mbGhp10.resetErrorsFailReasonStrs[result], Language.getMessages("msg_error"), JOptionPane.ERROR_MESSAGE);
        							}
        						});		
                    		}
                		} else if(isDDC) {
                			int result = 0; //mbDDC.resetErrors(modId, null);
                			
                			mbDDC.resetErrors(modId, null);

    						do
    						{
    							try
    							{
    								Thread.sleep(250);
    							}
    							catch(InterruptedException excp)
    							{}

    							result = mbDDC.getResetErrorsResult(modId, null);
    						}
    						while ( result == ModbusDDC.resultType.RESET_IN_PROGRESS.ordinal() );

    						final int resultErr = ignoreResetResults(result);
                			
                			if (result == ModbusDDC.resultType.OK.ordinal()) {
                				// Isn't EDT, so update GUI using invokeLater().
        	                    SwingUtilities.invokeLater(new Runnable() {
									public void run() {
        	                        	view.setCursor(Cursor.getDefaultCursor());   
        	                        }
        	                    });                    			
                    		} else {
                    			// Isn't EDT, so update GUI using invokeLater().
        						SwingUtilities.invokeLater(new Runnable () {
        							@Override
        							public void run(){
        								view.setCursor(Cursor.getDefaultCursor());
        	                			JOptionPane.showMessageDialog(view, mbDDC.resetErrorsFailReasonStrs[resultErr], Language.getMessages("msg_error"), JOptionPane.ERROR_MESSAGE);
        							}
        						});	
                    		}
                		} else if(isCCI) {
                			final int result = mbCCI.resetErrors(modId, null);
                			
                			if (result == ModbusCCI.resultType.OK.ordinal()) {
                				// Isn't EDT, so update GUI using invokeLater().
        	                    SwingUtilities.invokeLater(new Runnable() {
									public void run() {
        	                        	view.setCursor(Cursor.getDefaultCursor());   
        	                        }
        	                    });                    			
                    		} else {
                    			// Isn't EDT, so update GUI using invokeLater().
        						SwingUtilities.invokeLater(new Runnable () {
        							@Override
        							public void run(){
        								view.setCursor(Cursor.getDefaultCursor());
        	                			JOptionPane.showMessageDialog(view, mbCCI.resetErrorsFailReasonStrs[result], Language.getMessages("msg_error"), JOptionPane.ERROR_MESSAGE);
        							}
        						});	
                    		}
                		} else if (isNextG) {
                			final int result = mbGen10.resetErrors(null);
                			
                			if (result == 0) {
                				// Isn't EDT, so update GUI using invokeLater().
        	                    SwingUtilities.invokeLater(new Runnable() {
									public void run() {
        	                        	view.setCursor(Cursor.getDefaultCursor());   
        	                        }
        	                    });	                    			
                    		} else {
                    			// Isn't EDT, so update GUI using invokeLater().
        						SwingUtilities.invokeLater(new Runnable () {
        							@Override
        							public void run(){
        								view.setCursor(Cursor.getDefaultCursor());
        	                			JOptionPane.showMessageDialog(view, mbGen10.resetErrorsFailReasonStrs[result], Language.getMessages("msg_error"), JOptionPane.ERROR_MESSAGE);
        							}
        						});		
                    		}
                		}
	            	}
	            	catch(Throwable excp)
	            	{
	            		final Throwable e = excp;
	            		
	            		// Isn't EDT, so update GUI using invokeLater().
						SwingUtilities.invokeLater(new Runnable () {
							@Override
							public void run(){
								view.setCursor(Cursor.getDefaultCursor());
								showErrorDialogWithExtraInfo(e);
							}
						});							            		
	            	}
   
                	
                }
            }.start();
		} catch (NumberFormatException ex) {
			JOptionPane.showMessageDialog(view,Language.getMessages("msg_decimal"), Language.getMessages("msg_warning"), JOptionPane.WARNING_MESSAGE);  	
		}		
	}
	
	private int ignoreResetResults(int result)
	{
		if( result == ModbusDDC.resultType.RESET_IN_PROGRESS.ordinal() )
			return result;
		else
		{
			// debugLog.write original result

			return ModbusDDC.resultType.OK.ordinal();
		}
	}
	
	public synchronized void disconnect() {
		comm.stopListening();
		exitService = true;		
		view.showGUI_not_Busy("");
		view.getConfigurationViewRef().setEnabled(false);
		view.getStatusViewRef().enableReboot(false);
		view.enableConfigurationUserAction(false);
	}
	
	public synchronized void reboot() {
		final String justRebootJSON = "{\"action\":\"doreboot\"}";
		
		try {
			
			comm.sendStringFile(justRebootJSON);
			
		} catch (IOException ex) {
			// Not in the finally block, since it has already been disconnected at the end of the main wile loop
			if(comm != null)
				comm.disconnect();
			
			final Throwable e = ex;
    		// Isn't EDT, so update GUI using invokeLater().
			SwingUtilities.invokeLater(new Runnable () {
				@Override
				public void run(){
					view.showGUI_not_Busy("");
					showErrorDialogWithExtraInfo(e);
				}
			});	
		}
	}
	
	private void connectTask() {		
	    new Thread("Connect Thread") {                
			public void run() {
	        		        	
	        	long minMillisToWaitBeforeError = 0;
	        	long startTime = System.currentTimeMillis();
	        	
	        	// Isn't EDT, so update GUI using invokeLater().
        		SwingUtilities.invokeLater(new Runnable () {
        			@Override
        			public void run(){
        				view.enableConfigurationUserAction(true);
        				view.getStatusViewRef().clearStatusView();
        				view.getStatusViewRef().enableReboot(false);
        				view.showGUIBusy(Language.getMessages("msg_connection"));
        			}
        		});
	        
		        try {
		        	
		        	//Open communication
		        	comm = new SocketRberryComm("169.254.1.1");
		        	minMillisToWaitBeforeError =  2 * 60 * 1000;		// allow minutes to the Raspeberry to reboot        	
		        	exitService = false;
		        	
	            	while(!exitService ) {
	            		
	            		//Try to connect to Roburberry
	            		try {
	    					comm.connect();
	    				}
	    				catch(IOException excp) {
	    					if ( (System.currentTimeMillis() - startTime) >  minMillisToWaitBeforeError)
	    						throw excp;
	    					try
	    					{
	    						//comm.stopListening();
	    						Thread.sleep(5000);
	    					}
	    					catch(InterruptedException ecxcp)
	    					{}
	
	    					continue;
	    				}  
	            		
	                    comm.sendStringFile(JSONUtil.getConfig);
	                    
	                    // Isn't EDT, so update GUI using invokeLater().
	                    SwingUtilities.invokeLater(new Runnable() {
							public void run() {
	                        	view.createTabPanelForConfiguration();	                        	
	                        }
	                    });
	                    
	                    //Start listening
	                    comm.startListening(
	        					new OpJSON()
	        					{
	        						@Override
	        						public void op(JSONObject value) throws IOException
	        						{
	    								
	        							if(value.has("action")) {	    									
	    									String action = value.getString("action");
	    									//System.out.println(value);
	
	    									if (action.equals("echoconfig")) {
	    										final JSONObject config = value.getJSONObject("config");	    										
	    										
	    										// Isn't EDT, so update GUI using invokeLater().
		        			                    SwingUtilities.invokeLater(new Runnable() {
													public void run() {
		        			                        	view.showGUI_not_Busy(Language.getMessages("msg_connected"));
		        			                        	view.getConfigurationViewRef().setConfiguration(config);
		        			                        	view.getConfigurationViewRef().setEnabled(true);
		        			                        }
		        			                    });
	
	    									}
	    									else if (action.equals("reboot")) {

	    										// Isn't EDT, so update GUI using invokeLater().
	    						        		SwingUtilities.invokeLater(new Runnable () {
	    						        			@Override
	    						        			public void run(){
	    						        				view.clearTabPanel();
	    						        				view.clearCloudConnected();
	    						        				view.getStatusViewRef().clearStatusView();
	    						        				view.updateWarning("");
	    						        				view.showGUIBusy(Language.getMessages("msg_reboot"));	    						        				
	    						        			}
	    						        		});

	    						        		// i.e. stop this communication session, NOT exit service but retry after a while
	    										comm.stopListening(); 

	    										// wait just a little while to allow ConfServer to close its socket end
	    										try
	    										{
	    											Thread.sleep(1000);
	    										}
	    										catch(InterruptedException ecxcp)
	    										{}
	    									}
	    									else if (action.equals("error"))
	    									{
	    										final JSONObject error = value;
	    										
 										
	    										// Isn't EDT, so update GUI using invokeLater().
		        			                    SwingUtilities.invokeLater(new Runnable() {
													public void run() { 
		        			                        	view.getStatusViewRef().enableReboot(true);
		        			                        	view.getStatusViewRef().setError(error);
		        			                        }
		        			                    });				

	    									}
	    									else if (action.equals("warning"))
	    									{
	    										final JSONObject warning = value;	    										
	    										
	    										// Isn't EDT, so update GUI using invokeLater().
		        			                    SwingUtilities.invokeLater(new Runnable() {
													public void run() {         
		        			                        	if(warning.has("type") && warning.getString("type").equals("JSerialCannotWriteXML"))
			    											view.updateWarning(Language.getMessages("msg_warning").toUpperCase() + ": " + Language.getMessages("rberry_usb_memory_suggestion"));
			    										else
			    											view.updateWarning(warning.toString());
		        			                        }
		        			                    });	
		        			                    

	    									}
	    									else if( action.equals("status") )
	    									{
	    										if( value.has("connected") )
	    										{
	    											//final JSONObject status = value;	
	    											final boolean connected = value.getString("connected").equals("true");
	    											
	    											// Isn't EDT, so update GUI using invokeLater().
			        			                    SwingUtilities.invokeLater(new Runnable() {
														public void run() {
			        			                        	if(connected)
			        			                        		view.setCloudConnected();
			        			                        	else
			        			                        		view.setCloudNOTConnected();
			        			                        }
			        			                    });	             


	    										}

	    									}
	    									
	    								}   
	        								
	        						}
	        					},
	        					new OpJSON()
	        					{
	        						@Override
	        						public void op(JSONObject value) throws IOException
	        						{
	        							final JSONObject sample = value;

	        							// Isn't EDT, so update GUI using invokeLater().
        			                    SwingUtilities.invokeLater(new Runnable() {
											public void run() {                       	
        			                        		view.getStatusViewRef().setSample(sample);
        			                        }
        			                    });	
	        						}
	        					}
	        				);
	                    	                    
	            		comm.disconnect();
	    				startTime = System.currentTimeMillis();	            		
	   
	            	} // END while ! exitService
	            	
	            	// Isn't EDT, so update GUI using invokeLater().
                    SwingUtilities.invokeLater(new Runnable() {
						public void run() {
                        	view.clearCloudConnected();
                        	view.updateWarning("");
                        	view.showGUI_not_Busy(Language.getMessages("msg_disconnected"));
                        }
                    });	
	            	
		        }
				catch(Exception excp) {
					
					// Not in the finally block, since it has already been disconnected at the end of the main wile loop
					if(comm != null)
						comm.disconnect();
					
					final Throwable e = excp;
            		// Isn't EDT, so update GUI using invokeLater().
					SwingUtilities.invokeLater(new Runnable () {
						@Override
						public void run(){
							view.showGUI_not_Busy("");
							showErrorDialogWithExtraInfo(e);
						}
					});	
					
					
				}		     
		        
		        
	        }        
        }.start();	
	    
	}
	
	private void sendJSONTask() {
		 new Thread("Send Thread") {                
				public void run() {
					
					try {
						JSONObject actConfig = view.getConfigurationViewRef().getConfiguration();
						
						if (actConfig == null)
							return;
						else
			            {
			                JSONObject json = new JSONObject();
			                try
			                {
			                    json.put("action", "setconfig");
			                    json.put("config", actConfig);
			                }
			                catch(JSONException e)
			                {}
			                
			                comm.sendJSONFile(json);			                
			                
			            }
						
					} catch (IOException excp) {

						final Throwable e = excp;
	            		// Isn't EDT, so update GUI using invokeLater().
						SwingUtilities.invokeLater(new Runnable () {
							@Override
							public void run(){
								view.showGUI_not_Busy("");
								showErrorDialogWithExtraInfo(e);
								view.enableConfigurationUserAction(false);
							}
						});	
					}
		        }        
	        }.start();
	}
	
	// This task share the diagnostic info of the system
	private void shareDiagnostic() {		
		SwingWorker<Boolean, Void> workerShareDiagnostic = new SwingWorker<Boolean, Void>() {
			final String logPath = settingsModel.getLogPath();
			final String tempPath  = logPath + File.separator + ".." + File.separator + ".itroburtemp";
			final String flashPath = settingsModel.getFlashPath();
			String fileToMove = null;
			String file = null;
			//Custom button text
    		Object[] options = {Language.getMessages("button_copy_path"),
    		                    Language.getMessages("button_show_in_folder"),
    		                    Language.getMessages("annulla")};
	        @Override
	        protected Boolean doInBackground() throws Exception {
	        	
	        	// Isn't EDT, so update GUI using invokeLater().
				SwingUtilities.invokeLater(new Runnable () {
					@Override
					public void run(){
						view.showCursorBusy();
					}
				});

	        	LogFilesUtil converter = new LogFilesUtil(new LogConvResolverPC(ApplicationPreference.XML_PATH));
				
				//Collect all the info about the system, java VM etc.
				Properties systemProperties = System.getProperties();
				SortedMap sortedSystemProperties = new TreeMap(systemProperties);
				Set<String> keys = sortedSystemProperties.keySet();
				String otherInfo = "";
				for (String key : keys) {
					otherInfo += "[" + key + "] = " + systemProperties.get(key) + "\n";
				}
				
				//ensure tempPath exist
				new File(tempPath).mkdirs();
				fileToMove = converter.zipLastAppLogs("diagnostics", new File(tempPath), "PC", Manifest.versionName, otherInfo, new File(flashPath)); 					
				file = SharerPC.moveFileToMail(new File(".tomail"), new File(fileToMove));
	        	return true;
	        }
	        @Override
	        //executed by EDT, when doInBackground ends
	        protected void done() {
	        	boolean task_ended_correctly;	
	        	try { 	        		
	        		view.showCursor_not_Busy();
	        		task_ended_correctly = get();
	        		if (task_ended_correctly) {
		        		int choice = JOptionPane.showOptionDialog(view, "File: " + file, Language.getMessages("button_share"),
		        		    JOptionPane.YES_NO_CANCEL_OPTION,
		        		    JOptionPane.QUESTION_MESSAGE,
		        		    null, options, options[2]);
		        		if (choice == 0 )
		        			Toolkit.getDefaultToolkit().getSystemClipboard().setContents(new StringSelection(file), null);
		        		else if (choice == 1)
		        			SharerPC.openParentFolderInExplorer(new File(file));
	        		}
        	
	            } catch (Exception e) { 
	            	showErrorDialogWithExtraInfo(e);  
	            }
	        }
		};
		workerShareDiagnostic.execute();
	}
	


	
}
