package it.robur.test.physicalJTAGGHP10;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.CoreMatchers.is;

import java.io.IOException;
import java.util.ArrayList;

import org.junit.After;
import org.junit.AfterClass;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;

import static org.junit.Assert.assertThat;
import static org.junit.Assert.assertArrayEquals;
import static org.junit.Assert.assertNull;
import static uk.co.datumedge.hamcrest.json.SameJSONAs.sameJSONAs;
import static uk.co.datumedge.hamcrest.json.SameJSONAs.sameJSONObjectAs;

import it.robur.flashers.SubprocessFlasher;
import it.robur.interfaces.OpLong;
import it.robur.interfaces.OpStrStr;


public class SubprocessFlasherTest 
{
	public static class InstrGHP10SubprocessFlasher extends SubprocessFlasher
	{
		public String errCode    = null;
		public String errDetails = null;
				
		long total = 0;
		ArrayList<Long> progresses = new ArrayList<Long>();
			
		public InstrGHP10SubprocessFlasher(String checkCommmandline, String flashCommmandline)
		{
			super("GHP10", 
					checkCommmandline, flashCommmandline,
					null, null, null,
					".itroburtemp");

			totalCallback = new OpLong()
			{
				public void op(long value) throws IOException
				{
					total = value;
				}
			};
			
			progressCallback = new OpLong()
			{
				public void op(long value) throws IOException
				{
					progresses.add(value);
				}
			};
			
			errorCallback = new OpStrStr()
			{
				public void op(String pErrCode, String pErrDetails) throws IOException
				{
					errCode    = pErrCode;
					errDetails = pErrDetails;
				}
			};

			
		}
		
		int[] getProgresses()
		{
			int[] p = new int[progresses.size()];
			
			for (int i = 0; i < progresses.size(); i++) 
				p[i] = progresses.get(i).intValue();
			
			return p;
		}
				
	}
	
	
	@BeforeClass
	public static void setupTest() throws IOException
	{	

	}
	
	@AfterClass
	public static void teardownTest() throws IOException
	{
		
	}
	
	@Before
	public void setup() throws IOException
	{			
	}	
	
	@After
	public void teardown() throws IOException
	{				
	}	
	
    @Test
    public void testMockFlash() throws IOException
    {
    	InstrGHP10SubprocessFlasher flasher = new InstrGHP10SubprocessFlasher("mockCheckTargetGHP10.bat", "mockFlash.bat");
    	
    	flasher.flash("cippa");
    	
    	assertThat(flasher.errCode, equalTo(null));
    	assertThat(flasher.errDetails, equalTo(null));
    	    	
    	assertThat(flasher.total, equalTo((long)22222));
    	assertArrayEquals(
        		flasher.getProgresses(),
        		new int[]{ 100, 1000, 10000, 22222});    	    	
    }
    
    @Test
    public void testMockFailTarget() throws IOException
    {
    	InstrGHP10SubprocessFlasher flasher = new InstrGHP10SubprocessFlasher("mockCheckTargetS61.bat", "mockFlash.bat");
    	
    	flasher.flash("cippa");
    	
    	assertThat(flasher.errCode, equalTo("err_not_target"));
    	assertThat(flasher.errDetails, equalTo("S61"));
    	    	
    }
    
    @Test
    public void testMockFlashErr() throws IOException
    {
    	InstrGHP10SubprocessFlasher flasher = new InstrGHP10SubprocessFlasher("mockCheckTargetGHP10.bat", "mockFlashErr.bat");
    	
    	flasher.flash("cippa");
    	
    	assertThat(flasher.errCode, equalTo("err_serial_not_rec"));
    	assertThat(flasher.errDetails, equalTo(null));
    	    	
    	assertThat(flasher.total, equalTo((long)22222));
    	assertArrayEquals(
        		flasher.getProgresses(),
        		new int[]{ 100, 1000 });    	    	
    }


    
}
