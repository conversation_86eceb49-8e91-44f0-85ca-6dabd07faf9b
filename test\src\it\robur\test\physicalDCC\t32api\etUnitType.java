/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 3.0.12
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package it.robur.test.physicalDCC.t32api;

public final class etUnitType {
  public final static etUnitType utACF = new etUnitType("utACF");
  public final static etUnitType utAY = new etUnitType("utAY");
  public final static etUnitType utAYF2 = new etUnitType("utAYF2");
  public final static etUnitType utAYF4 = new etUnitType("utAYF4");
  public final static etUnitType utGAHPW = new etUnitType("utGAHPW");
  public final static etUnitType utProntoclimaC = new etUnitType("utProntoclimaC");
  public final static etUnitType utProntoclimaCR = new etUnitType("utProntoclimaCR");
  public final static etUnitType utGAHPA = new etUnitType("utGAHPA");
  public final static etUnitType utGAHPAR = new etUnitType("utGAHPAR");
  public final static etUnitType utACFHR = new etUnitType("utACFHR");
  public final static etUnitType utGAHPW_Modulating = new etUnitType("utGAHPW_Modulating");
  public final static etUnitType utGAHPAR_Modulating = new etUnitType("utGAHPAR_Modulating");
  public final static etUnitType utGAHPA_Modulating = new etUnitType("utGAHPA_Modulating");
  public final static etUnitType utAY_Condensing = new etUnitType("utAY_Condensing");
  public final static etUnitType utNonRobur = new etUnitType("utNonRobur");
  public final static etUnitType utGAHPW_Modulating_HeatOnly = new etUnitType("utGAHPW_Modulating_HeatOnly");
  public final static etUnitType utGAHPA18Hyb_Modul = new etUnitType("utGAHPA18Hyb_Modul");
  public final static etUnitType utMaxUnitType = new etUnitType("utMaxUnitType");
  public final static etUnitType utVoid = new etUnitType("utVoid", 0xFF);

  public final int swigValue() {
    return swigValue;
  }

  public String toString() {
    return swigName;
  }

  public static etUnitType swigToEnum(int swigValue) {
    if (swigValue < swigValues.length && swigValue >= 0 && swigValues[swigValue].swigValue == swigValue)
      return swigValues[swigValue];
    for (int i = 0; i < swigValues.length; i++)
      if (swigValues[i].swigValue == swigValue)
        return swigValues[i];
    throw new IllegalArgumentException("No enum " + etUnitType.class + " with value " + swigValue);
  }

  private etUnitType(String swigName) {
    this.swigName = swigName;
    this.swigValue = swigNext++;
  }

  private etUnitType(String swigName, int swigValue) {
    this.swigName = swigName;
    this.swigValue = swigValue;
    swigNext = swigValue+1;
  }

  private etUnitType(String swigName, etUnitType swigEnum) {
    this.swigName = swigName;
    this.swigValue = swigEnum.swigValue;
    swigNext = this.swigValue+1;
  }

  private static etUnitType[] swigValues = { utACF, utAY, utAYF2, utAYF4, utGAHPW, utProntoclimaC, utProntoclimaCR, utGAHPA, utGAHPAR, utACFHR, utGAHPW_Modulating, utGAHPAR_Modulating, utGAHPA_Modulating, utAY_Condensing, utNonRobur, utGAHPW_Modulating_HeatOnly, utGAHPA18Hyb_Modul, utMaxUnitType, utVoid };
  private static int swigNext = 0;
  private final int swigValue;
  private final String swigName;
}

