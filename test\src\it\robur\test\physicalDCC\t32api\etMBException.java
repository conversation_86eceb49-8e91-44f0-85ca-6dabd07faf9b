/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 3.0.12
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package it.robur.test.physicalDCC.t32api;

public final class etMBException {
  public final static etMBException mbeNone = new etMBException("mbeNone", 0);
  public final static etMBException mbeIllegalFunction = new etMBException("mbeIllegalFunction", 1);
  public final static etMBException mbeIllegalDataAddress = new etMBException("mbeIllegalDataAddress", 2);
  public final static etMBException mbeIllegalDataValue = new etMBException("mbeIllegalDataValue", 3);
  public final static etMBException mbeSlaveDeviceFailure = new etMBException("mbeSlaveDeviceFailure", 4);

  public final int swigValue() {
    return swigValue;
  }

  public String toString() {
    return swigName;
  }

  public static etMBException swigToEnum(int swigValue) {
    if (swigValue < swigValues.length && swigValue >= 0 && swigValues[swigValue].swigValue == swigValue)
      return swigValues[swigValue];
    for (int i = 0; i < swigValues.length; i++)
      if (swigValues[i].swigValue == swigValue)
        return swigValues[i];
    throw new IllegalArgumentException("No enum " + etMBException.class + " with value " + swigValue);
  }

  private etMBException(String swigName) {
    this.swigName = swigName;
    this.swigValue = swigNext++;
  }

  private etMBException(String swigName, int swigValue) {
    this.swigName = swigName;
    this.swigValue = swigValue;
    swigNext = swigValue+1;
  }

  private etMBException(String swigName, etMBException swigEnum) {
    this.swigName = swigName;
    this.swigValue = swigEnum.swigValue;
    swigNext = this.swigValue+1;
  }

  private static etMBException[] swigValues = { mbeNone, mbeIllegalFunction, mbeIllegalDataAddress, mbeIllegalDataValue, mbeSlaveDeviceFailure };
  private static int swigNext = 0;
  private final int swigValue;
  private final String swigName;
}

