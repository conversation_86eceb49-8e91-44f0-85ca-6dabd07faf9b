/* Wiki styling */


article .centered {
  margin: 5px;
  text-align: center;
}

.strike {
  text-decoration: line-through;
}

.italic {
  font-style:italic;
}

.code {
  font-family: courier, 'Lucida Sans Typewriter', monospace;
  color: #004000;
  font-weight: bold;
}

.meta {
  color: #BF8660;
  font-style: italic;
}

.note {
  font-size: 85%;
}

.fitnesse {
    font-style: italic;
}

/** Collapsible section */
.collapsible {
  background: #F9F9F9;
  margin: 5px 0px 5px 0px;
  padding: 2px 2px 2px 2px;
  border: 1px dotted #909090;
}

.collapsible {
  border-color: #BF8660;
}

.collapsible > div {
    margin: 16px 0 0 16px;
}

.collapsible.closed > div {
  display: none;
}

.collapsible.invisible {
  display: none;
}

.collapsible p.title {
  color: #BF8660;
  font-style: italic;
  margin: 0;
  padding: 0 0 0 16px;
}

.collapsible p.title {
  background: url(../images/collapsibleOpen.png) no-repeat scroll 0 2px transparent;
}

.collapsible.closed p.title {
  background: url(../images/collapsibleClosed.png) no-repeat scroll 0 2px transparent;
}

.collapsible > ul {
  float: right;
  margin: 0;
}

.collapsible > ul li {
  display: inline;
  padding: 0 .5em;
  border-left: 1px solid #BF8660;
}

table {
	border: 1px solid #EEEEEE;
	border-collapse:collapse;
}

/* Table coloring */
tr.slimRowTitle {
	background-color: #f5f5f5;
}
tr.slimRowColor0 {
	background-color: #F5FEFF;
}
tr.slimRowColor1 {
	background-color: #F5FFF7;
}
tr.slimRowColor2 {
	background-color: #F9F5FA;
}
tr.slimRowColor3 {
	background-color: #FDF9FB;
}
tr.slimRowColor4 {
	background-color: #FCFAF7;
}
tr.slimRowColor5 {
	background-color: #F7FCFA;
}
tr.slimRowColor6 {
	background-color: #F7F7F9;
}
tr.slimRowColor7 {
	background-color: #FFFAFA;
}
tr.slimRowColor8 {
	background-color: #FBF9FF;
}
tr.slimRowColor9 {
	background-color: #FDFCF9;
}

td, th {
	padding: 0.25em 0.5em;
}
table.plain_text_table {
  border-top: none;
  border-right: none;
  border-bottom: none;
  border-left: 2px solid #e5e5e5;
  margin-left: 1em;
}
table.plain_text_table td,
table.plain_text_table th {
  border: none;
}

/** Test output */
.error,
span.error * {
  background-color: #ffffaa;
}
.pass,
span.pass * {
  background-color: #AAFFAA;
}
.fail,
span.fail * {
  background-color: #FFAAAA;
}
.diff,
span.diff * {
  background-color: #FF6666;
}
.ignore,
span.ignore * {
  background-color: lightgray;
}

/** !style_* */
.caps {
  font-variant: small-caps;
}
.red {
  color: red;
}
.orange {
  color: orange;
}
.yellow {
  color: yellow;
}
.green {
  color: green;
}
.blue {
  color: blue;
}
.indigo {
  color: indigo;
}
.violet {
  color: violet;
}
.pink {
  color: pink;
}
.lightYellow {
  color: lightYellow;
}
.lightGreen {
  color: lightGreen;
}
.lightBlue {
  color: lightBlue;
}
.chocolate {
  color: chocolate;
}
.darkOrange {
  color: darkOrange;
}
.darkRed {
  color: darkRed;
}
.olive {
  color: olive;
}
.darkBlue {
  color: darkBlue;
}
.darkGreen {
  color: darkGreen;
}
.right {
  float: right;
}
.left {
  float: left;
}
.pageHelp {
  font-size: 0.7em;
  font-style: italic;
  margin-left: 1.5em;
}
.hidden {
  display: none;
}
.fit_stacktrace {
  font-size: 0.7em;
}
.fit_label {
  font-style: italic;
  color: #C08080;
}
.fit_grey {
  color: #808080;
}

.fit_extension {border: solid 1px grey;}
.fit_table {border: solid 1px grey; border-collapse: collapse; margin: 2px 0px;}
table.fit_table tr td {border: solid 1px grey; padding: 2px 2px 2px 2px;}
.fit_interpreter {font-style: italic; color: #808020;}
.fit_keyword {color: #1010A0;}
.fit_member {color: #208080;}
.fit_SUT {color: #808020;}

/* Diff styling */

table.diff_table tr td {border: 0px; padding: 0px 0px 0px 0px;}

.diff_table {
  margin: 0px;
  padding: 0px;
  border: 1px;
  border-spacing: 0px;
  font-family: monospace;
  font-size: 125%;
}

.diff_line {
  padding-bottom: 0px;
  padding-top: 0px;
  white-space: pre;
  background: #D6D0D2;
}

.diff_chunk_header {
  background: #C0C0C0;
  font-weight: bold;
}

.diff_line_added {
  background: #B3FCC1;
  color: #000000;
  font-weight: bold;
}

.diff_line_removed {
  background: #FFCBDB;
  color: #CC5478;
  font-weight: bold;
}
