@echo off
setlocal enabledelayedexpansion

echo ========================================
echo    Compilazione avanzata jserialtool
echo ========================================

REM Pulisce build precedenti
if exist "build" (
    echo Pulizia build precedente...
    rmdir /S /Q build
)

REM Crea directory per i file compilati
mkdir build
mkdir build\classes

REM Costruisce il classpath con tutte le librerie
echo Costruzione classpath...
set CLASSPATH=.
for %%i in (libs\*.jar) do (
    set CLASSPATH=!CLASSPATH!;%%i
)
echo Classpath: %CLASSPATH%

REM Trova tutti i file .java (src E test)
echo Ricerca file sorgente...
dir /s /b src\*.java > build\sources.txt
dir /s /b test\*.java >> build\sources.txt

REM Conta i file
for /f %%A in ('type build\sources.txt ^| find /c /v ""') do set count=%%A
echo Trovati %count% file sorgente

REM Compila tutti i file Java (src + test)
echo.
echo Compilazione in corso...
javac -cp "%CLASSPATH%" -d build\classes -sourcepath "src;test" @build\sources.txt

if %ERRORLEVEL% neq 0 (
    echo.
    echo ========================================
    echo    ERRORE: Compilazione fallita!
    echo ========================================
    echo Controlla gli errori sopra riportati.
    pause
    exit /b 1
)

REM Copia le risorse se esistono
if exist "res" (
    echo Copia risorse...
    xcopy /S /I /E res build\classes\res > nul
)

REM Crea il manifest per il JAR
echo Creazione manifest...
echo Main-Class: it.robur.jserialtool.JSerialTool > build\manifest.txt
echo Class-Path: %CLASSPATH% >> build\manifest.txt

REM Crea il JAR con manifest
echo Creazione JAR...
cd build\classes
jar cfm ..\jserialtool-lib.jar ..\manifest.txt *
cd ..\..

REM Crea anche un JAR eseguibile con tutte le dipendenze
echo Creazione JAR eseguibile con dipendenze...
mkdir build\temp
cd build\temp

REM Estrae tutte le librerie
for %%i in (..\..\libs\*.jar) do (
    echo Estrazione %%i...
    jar xf %%i
)

REM Copia le classi compilate
xcopy /S /I /E ..\classes\* . > nul

REM Crea JAR finale
jar cfm ..\jserialtool-complete.jar ..\manifest.txt *
cd ..\..

REM Pulizia
rmdir /S /Q build\temp

echo.
echo ========================================
echo    Compilazione completata con successo!
echo ========================================
echo JAR libreria: build\jserialtool-lib.jar
echo JAR completo: build\jserialtool-complete.jar
echo.
pause
