@echo off
echo Compilazione jserialtool...

REM Crea directory per i file compilati
if not exist "build" mkdir build
if not exist "build\classes" mkdir build\classes

REM Costruisce il classpath con tutte le librerie
set CLASSPATH=
for %%i in (libs\*.jar) do call :addcp %%i
goto :continue

:addcp
set CLASSPATH=%CLASSPATH%;%1
goto :eof

:continue
echo Classpath: %CLASSPATH%

REM Compila tutti i file Java
echo Compilazione in corso...
javac -cp "%CLASSPATH%" -d build\classes -sourcepath src src\it\robur\jserialtool\*.java src\it\robur\*\*.java src\it\robur\*\*\*.java

if %ERRORLEVEL% neq 0 (
    echo ERRORE: Compilazione fallita!
    pause
    exit /b 1
)

REM Copia le risorse
echo Copia risorse...
if exist "res" xcopy /S /I /E res build\classes\res

REM Crea il JAR
echo Creazione JAR...
cd build\classes
jar cf ..\jserialtool-lib.jar *
cd ..\..

echo Compilazione completata! JAR creato in: build\jserialtool-lib.jar
pause
