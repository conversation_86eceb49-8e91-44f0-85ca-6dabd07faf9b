/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 3.0.12
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package it.robur.test.physicalDCC.t32api;

public final class etParamState {
  public final static etParamState psNone = new etParamState("psNone");
  public final static etParamState psReceiving = new etParamState("psReceiving");
  public final static etParamState psEditing = new etParamState("psEditing");
  public final static etParamState psInitSending = new etParamState("psInitSending");
  public final static etParamState psSending = new etParamState("psSending");
  public final static etParamState psAbortSend = new etParamState("psAbortSend");

  public final int swigValue() {
    return swigValue;
  }

  public String toString() {
    return swigName;
  }

  public static etParamState swigToEnum(int swigValue) {
    if (swigValue < swigValues.length && swigValue >= 0 && swigValues[swigValue].swigValue == swigValue)
      return swigValues[swigValue];
    for (int i = 0; i < swigValues.length; i++)
      if (swigValues[i].swigValue == swigValue)
        return swigValues[i];
    throw new IllegalArgumentException("No enum " + etParamState.class + " with value " + swigValue);
  }

  private etParamState(String swigName) {
    this.swigName = swigName;
    this.swigValue = swigNext++;
  }

  private etParamState(String swigName, int swigValue) {
    this.swigName = swigName;
    this.swigValue = swigValue;
    swigNext = swigValue+1;
  }

  private etParamState(String swigName, etParamState swigEnum) {
    this.swigName = swigName;
    this.swigValue = swigEnum.swigValue;
    swigNext = this.swigValue+1;
  }

  private static etParamState[] swigValues = { psNone, psReceiving, psEditing, psInitSending, psSending, psAbortSend };
  private static int swigNext = 0;
  private final int swigValue;
  private final String swigName;
}

