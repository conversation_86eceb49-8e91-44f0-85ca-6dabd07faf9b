package it.robur.test.simulableSerialMultiGEN10;

public class Settings 
{
	protected static final String winPort = "COM11";
	protected static final String uxPort = "/dev/ttyUSB3";
	
	public static final boolean rs232 = true; // false imply RS485
	
	public static String getPort()
	{
		if( System.getProperty("os.name").toLowerCase().startsWith("win") )
			return winPort;
		else
			return uxPort;
	}
		
}
