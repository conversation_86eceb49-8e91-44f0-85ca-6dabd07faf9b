/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 3.0.12
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package it.robur.test.physicalDCC.t32api;

public final class etChilHeatSwitchAction {
  public final static etChilHeatSwitchAction chsImplicitSet = new etChilHeatSwitchAction("chsImplicitSet");
  public final static etChilHeatSwitchAction chsExplicitSet = new etChilHeatSwitchAction("chsExplicitSet");
  public final static etChilHeatSwitchAction chsGetCandidate = new etChilHeatSwitchAction("chsGetCandidate");

  public final int swigValue() {
    return swigValue;
  }

  public String toString() {
    return swigName;
  }

  public static etChilHeatSwitchAction swigToEnum(int swigValue) {
    if (swigValue < swigValues.length && swigValue >= 0 && swigValues[swigValue].swigValue == swigValue)
      return swigValues[swigValue];
    for (int i = 0; i < swigValues.length; i++)
      if (swigValues[i].swigValue == swigValue)
        return swigValues[i];
    throw new IllegalArgumentException("No enum " + etChilHeatSwitchAction.class + " with value " + swigValue);
  }

  private etChilHeatSwitchAction(String swigName) {
    this.swigName = swigName;
    this.swigValue = swigNext++;
  }

  private etChilHeatSwitchAction(String swigName, int swigValue) {
    this.swigName = swigName;
    this.swigValue = swigValue;
    swigNext = swigValue+1;
  }

  private etChilHeatSwitchAction(String swigName, etChilHeatSwitchAction swigEnum) {
    this.swigName = swigName;
    this.swigValue = swigEnum.swigValue;
    swigNext = this.swigValue+1;
  }

  private static etChilHeatSwitchAction[] swigValues = { chsImplicitSet, chsExplicitSet, chsGetCandidate };
  private static int swigNext = 0;
  private final int swigValue;
  private final String swigName;
}

