!3 We should be able to move a page from one location to another.

First build a page, a subpage to move, and a target page to move it to.

|Page creator.|
|Page name.          |Page contents.|valid?|
|!-ParentPage-!        |x           |true   |
|!-ParentPage.SubPage-!|sub page    |true   |
|!-NewParentPage-!     |x           |true   |

Then move that page.

|Response Requester.|
|uri   |status?|
|!-ParentPage.SubPage?responder=movePage&newLocation=NewParentPage-!||

Next fetch the moved page.

|Response Requester.|
|uri|valid?|contents?|
|!-NewParentPage.SubPage-!|true||

Make sure that the sub page can be referenced in it's new location.

|Response Examiner.|
|type  |pattern|matches?|wrapped html?|
|contents|sub page|true||

Make sure that there is no sub page beneath !-ParentPage-!.

!|Response Requester.|
|uri|valid?|contents?|
|ParentPage.SubPage?getPage&dontCreatePage|false||

