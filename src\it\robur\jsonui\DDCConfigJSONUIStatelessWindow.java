package it.robur.jsonui;

import org.json.JSONException;
import org.json.JSONObject;
import org.json.JSONException;

import java.io.IOException;

import it.robur.strings.Language;

public class DDCConfigJSONUIStatelessWindow implements JSONUIStatelessWindow {

    /**
     * @param langCode as 2-characters language code
     * @param resPath: path to res directory containing Robur strings, in xml
     * and values subdirectories
     * @param identif as received from <PERSON><PERSON><PERSON>
     * @param config as received from <PERSON><PERSON><PERSON>
     * @return JSON representing widgets' state, a "window"
     * @throws JSONException, IOException
     */
    @Override
    public JSONObject load(String langCode, String resPath, JSONObject identif, JSONObject config) throws JSONException, IOException {
        Language.instance(langCode, resPath);

        return new DDCConfigJSONUIWindow(identif, config).load(identif, config);
    }

    /**
     * @param langCode as 2-characters language code
     * @param resPath: path to res directory containing Robur strings, in xml
     * and values subdirectories
     * @param identif as received from <PERSON><PERSON><PERSON>
     * @param config as received from <PERSON><PERSON><PERSON>
     * @param prevWindow as returned by most recent previous call of load() or
     * update() itself
     * @param updatedWidget as just {"id":"...", "value":"..."}
     * @return JSON representing widgets' state, a "window"
     * @throws JSONException, IOException
     */
    @Override
    public JSONObject update(String langCode, String resPath, JSONObject identif, JSONObject config, JSONObject prevWindow, JSONObject updatedWidget) throws JSONException, IOException {
        Language.instance(langCode, resPath);

        DDCConfigJSONUIWindow configWindow = new DDCConfigJSONUIWindow(identif, config);

        configWindow.restore(identif, config, prevWindow);
        configWindow.update(updatedWidget);

        return configWindow.toJSON();
    }

    /**
     * @param langCode as 2-characters language code
     * @param resPath: path to res directory containing Robur strings, in xml
     * and values subdirectories
     * @param identif as received from Roburberry
     * @param config as received from Roburberry
     * @param window as returned by most recent previous call of load() or
     * update()
     * @return JSON to be sent to Roburberry
     * @throws JSONException, IOException
     */
    @Override
    public JSONObject save(String langCode, String resPath, JSONObject identif, JSONObject config, JSONObject window) throws JSONException, IOException {
        Language.instance(langCode, resPath);

        DDCConfigJSONUIWindow configWindow = new DDCConfigJSONUIWindow(identif, config);

        configWindow.restore(identif, config, window);

        return configWindow.save(identif, config);
    }

}
