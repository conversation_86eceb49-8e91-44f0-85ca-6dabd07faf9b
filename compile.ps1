Write-Host "========================================" -ForegroundColor Green
Write-Host "    Compilazione jserialtool" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

# Pulisce build precedenti
if (Test-Path "build") {
    Write-Host "Pulizia build precedente..." -ForegroundColor Yellow
    Remove-Item -Recurse -Force "build"
}

# Crea directory per i file compilati
New-Item -ItemType Directory -Path "build" -Force | Out-Null
New-Item -ItemType Directory -Path "build\classes" -Force | Out-Null

# Costruisce il classpath con tutte le librerie
Write-Host "Costruzione classpath..." -ForegroundColor Cyan
$classpath = "."
Get-ChildItem "libs\*.jar" | ForEach-Object {
    $classpath += ";$($_.FullName)"
}
Write-Host "Classpath: $classpath" -ForegroundColor Gray

# Trova tutti i file .java (src E test)
Write-Host "Ricerca file sorgente..." -ForegroundColor Cyan
$sourceFiles = @()
$sourceFiles += Get-ChildItem -Recurse "src\*.java" | Select-Object -ExpandProperty FullName
$sourceFiles += Get-ChildItem -Recurse "test\*.java" | Select-Object -ExpandProperty FullName

# Salva la lista dei file (senza BOM per javac)
$sourceFiles | Out-File -FilePath "build\sources.txt" -Encoding ASCII

Write-Host "Trovati $($sourceFiles.Count) file sorgente" -ForegroundColor Green

# Compila tutti i file Java
Write-Host ""
Write-Host "Compilazione in corso..." -ForegroundColor Yellow

$javacArgs = @(
    "-cp", $classpath,
    "-d", "build\classes",
    "-sourcepath", "src;test",
    "@build\sources.txt"
)

$process = Start-Process -FilePath "javac" -ArgumentList $javacArgs -Wait -PassThru -NoNewWindow

if ($process.ExitCode -ne 0) {
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Red
    Write-Host "    ERRORE: Compilazione fallita!" -ForegroundColor Red
    Write-Host "========================================" -ForegroundColor Red
    Write-Host "Controlla gli errori sopra riportati." -ForegroundColor Red
    Read-Host "Premi Enter per continuare"
    exit 1
}

# Copia le risorse se esistono
if (Test-Path "res") {
    Write-Host "Copia risorse..." -ForegroundColor Cyan
    Copy-Item -Recurse "res" "build\classes\" -Force
}

# Crea il manifest per il JAR
Write-Host "Creazione manifest..." -ForegroundColor Cyan
$manifest = @"
Main-Class: it.robur.jserialtool.JSerialTool
Class-Path: $classpath
"@
$manifest | Out-File -FilePath "build\manifest.txt" -Encoding UTF8

# Crea il JAR con manifest
Write-Host "Creazione JAR..." -ForegroundColor Cyan
Push-Location "build\classes"
$jarArgs = @("cfm", "..\jserialtool-lib.jar", "..\manifest.txt", "*")
Start-Process -FilePath "jar" -ArgumentList $jarArgs -Wait -NoNewWindow
Pop-Location

# Crea anche un JAR eseguibile con tutte le dipendenze
Write-Host "Creazione JAR eseguibile con dipendenze..." -ForegroundColor Cyan
New-Item -ItemType Directory -Path "build\temp" -Force | Out-Null
Push-Location "build\temp"

# Estrae tutte le librerie
Get-ChildItem "..\..\libs\*.jar" | ForEach-Object {
    Write-Host "Estrazione $($_.Name)..." -ForegroundColor Gray
    $jarArgs = @("xf", $_.FullName)
    Start-Process -FilePath "jar" -ArgumentList $jarArgs -Wait -NoNewWindow
}

# Copia le classi compilate
Copy-Item -Recurse "..\classes\*" "." -Force

# Crea JAR finale
$jarArgs = @("cfm", "..\jserialtool-complete.jar", "..\manifest.txt", "*")
Start-Process -FilePath "jar" -ArgumentList $jarArgs -Wait -NoNewWindow
Pop-Location

# Pulizia
Remove-Item -Recurse -Force "build\temp"

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "    Compilazione completata con successo!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host "JAR libreria: build\jserialtool-lib.jar" -ForegroundColor Cyan
Write-Host "JAR completo: build\jserialtool-complete.jar" -ForegroundColor Cyan
Write-Host ""
Read-Host "Premi Enter per continuare"
