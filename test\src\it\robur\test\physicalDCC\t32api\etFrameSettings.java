/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 3.0.12
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package it.robur.test.physicalDCC.t32api;

public final class etFrameSettings {
  public final static etFrameSettings p_8E1 = new etFrameSettings("p_8E1");
  public final static etFrameSettings p_8O1 = new etFrameSettings("p_8O1");
  public final static etFrameSettings p_8N1 = new etFrameSettings("p_8N1");
  public final static etFrameSettings p_8N2 = new etFrameSettings("p_8N2");

  public final int swigValue() {
    return swigValue;
  }

  public String toString() {
    return swigName;
  }

  public static etFrameSettings swigToEnum(int swigValue) {
    if (swigValue < swigValues.length && swigValue >= 0 && swigValues[swigValue].swigValue == swigValue)
      return swigValues[swigValue];
    for (int i = 0; i < swigValues.length; i++)
      if (swigValues[i].swigValue == swigValue)
        return swigValues[i];
    throw new IllegalArgumentException("No enum " + etFrameSettings.class + " with value " + swigValue);
  }

  private etFrameSettings(String swigName) {
    this.swigName = swigName;
    this.swigValue = swigNext++;
  }

  private etFrameSettings(String swigName, int swigValue) {
    this.swigName = swigName;
    this.swigValue = swigValue;
    swigNext = swigValue+1;
  }

  private etFrameSettings(String swigName, etFrameSettings swigEnum) {
    this.swigName = swigName;
    this.swigValue = swigEnum.swigValue;
    swigNext = this.swigValue+1;
  }

  private static etFrameSettings[] swigValues = { p_8E1, p_8O1, p_8N1, p_8N2 };
  private static int swigNext = 0;
  private final int swigValue;
  private final String swigName;
}

